From cd0dff773582d9c106bf35556fda49cfdd9fd924 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 17 Sep 2025 15:10:18 +0800
Subject: [PATCH 1/3] docs: tevs: rename the yocto 5.2 files in NXP.

---
 .../_category_.json                                             | 2 +-
 .../imx8mm-tevs-camera-usage-guide.md                           | 2 +-
 .../imx93-tevs-camera-usage-guide.md                            | 2 +-
 3 files changed, 3 insertions(+), 3 deletions(-)
 rename docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/{yocto-5.2-scarthgap-release => yocto-5.2-walnascar-release}/_category_.json (75%)
 rename docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/{yocto-5.2-scarthgap-release => yocto-5.2-walnascar-release}/imx8mm-tevs-camera-usage-guide.md (99%)
 rename docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/{yocto-5.2-scarthgap-release => yocto-5.2-walnascar-release}/imx93-tevs-camera-usage-guide.md (99%)

diff --git a/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/_category_.json b/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/_category_.json
similarity index 75%
rename from docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/_category_.json
rename to docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/_category_.json
index 2667e2f..adf4f88 100644
--- a/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/_category_.json
+++ b/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/_category_.json
@@ -1,5 +1,5 @@
 {
-  "label": "Yocto 5.2 Scarthgap Release",
+  "label": "Yocto 5.2 Walnascar Release",
   "position": 1,
   "link": {
     "type": "generated-index",
diff --git a/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/imx8mm-tevs-camera-usage-guide.md b/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/imx8mm-tevs-camera-usage-guide.md
similarity index 99%
rename from docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/imx8mm-tevs-camera-usage-guide.md
rename to docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/imx8mm-tevs-camera-usage-guide.md
index 6fee5bb..17e2582 100644
--- a/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/imx8mm-tevs-camera-usage-guide.md
+++ b/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/imx8mm-tevs-camera-usage-guide.md
@@ -11,7 +11,7 @@ import TabItem from '@theme/TabItem';
 ## 🚀 Introduction
 
 This guide shows you how to get started using TechNexion camera modules on **EDM-WB-IMX8M-MINI
-** board. You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS). This article is based on the 🔗 [**Yocto 5.2 (Scarthgap) 2025Q3 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp52-2025q3.md) with Linux kernel version **6.12.20-2.0.0**.
+** board. You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS). This article is based on the 🔗 [**Yocto 5.2 (Walnascar) 2025Q3 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp52-2025q3.md) with Linux kernel version **6.12.20-2.0.0**.
 
 ---
 ## 📸 Supported Camera Modules
diff --git a/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/imx93-tevs-camera-usage-guide.md b/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/imx93-tevs-camera-usage-guide.md
similarity index 99%
rename from docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/imx93-tevs-camera-usage-guide.md
rename to docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/imx93-tevs-camera-usage-guide.md
index e53ce87..800832e 100644
--- a/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-scarthgap-release/imx93-tevs-camera-usage-guide.md
+++ b/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.2-walnascar-release/imx93-tevs-camera-usage-guide.md
@@ -10,7 +10,7 @@ import TabItem from '@theme/TabItem';
 
 ## 🚀 Introduction
 
-This guide shows you how to get started using TechNexion camera modules on **AXON-WB-IMX93** board. You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS). This article is based on the 🔗 [**Yocto 5.2 (Scarthgap) 2025Q3 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp52-2025q3.md) with Linux kernel version **6.12.20-2.0.0**.
+This guide shows you how to get started using TechNexion camera modules on **AXON-WB-IMX93** board. You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS). This article is based on the 🔗 [**Yocto 5.2 (Walnascar) 2025Q3 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp52-2025q3.md) with Linux kernel version **6.12.20-2.0.0**.
 
 ---
 ## 📸 Supported Camera Modules
-- 
2.43.0

