#!/usr/bin/env python3
import sys
import re

def main():
    import argparse
    parser = argparse.ArgumentParser(description='Convert d360 metadata to MDX frontmatter and clean markdown content')
    parser.add_argument('input', help='Input markdown file path')
    parser.add_argument('output', nargs='?', help='Optional output file path (defaults to input file)')
    args = parser.parse_args()
    
    input_path = args.input
    output_path = args.output if args.output else input_path
    
    input_path = sys.argv[1]
    if len(sys.argv) == 2:
        output_path = input_path
    else:
        output_path = sys.argv[2]
    
    with open(input_path, 'r') as infile:
        lines = infile.readlines()
    
    in_metadata = False
    frontmatter = []
    cleaned_lines = []
    keys = ["title", "description"]
    in_internal = False  # Track if we're inside an Internal admonition

    for line in lines:

        # Replace d360 metadata with mdx frontmatter, preserving certain keys
        if line.startswith('## Metadata_Start'):
            in_metadata = True
            frontmatter = ['---\n']
            continue
        elif line.startswith('## Metadata_End'):
            in_metadata = False
            frontmatter.append('---\n')
            cleaned_lines.extend(frontmatter)
            continue
        elif in_metadata:
            stripped_line = line.lstrip('## ')
            if ':' in stripped_line:
                key, value = stripped_line.split(':', 1)
                key = key.strip()
                value = value.strip()
                if key in keys:  # Make sure it contains the keys we want to keep
                    if key == "description" and value == "":
                        value = "Add document description here"
                    frontmatter.append(f'{key}: {value}\n')
            continue
        else:
            # Remove target="_blank" from links
            line = re.sub(r'\{target="_blank"\}', '', line)
            # Check if it's an image line
            if line.find('![') >= 0:
                # Remove any attributes after the image URL
                line = re.sub(r'\)\{[^}]*\}', ')', line)
                # Replace external URLs with local static image path
                line = re.sub(r'\(https://cdn\.[^)]+/([^)]+)\)', r'(//img/\1)', line)
            # Add MDX admonition syntax conversion
            if line.startswith(':::('):
                match = re.match(r'^:::\((\w+)\)\s*(.*)', line)
                if match:
                    admonition_type = match.group(1)
                    if admonition_type == 'Internal':
                        # Start of Internal admonition
                        # Convert to HTML comment
                        # This is a workaround for the fact that MDX doesn't support Internal admonitions
                        # and we want to keep the content but hide it from rendering
                        in_internal = True
                        line = f'<!-- {line}'
                    else:
                        line = re.sub(r'^:::\((\w+)\)\s*(.*)', lambda m: f':::{m.group(1).lower()}', line)
            if in_internal:
                if line.startswith(':::'):
                    # End of Internal admonition
                    # Convert to HTML comment
                    # This is a workaround for the fact that MDX doesn't support Internal admonitions
                    # and we want to keep the content but hide it from rendering
                    in_internal = False
                    line = f'{line} -->'
        
            # Convert linkbreaks to <br/>            
            line = re.sub(r'<br>', '<br/>', line)
            cleaned_lines.append(line)
        

    with open(output_path, 'w') as outfile:
        outfile.writelines(cleaned_lines)

if __name__ == "__main__":
    main()