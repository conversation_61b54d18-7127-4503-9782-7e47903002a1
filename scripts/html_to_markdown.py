#!/usr/bin/env python3

import sys
import argparse
import html2text
from pathlib import Path

def convert_html_to_md(input_path, output_path):
    with open(input_path, 'r', encoding='utf-8') as html_file:
        html_lines = html_file.read().splitlines()

    metadata = {}
    start_idx = None
    end_idx = None
    in_metadata = False
    for i, line in enumerate(html_lines):
        if '<!-- ## Metadata_Start' in line:
            start_idx = i
            in_metadata = True
        elif '## Metadata_End -->' in line and in_metadata:
            end_idx = i
            break
    if start_idx is not None and end_idx is not None:
        metadata_lines = html_lines[start_idx+1:end_idx]
        for line in metadata_lines:
            stripped = line.lstrip()
            if stripped.startswith('## '):
                key_val = stripped[3:].split(':', 1)
                key = key_val[0].strip()
                val = key_val[1].strip()
                metadata[key] = val
        del html_lines[start_idx:end_idx+1]
    html_content = '\n'.join(html_lines)

    converter = html2text.HTML2Text()
    converter.ignore_images = False  # Keep images
    converter.mark_code = True
    converter.body_width = 0        # Disable line wrapping

    md_content = converter.handle(html_content)
    md_content = md_content.replace("[code]", "```shell\n")
    md_content = md_content.replace("[/code]", "```\n")

    # Add frontmatter
    frontmatter = '---\n'
    if 'title' in metadata:
        frontmatter += f'title: "{metadata["title"]}"\n'
    if 'description' in metadata:
        frontmatter += f'description: "{metadata["description"]}"\n'
    frontmatter += '---\n'
    md_content = frontmatter + md_content

    with open(output_path, 'w', encoding='utf-8') as md_file:
        md_file.write(md_content)

def main():
    parser = argparse.ArgumentParser(description="Convert HTML to Markdown")
    parser.add_argument("input", help="Input HTML file path")
    parser.add_argument("-o", "--output", help="Output MD file path")
    
    args = parser.parse_args()
    
    input_path = args.input
    output_path = args.output if args.output else Path(args.input).with_suffix('.md')
    
    convert_html_to_md(input_path, output_path)
    print(f"Converted {input_path} to {output_path}")

if __name__ == "__main__":
    main()
