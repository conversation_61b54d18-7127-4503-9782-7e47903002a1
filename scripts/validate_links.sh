#!/bin/bash

if [ -f invalid_links.log ]; then
  rm invalid_links.log
fi
# Find all markdown files in the docs directory
find docs/ -type f -name "*.md" | while read -r file; do
  # Extract all links with the specific prefix
  grep -oE 'https://(ftp|download)\.technexion\.com[^"]*' "$file" | while read -r url; do
    # Clean up URLs
    url=$(echo "$url" | sed 's/).*//')
    url=$(echo "$url" | sed 's/ [^"]*$//')
    #url=$(echo "$url" | sed 's/\/$//') # Remove trailing slash for folder URLs
    url=$(echo "$url" | sed 's/).*//')
    url=$(echo "$url" | sed 's/ [^"]*$//')
    # Check if the URL is valid using curl with timeout
    response=$(curl -sI -m 5 "$url")
    status=$(echo "$response" | grep -i "HTTP/" | tail -n1 | awk '{print $2}')
    
    if [[ "$status" =~ ^[0-9]+$ ]] && [ "$status" -eq 200 ]; then
      echo "✅ Valid link: $url (Status: $status)"
    else
      echo "❌ Invalid link: $url" >> invalid_links.log
      echo "❌ $file" >> invalid_links.log
      echo >> invalid_links.log
    fi
  done
done

# Check if any invalid links were found
if grep -q "❌" invalid_links.log; then
  exit 1
fi
