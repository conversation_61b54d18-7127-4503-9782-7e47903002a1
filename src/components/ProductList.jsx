// 'use client';

import React, { useState, useEffect } from 'react';
import { TEVI_Series } from '@site/src/data/tevi_series';
import { TEVS_Series } from '@site/src/data/tevs_series';
import { VLS3_Series } from '@site/src/data/vls3_series';
import { VLS_GM2_Series } from '@site/src/data/vls_gm2_series';
import { VCI_Series } from '@site/src/data/vci_series';
import { VCS_Series } from '@site/src/data/vcs_series';
import { UVC_VLS3_Series } from '@site/src/data/uvc_vls3_series';

const seriesMap = {
  TEVI: TEVI_Series,
  TEVS: TEVS_Series,
  VLS3: VLS3_Series,
  VLS_GM2: VLS_GM2_Series,
  VCI: VCI_Series,
  VCS: VCS_Series,
  UVC_VLS3: UVC_VLS3_Series,
};

const ProductList = ({ series }) => {
  const products = seriesMap[series] || [];

  return (
    <div>
      {products.map((product) => (
        <React.Fragment key={product}>
          {product}
          <br />
        </React.Fragment>
      ))}
    </div>
  );
};

export default ProductList;