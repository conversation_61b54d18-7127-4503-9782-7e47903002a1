.features {
  display: flex;
  align-items: center;
  padding: 2rem 0;
  width: 100%;
}

.featureSvg {
  height: 200px;
  width: 200px;
}

.youtubeSection {
  padding: 2rem 0;
  margin-bottom: 2rem;
  background-color: #f5f5f5;
}

.youtubeContent {
  padding: 1rem;
}

.youtubeContent h2 {
  margin-bottom: 1rem;
}

.youtubeContent p {
  margin-bottom: 1rem;
}

.youtubeContainer {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 70%;
  margin: 0 auto;
}
