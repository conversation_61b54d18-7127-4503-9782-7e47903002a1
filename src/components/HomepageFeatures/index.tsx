import type {ReactNode} from 'react';
import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';

type FeatureItem = {
  title: string;
  Svg: React.ComponentType<React.ComponentProps<'svg'>>;
  description: ReactNode;
  href: string;
};

// YouTube section component
function YouTubeSection() {
  return (
    <section className={styles.youtubeSection}>
      <div className="container">
        <div className="row">
          <div className="col col--6">
            <div className={styles.youtubeContainer}>
              <iframe
                width="100%"
                height="220"
                src="https://www.youtube.com/embed/QH9bvBi2ktE"
                title="TechNexion Product Overview"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>
          </div>
          <div className="col col--6">
            <div className={styles.youtubeContent}>
              <Heading as="h2">TechNexion MIPI CSI-2 Cameras and Embedded Vision Development Kits at Embedded World 2025</Heading>
              <p>Discover TechNexion’s advanced MIPI CSI-2 camera solutions designed for embedded vision applications. Recorded live at Embedded World 2025,</p>
              <p>MIPI cameras integrated with powerful development kits for platforms like NXP i.MX8, NVIDIA Jetson, and more.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

const FeatureList: FeatureItem[] = [
  {
    title: 'System On Modules',
    Svg: require('@site/static/img/system_on_module.svg').default,
    description: (
      <>
        Our System on Modules are designed to get you into production quickly.
      </>
    ),
    href: '/docs/system-on-modules',
  },
  {
    title: 'Embedded Vision',
    Svg: require('@site/static/img/embedded_vision.svg').default,
    description: (
      <>
        We make a full line of MIPI, USB, GMSL2, and FPD-Link cameras, all supported by a comprehensive SDK.
      </>
    ),
    href: '/docs/embedded-vision',
  },
  {
    title: 'Embedded Software Development',
    Svg: require('@site/static/img/embedded_software_development.svg').default,
    description: (
      <>
        Go here for information regarding software development for our system on modules.
      </>
    ),
    href: '/docs/embedded-software',
  },
  {
    title: 'Vision Software Development',
    Svg: require('@site/static/img/vision_software_development.svg').default,
    description: (
      <>
        Go here for information regarding software development for our embedded vision products.
      </>
    ),
    href: '/docs/vision-software',
  },
];

function Feature({title, Svg, description, href}: FeatureItem) {
  return (
    <div className={clsx('col col--3')}>  {/* Change from col--4 to col--3 for 4 items */}
      <div className="text--center">
        <a href={href}>
          <Svg className={styles.featureSvg} role="img" />
        </a>
      </div>
      <div className="text--center padding-horiz--md">
        <Heading as="h3">{title}</Heading>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures(): ReactNode {
  return (
    <>
      <section className={styles.features}>
        <div className="container">
          <div className="row row--no-gutters">
            {FeatureList.map((props, idx) => (
              <Feature key={idx} {...props} />
            ))}
          </div>
        </div>
      </section>
      <YouTubeSection />
    </>
  );
}
