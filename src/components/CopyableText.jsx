import React, { useState, useEffect } from 'react';

function CopyableText({ fullText }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copyStatus, setCopyStatus] = useState('Copy');
  const [isClipboardSupported, setIsClipboardSupported] = useState(false);
  const textId = `copyable-text-${Math.random().toString(36).substring(7)}`;
  const textToCopy = fullText ? fullText.replace(/^u-boot=>\s*/, '') : '';

  useEffect(() => {
    setIsClipboardSupported(!!(navigator && navigator.clipboard && navigator.clipboard.writeText));
    if (!isClipboardSupported) {
      console.warn('Clipboard API is not supported in this browser.');
    }
  }, [isClipboardSupported]);

  const handleCopyClick = () => {
    if (!isClipboardSupported) {
      alert('Not support copy behavior in this browser.');
      return;
    }

    const textElement = document.getElementById(textId);
    if (textElement) {
      navigator.clipboard.writeText(textToCopy)
        .then(() => {
          setCopyStatus('\u2713'); // Copy success, show ok
          setTimeout(() => {
            setCopyStatus('Copy'); // delay and resume button
          }, 300); // 300ms
        })
        .catch(err => {
          console.error('Copy Fail: ', err);
          setCopyStatus('Failed');
          setTimeout(() => {
            setCopyStatus('Copy');
          }, 300);
        });
    }
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const buttonStyle = {
    fontSize: '0.7em',
    marginLeft: '0.5em',
    width: '30px',
    padding: '0',
    textAlign: 'center',
  };

  return (
    <div style={{display: 'flex' }}>
      <span
        style={{ cursor: 'pointer' }}
        onClick={toggleExpand}
      >
        {isExpanded ? '\u25BC' : '\u25B6'} {/* Down/Right arrow for toggle */}
      </span>
      {isExpanded && (
        <div>
          <span id={textId} style={{ fontSize: '0.8em', fontFamily: 'system-ui' }}>
            {fullText}
          </span>
          <button
            onClick={handleCopyClick}
            style={{ ...buttonStyle }}
            disabled={!isClipboardSupported}
          >
            {copyStatus}
          </button>
        </div>
      )}
    </div>
  );
}

export default CopyableText;