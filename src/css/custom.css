/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

/* You can override the default Infima variables here. */
:root {
  --ifm-font-family-base: 'Inter', system-ui, -apple-system, sans-serif;
  --ifm-color-primary: #003366;
  --ifm-color-primary-dark: #002952;
  --ifm-color-primary-darker: #002447;
  --ifm-color-primary-darkest: #001a33;
  --ifm-color-primary-light: #003d7a;
  --ifm-color-primary-lighter: #004285;
  --ifm-color-primary-lightest: #004c99;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
  --search-local-modal-width: 560px;
  --search-local-hit-height: 56px;
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Add these custom styles for the dropdown menu */
.navbar__item.dropdown {
  padding: 0;
}

.navbar__item.dropdown .navbar__link {
  display: flex;
  align-items: center;
  padding: var(--ifm-navbar-item-padding-vertical) var(--ifm-navbar-item-padding-horizontal);
}

/* Dropdown menu styling */
.dropdown__menu {
  min-width: 250px;
  padding: 0.5rem 0;
}

/* Separator styling */
.navbar-separator {
  font-weight: bold;
  color: var(--ifm-color-primary);
  padding: 0.75rem 1rem 0.25rem;
  margin-top: 0.5rem;
  border-bottom: 1px solid var(--ifm-color-primary-lighter);
  pointer-events: none;
}

/* First separator shouldn't have top margin */
.dropdown__menu .navbar-separator:first-child {
  margin-top: 0;
}

/* Menu items styling */
.dropdown__link {
  padding: 0.5rem 1rem;
}

/* Hover effect for menu items */
.dropdown__link:hover {
  background-color: var(--ifm-color-emphasis-100);
}

/* Optional: Customize search modal appearance */
.DocSearch-Modal {
  border-radius: 8px;
}

.DocSearch-Hit-title {
  font-weight: 600;
}

/* link format */
.markdown a {
  color: #3b3be2;
  text-decoration: underline;
}

.hidden {
  display: none !important;
}

/* release note table style */
.tn-table {
  font-size: 12px;
  max-height: 600px;
  overflow-x: auto;
  overflow: auto;
  display: flexbox;
  min-width: 100%;
  text-align: left;
  border: 0px;
}

.tn-table thead {
  position: sticky;
  top: 0;
  z-index: 1;
  white-space: nowrap;
  font-weight: bold;
  background-color: #fae197;
}

/* stick the first element */
.tn-table thead tr:first-child td:first-child {
  left: 0;
  z-index: 2;
}

.tn-table thead td {
  min-width: 150px;
  background-color: #facc97;
}

/* first column */
.tn-table td:first-child {
  position: sticky;
  left: 0;
  white-space: normal;
  min-width: 150px;
}

.tn-table tbody td:first-child {
  background-color: antiquewhite;
}

/* release note color style */
.tn-table-color thead tr:first-child td:nth-child(odd) {
  background-color: #f9c7f9;
}

.tn-table-color thead tr:first-child td:nth-child(even) {
  background-color: #96f8c8;
}

.tn-table-color thead tr:first-child td:first-child {
  background-color: #f9f801;
}

.tn-table-color tbody td:first-child {
  background-color: antiquewhite;
}

/* custom markdown table style */
.md-table {
  max-height: 600px;
  overflow-x: auto;
  overflow: auto;
  display: flex;
  min-width: 100%;
  text-align: left;
  border: 0px;
}

.md-table thead th {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #fae197;
  min-width: 150px;
}

.md-table thead th:first-child {
  left: 0;
  z-index: 2;
}

.md-table td:first-child {
  background-color: antiquewhite;
  position: sticky;
  left: 0;
}

.footer-link-github {
  position: relative;
  padding-left: 25px; /* space for icon */
}
.footer-link-youtube {
  position: relative;
  padding-left: 25px; /* space for icon */
}

.footer-link-technexion {
  position: relative;
  padding-left: 25px;
}

/* Common icon style (white background circle) */
.footer-link-github::before,
.footer-link-youtube::before,
.footer-link-technexion::before  {
  content: '';
  display: inline-block;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: white;           /* White background */
  border-radius: 50%;                /* Make it circular */
  background-repeat: no-repeat;
  background-position: center;
  background-size: 15px 15px;        /* Icon size inside the circle */
}

/* Specific icons */
.footer-link-github::before {
  background-image: url('/img/github-mark.svg');
}

.footer-link-youtube::before {
  background-image: url('/img/youtube_icon_2024.svg');
}

.footer-link-technexion::before {
  background-image: url('/img/tn_logo.svg');
}