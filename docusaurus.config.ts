import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

const config: Config = {
  title: 'TechNexion Developer Portal',
  tagline: 'Comprehensive documentation regarding software, hardware, and usage of TechNexion products',
  favicon: 'img/tn-favicon.jpg',

  // Set the production url of your site here
  url: 'https://your-docusaurus-site.example.com',
  // Set the /<baseUrl>/ pathname under which your site is served
  // For GitHub pages deployment, it is often '/<projectName>/'
  baseUrl: '/',

  // GitHub pages deployment config.
  // If you aren't using GitHub pages, you don't need these.
  organizationName: 'TechNexion', // Usually your GitHub org/user name.
  projectName: 'devportal', // Usually your repo name.

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-Hans".
  i18n: {
    defaultLocale: 'en',
    locales: ['en'],
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: './sidebars.js',
        },
        blog: {
          showReadingTime: true,
          feedOptions: {
            type: ['rss', 'atom'],
            xslt: true,
          },
          // Useful options to enforce blogging best practices
          onInlineTags: 'warn',
          onInlineAuthors: <AUTHORS>
          onUntruncatedBlogPosts: 'warn',
        },
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  // Add the themes configuration
  markdown: {
    mermaid: true,
  },
  themes: [
    '@docusaurus/theme-mermaid',
    [
      require.resolve('@easyops-cn/docusaurus-search-local'),
      {
        hashed: true,
        language: ["en"],
        indexDocs: true,
        indexBlog: true,
        indexPages: false,
        docsRouteBasePath: "/docs",
        blogRouteBasePath: "/blog",
        searchResultLimits: 8,
        searchBarPosition: "right",
        searchResultContextMaxLength: 50,
      },
    ],
  ],

  themeConfig: {
    // Replace with your project's social card
    image: 'img/docusaurus-social-card.jpg',
    colorMode: {
      defaultMode: 'light',
      disableSwitch: true,
      respectPrefersColorScheme: false,
    },
// announcementBar: {
    //   id: 'proof_of_concept',
    //   content:
    //     'This is a proof of concept for a new developer portal</a>',
    //   backgroundColor: '#fafbfc',
    //   textColor: '#091E42',
    //   isCloseable: true,
    // },
    docs: {
      versionPersistence: 'localStorage',
      sidebar: {
        hideable: true,
        autoCollapseCategories: true,
      },
    },
    navbar: {
      hideOnScroll: true,
      logo: {
        alt: 'My Site Logo',
        src: 'img/tn_logo.svg',
      },
      items: [
        // Add the search bar item at the beginning of the items array
        {
          type: 'search',
          position: 'right',
        },
        {
          "label": "System On Modules",
          "to": "/docs/system-on-modules",
          "position": "left",
          "items": [
            {
              type: 'html',
              value: '<div class="navbar-separator">EDM SoM Family</div>',
            },
            {
              "label": "EDM-G-IMX8M-PLUS",
              "to": "/docs/system-on-modules/edm/edm-g-imx8m-plus"
            },
            {
              "label": "EDM-G-IMX8M-MINI",
              "to": "/docs/system-on-modules/edm/edm-g-imx8m-mini"
            },
            {
              "label": "EDM-IMX95",
              "to": "/docs/system-on-modules/edm/edm-imx95"
            },
            {
              "label": "EDM-IMX93",
              "to": "/docs/system-on-modules/edm/edm-imx93"
            },
            {
              "label": "EDM-IMX91",
              "to": "/docs/system-on-modules/edm/edm-imx91"
            },
            {
              type: 'html',
              value: '<div class="navbar-separator">AXON SoM Family</div>',
            },
            {
              "label": "AXON-IMX8M-PLUS",
              "to": "/docs/system-on-modules/axon/axon-imx8m-plus"
            },
            {
              "label": "AXON-IMX93",
              "to": "/docs/system-on-modules/axon/axon-imx93"
            },
            {
              "label": "AXON-IMX91",
              "to": "/docs/system-on-modules/axon/axon-imx91"
            },
            {
              type: 'html',
              value: '<div class="navbar-separator">PICO SoM Family</div>',
            },
            {
              "label": "PICO-IMX8M-MINI",
              "to": "/docs/system-on-modules/pico/pico-imx8m-mini"
            },
            {
              "label": "PICO-IMX7",
              "to": "/docs/system-on-modules/pico/pico-imx7"
            },
            {
              "label": "PICO-IMX8MQ",
              "to": "/docs/system-on-modules/pico/pico-imx8m"
            },
            {
              "label": "PICO-IMX6",
              "to": "/docs/system-on-modules/pico/pico-imx6"
            },
            {
              "label": "PICO-IMX93",
              "to": "/docs/system-on-modules/pico/pico-imx93"
            },
            {
              "label": "PICO-IMX6UL/ULL",
              "to": "/docs/system-on-modules/pico/pico-imx6ul"
            },
          ]
        },
        {
          "label": "Embedded Software",
          "to": "/docs/embedded-software",
          "position": "left",
          "items": [
            {
              type: 'html',
              value: '<div class="navbar-separator">Linux</div>',
            },
            {
              "label": "Yocto",
              "to": "/docs/embedded-software/linux/yocto"
            },
            {
              "label": "Debian",
              "to": "/docs/embedded-software/linux/debian"
            },
            {
              "label": "Ubuntu",
              "to": "/docs/embedded-software/linux/ubuntu"
            },
            {
              "label": "NVIDIA JetPack",
              "to": "/docs/embedded-software/linux/nvidia-jetpack/"
            },
            {
              type: 'html',
              value: '<div class="navbar-separator">Android</div>',
            },
            {
              "label": "Android",
              "to": "/docs/embedded-software/android"
            },
            {
              type: 'html',
              value: '<div class="navbar-separator">Real Time OS</div>',
            },
            {
              "label": "FreeRTOS",
              "to": "/docs/embedded-software/rtos/freertos"
            }
          ]
        },
        {
          "label": "Embedded Vision",
          "to": "/docs/embedded-vision",
          "position": "left",
          "items": [
            {
              "label": "MIPI-CSI2 Cameras",
              "to": "/docs/embedded-vision/tevs"
            },
            {
              "label": "VizionCam USB3 Cameras",
              "to": "/docs/embedded-vision/vizioncam-usb3/usb-camera-user-guide"
            },
            {
              "label": "Vizionlink Cameras",
              "to": "/docs/embedded-vision/vizionlink"
            },
          ]
        },
        {
          "label": "Vision Software",
          "to": "/docs/vision-software",
          "position": "left",
          "items": [
            {
              "label": "VizionSDK",
              "to": "/docs/vision-software/vizionsdk"
            },
            {
              "label": "VizionViewer™",
              "to": "/docs/vision-software/vizionviewer"
            }
          ]
        }
      ],
    },
    footer: {
      style: 'dark',
      links: [
        {
          title: 'Docs',
          items: [
            {
              label: 'System on Modules',
              to: '/docs/system-on-modules',
            },
            {
              label: 'Embedded Software',
              to: '/docs/embedded-software',
            },
            {
              label: 'Embedded Vision',
              to: '/docs/embedded-vision',
            },
            {
              label: 'Vision Software',
              to: '/docs/vision-software',
            },
          ],
        },
        {
          title: 'Other Great Resources',
          items: [
            {
              label: 'TechNexion website',
              href: 'https://www.technexion.com',
              className: 'footer-link-technexion'
            },
            {
              label: 'YouTube',
              href: 'https://www.youtube.com/technexion',
              className: 'footer-link-youtube'
            },
            {
              label: 'GitHub',
              href: 'https://github.com/technexion',
              className: 'footer-link-github'
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} TechNexion Ltd.`,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
    },
  } satisfies Preset.ThemeConfig,

};

export default config;
