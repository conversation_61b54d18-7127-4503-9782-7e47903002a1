---
sidebar_position: 1
title: Vision Software
description: Vision Software Overview
---

Vision Software includes two key components: **VizionSDK** and the **VizionViewer™** application. VizionSDK is a cross-platform software development kit that facilitates integration, control, and application development to manage and optimize camera performance. VizionViewer™ is a cross-platform graphical application that enables video streaming and image capture through an intuitive interface, allowing users to monitor and control cameras without the need for programming knowledge.

## Vizion Software Key Feature

### VizionViewer™
- A user-friendly application for **real-time camera control** and **visualization**.
- Allows **adjustment of camera parameters** such as exposure, gain, and frame rate while viewing the live feed.

### VizionSDK
- A comprehensive software development kit for integrating and controlling camera modules programmatically.
- Provides a set of **APIs** and command-line utilities like **`vizion-ctl`** for configuring camera settings in various exposure modes.
- Enables the creation of custom applications that interact with camera hardware, making it a versatile tool for embedded vision development.

## Getting Started with Vision Software

To begin using the Vision Software, you can explore the following resources:

- [**VizionViewer™**](docs/vision-software/vizionviewer/vizionviewer.md)
- [**VizionSDK**](docs/vision-software/vizionsdk/vizionsdk.md)