---
sidebar_position: 2
title: Usage Guide
---

# Usage Guide

---

## Overview
- [Enumerate and list all video devices](#enumerate-and-list-all-video-devices)
- [Change streaming format](#change-streaming-format)
- [Display the current frame rate per second](#display-the-current-frame-rate-per-second)
- [Control extension settings (UVC/ISP modes)](#extension-settings-control)
- [Set AGC exposure mode to maintain constant framerate](#set-up-agc-exposure-mode-to-maintain-constant-framerate)
- [Limit Exposure Range in Auto Exposure Mode](#limit-exposure-range-in-auto-exposure-mode)
- [Update Technexion camera firmware](#update-technexion-camera-firmware)
- [Manage OS and On Sensor Profiles (OSP)](#manage-os-profiles)
- [Record videos and capture photos](#record-videos-and-capture-photos)
- [Simulate multiple cameras](#camera-simulation)
- [Customize layout with drag-and-drop](#customize-layout-with-drag-and-drop)
- [Change color theme](#change-color-theme)
- [Set custom logo](#set-custom-logo)
- [Access support resources](#support)

---

## Enumerate and List All Video Devices
When VizionViewer™ starts, it displays an initial screen showing all available video devices. You can select a camera either from this device list or from the dropdown menu. The device list refreshes every second.

![Camera Selection](//img/Camera_Selection.png)

---

## Change Streaming Format

### Color Space / Compression
After selecting a camera, the color space/compression dropdown displays all supported formats. Supported formats include **UYVY, YUY2, NV12, and MJPG**.

### Resolution
Once a color space/compression is chosen, the resolution dropdown lists all supported resolutions for the selected camera.

### Framerate
After selecting a resolution, the framerate dropdown displays all supported framerates.

---

## Display the Current Frame Rate Per Second
:::info
"Stream Frame Rate" refers to the camera's streaming output rate, not the rendering rate of VizionViewer™.
:::

![Frame Rate Display](//img/FrameRate.png)

---

## Extension Settings Control
:::info
Switching the Control Mode to UVC will reset ISP Extension settings to default. Switching to ISP mode will reset UVC Extension settings to default.
:::

![Extension Controls](//img/ExtensionControls.png)

### UVC Mode Extensions
Supported controls:
- White Balance (Auto / Manual)
- Exposure / Gain (Auto / Manual)
- Contrast
- Saturation
- Sharpness
- Gamma
- Brightness
- Hue
- Backlight
- Focus
- Iris
- Zoom
- Pan
- Tilt
- Roll

### ISP Mode Extensions
Supported controls:
- White Balance (Auto / Manual)
- Exposure / Gain (Auto / AGC / Manual)
- Contrast
- Saturation
- Sharpness
- Gamma
- Noise
- Brightness
- MJPG Quality

---

## Set Up AGC Exposure Mode to Maintain Constant Framerate
:::info Floating Framerate
For low-light applications, the camera increases exposure time in automatic exposure mode to maintain image quality.
:::

You can switch to **AGC exposure mode** to keep the framerate constant.

:::info AGC Mode
Auto gain control, manual exposure time.
:::

When AGC mode is enabled, gain is set automatically. The initial exposure value adjusts based on the current streaming framerate to maintain a constant framerate.

![AGC Mode](//img/AGC.png)

---

## Limit Exposure Range in Auto Exposure Mode
(Default Exposure Range) When switching to Auto Exposure Mode, the Exposure Time Range is automatically set to the default range based on the selected streaming format.

You can limit the exposure range even in Auto Exposure Mode. This is useful when you want to avoid overexposure or motion blur in specific lighting conditions.
The default exposure range varies depending on the selected resolution and framerate.

The current Exposure Time and Exposure Gain values are displayed after the camera starts streaming and are updated every second to reflect real-time changes.

![Exposure Range](//img/ExposureRange.png)

---

## Update Technexion Camera Firmware
If firmware updates are available for the selected camera, the **Update** button becomes enabled. You can:
- Update your camera's firmware directly
- Download firmware for later installation

![Firmware Update](//img/FirmwareManager_Update.png)
![Download for Later](//img/Download_Update_Later.png)

---

## Manage OS Profiles
Profiles allow you to save video stream and extension settings for your camera. You can create and name multiple profiles for different scenarios, making it easy to apply predefined configurations.

![OS Profile Management](//img/OS_Profile.png)

---

## Manage OSP (On Sensor Profile)
Store profiles directly on the sensor. The camera will automatically apply your preset profile upon startup.

![OSP Profile Settings](//img/OSP_Profile_Settings.png)

---

## Record Videos and Capture Photos

### Record Videos
:::info
On embedded platforms, the maximum recording resolution is 1920x1200.
:::
- Videos are saved in **H.264 encoding**. Hardware configuration may affect video framerate.
- Filenames follow the format: `yyyyMMdd_hh_mm_ss_zzz.avi`
- Default storage location: system default path (Tools → File → Open Video Folder)
- To change the storage location: Tools → File → Options

### Take Photos
- Filenames follow the format: `yyyyMMdd_hh_mm_ss_zzz.png`
- Default storage location: system default path (Tools → File → Open Image Folder)
- To change the storage location: Tools → File → Options

---

## Camera Simulation
- **View multiple cameras simultaneously:** Observe up to eight camera feeds at once.

![Camera Simulation](//img/Simulation.png)

---

## Customize Layout with Drag-and-Drop

### Layout Customization
Drag panel title bars to reposition interface elements.

![Drag Layout](//img/Drag_Layout.png)

---

## Change Color Theme

Select theme from Tools → Settings → Appearance (Ocean Blue/Dream Purple/Mountain Green).

![Color Theme](//img/Color_Theme.png)

---

## Set Custom Logo

### Logo Options
- **Default Logo:** TechNexion branding
- **Hide Logo:** No logo displayed
- **Custom Text Logo:** Enter custom text
- **Custom Image Logo:** Upload SVG image file

### Configuration
- Access: App Settings -> Customize Logo
- Select logo type
- For Custom Image Logo: Browse and select file
  - Supported formats: SVG
  - Recommended size: 206×26 pixels (SVG with transparent background)

![Custom Logo](//img/Custom_Logo.png)

---

## Support
If you encounter any issues with VizionViewer™:
1. Go to **Tools → Help → Report Issue**
2. Export the VizionViewer™ report
3. Attach the report file
4. Describe the issue
5. Send to [<EMAIL>](mailto:<EMAIL>)