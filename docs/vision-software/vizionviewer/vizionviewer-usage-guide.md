---
sidebar_position: 2
title: Usage Guide
---

# Usage Guide

---

## Overview
- [Enumerate and list all video devices](#enumerate-and-list-all-video-devices)
- [Change streaming format](#change-streaming-format)
- [Display the current frame rate per second](#display-the-current-frame-rate-per-second)
- [Control extension settings (UVC/ISP modes)](#extension-settings-control)
- [Set AGC exposure mode to maintain constant framerate](#set-up-agc-exposure-mode-to-maintain-constant-framerate)
- [Limit Exposure Range in Auto Exposure Mode](#limit-exposure-range-in-auto-exposure-mode)
- [Update Technexion camera firmware](#update-technexion-camera-firmware)
- [Manage OS and On Sensor Profiles (OSP)](#manage-os-profiles)
- [Record videos and capture photos](#record-videos-and-capture-photos)
- [Use Picture-in-Picture (PiP) mode](#picture-in-picture-pip-mode)
- [Simulate multiple cameras](#camera-simulation)
- [Focus Mode](#focus-mode)
- [Access support resources](#support)

---

## Enumerate and List All Video Devices
The device list refreshes every second. Select a camera from the dropdown menu to begin.

![Device List](//img/Enumerate_Device.png)

---

## Change Streaming Format

### Color Space / Compression
After selecting a camera, the color space/compression dropdown displays all supported formats. Supported formats include **UYVY, YUY2, NV12, and MJPG**.

### Resolution
Once a color space/compression is chosen, the resolution dropdown lists all supported resolutions for the selected camera.

### Framerate
After selecting a resolution, the framerate dropdown displays all supported framerates.

---

## Display the Current Frame Rate Per Second
:::info
"Stream Frame Rate" refers to the camera's streaming output rate, not the rendering rate of VizionViewer™.
:::

![Frame Rate Display](//img/FrameRate.png)

---

## Extension Settings Control
:::info
Switching the Control Mode to UVC will reset ISP Extension settings to default. Switching to ISP mode will reset UVC Extension settings to default.
:::

![Extension Controls](//img/ExtensionControls.png)

### UVC Mode Extensions
Supported controls:
- White Balance (Auto / Manual)
- Exposure / Gain (Auto / Manual)
- Contrast
- Saturation
- Sharpness
- Gamma
- Brightness
- Hue
- Backlight
- Focus
- Iris
- Zoom
- Pan
- Tilt
- Roll

### ISP Mode Extensions
Supported controls:
- White Balance (Auto / Manual)
- Exposure / Gain (Auto / AGC / Manual)
- Contrast
- Saturation
- Sharpness
- Gamma
- Noise
- Brightness
- MJPG Quality
- Filter
- Flick Mode
- Zoom
- Pan
- Tilt
- Flip

---

## Set Up AGC Exposure Mode to Maintain Constant Framerate
:::info Floating Framerate
For low-light applications, the camera increases exposure time in automatic exposure mode to maintain image quality.
:::

You can switch to **AGC exposure mode** to keep the framerate constant.

:::info AGC Mode
Auto gain control, manual exposure time.
:::

When AGC mode is enabled, gain is set automatically. The initial exposure value adjusts based on the current streaming framerate to maintain a constant framerate.

![AGC Mode](//img/AGC.png)

---

## Limit Exposure Range in Auto Exposure Mode
(Default Exposure Range) When switching to Auto Exposure Mode, the Exposure Time Range is automatically set to the default range based on the selected streaming format.

You can limit the exposure range even in Auto Exposure Mode. This is useful when you want to avoid overexposure or motion blur in specific lighting conditions.
The default exposure range varies depending on the selected resolution and framerate.

When hovering over the ± icon, the current Exposure Time and Exposure Gain values will be shown in a tooltip. These values are updated every second to reflect real-time changes.

![Exposure Range](//img/ExposureRange.png)

---

## Update Technexion Camera Firmware
If firmware updates are available for the selected camera, the **Update** button becomes enabled. You can:
- Update your camera's firmware directly
- Download firmware for later installation

![Firmware Update](//img/FirmwareManager_Update.png)
![Download for Later](//img/Download_Update_Later.png)

---

## Manage OS Profiles
Profiles allow you to save video stream and extension settings for your camera. You can create and name multiple profiles for different scenarios, making it easy to apply predefined configurations.

![OS Profile Management](//img/OS_Profile.png)

---

## Manage OSP (On Sensor Profile)
Store profiles directly on the sensor. The camera will automatically apply your preset profile upon startup.

![OSP Profile Settings](//img/OSP_Profile_Settings.png)

---

## Record Videos and Capture Photos

### Record Videos
:::info
On embedded platforms, the maximum recording resolution is 1920x1200.
:::
- Videos are saved in **H.264 encoding**. Hardware configuration may affect video framerate.
- Filenames follow the format: `yyyyMMdd_hh_mm_ss_zzz.avi`
- Default storage location: system default path (Tools → File → Open Video Folder)
- To change the storage location: Tools → File → Options

### Take Photos
- Filenames follow the format: `yyyyMMdd_hh_mm_ss_zzz.png`
- Default storage location: system default path (Tools → File → Open Image Folder)
- To change the storage location: Tools → File → Options

---

## Picture-in-Picture (PiP) Mode
PiP mode helps you focus on image composition.
- Double-click the screen or press **F11** in PiP mode to enter full-screen mode.
- Double-click the PiP window title bar to switch to windowed full-screen mode.

![Picture-in-Picture Mode](//img/Picture_In_Picture_Mode.png)

---

## Camera Simulation
- **View multiple cameras simultaneously:** Observe up to eight camera feeds at once.
- **Real-time frame rate alerts:** Receive instant alerts if the frame rate drops below 75% or 50% of the expected rate.

![Camera Simulation](//img/Simulation.png)

---

## Focus Mode

:::warning Impact on Performance
Enabling Focus Mode may affect the streaming framerate (FPS).
:::

Focus Mode provides tools to evaluate the sharpness of the image based on selected algorithms:

- **Tenengrad:** Captures horizontal and vertical edges, delivering stable focus evaluation for texture-rich images, with moderate sensitivity to noise and higher computational cost.
- **Laplacian:** Enables multi-directional edge detection and is well-suited for global focus assessment, though it is highly sensitive to noise and minor variations.
- **Brenner:** A fast, efficient method targeting horizontal edges, but limited in directional sensitivity and less effective in capturing fine details.

Once enabled, the streaming preview on the right will display:

- A Focus ROI (Region of Interest) overlay.
- The calculated Focus Value for the selected region.

You can adjust the ROI size using the ROI Size setting, and click directly on the streaming preview to reposition the focus ROI.

:::info Environment Consistency
For accurate comparison, it is recommended to use Focus Mode under consistent lighting and scene conditions.
:::

![Focus Mode](//img/focusMode.png)

---

## Support
If you encounter any issues with VizionViewer™:
1. Go to **Tools → Help → Report Issue**
2. Export the VizionViewer™ report
3. Attach the report file
4. Describe the issue
5. Send to [<EMAIL>](mailto:<EMAIL>)