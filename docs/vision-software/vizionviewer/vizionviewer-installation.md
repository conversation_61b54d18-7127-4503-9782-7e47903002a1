---
sidebar_position: 2
title: Installation
---

# Installation Guide

---

## Windows x64

**Supported Platforms:** Windows 10/11 64-bit

:::tip
It is recommended to use the latest version for the best experience.
:::

### Installation Steps

1. **Download the installer:**  
   [Download VizionViewer™ for Windows](https://download.technexion.com/vizionviewer/windows_x64/)
2. **Run the installer:**  
   Execute the downloaded file (`vizionviewersetup_{version}.exe`).
3. **Default installation path:**  
   `C:\Program Files\VizionViewer`

---

## Linux x64

**Supported Platforms:**
- Ubuntu Desktop 20.04 / 22.04
- UP Squared Pro 7000

### Offline Installation

1. **Download the .deb packages:**  
   [Download for Ubuntu x64](https://download.technexion.com/vizionviewer/linux_x64/)
2. **Extract and install the packages:**
   ```bash
   tar -xf ./vizionviewersetup_{version}_linux64.tar.xz
   sudo apt install ./vizionsdk.deb
   sudo apt install ./vizionviewer.deb
   ```
3. **APT repository and signing key will be installed automatically for future updates.**

### Online Installation

1. **Install repository and key:**
   ```bash
   sudo apt-get install wget gpg
   wget -qO- https://download.technexion.com/apt/technexion.asc | gpg --dearmor > packages.technexion.gpg
   sudo install -D -o root -g root -m 644 packages.technexion.gpg /etc/apt/keyrings/packages.technexion.gpg
   sudo sh -c 'echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionsdk/ stable main" > /etc/apt/sources.list.d/vizionsdk.list'
   sudo sh -c 'echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionviewer/ stable main" >> /etc/apt/sources.list.d/vizionsdk.list'
   ```
2. **Update package cache and install:**
   ```bash
   sudo apt update
   sudo apt install vizionsdk vizionviewer
   ```

:::info
If you are using UP Squared Pro 7000, please add the Gstreamer library to your environment:
```bash
export GST_PLUGIN_PATH=/usr/lib/gstreamer-1.0
```
:::

---

## Linux ARM64

**Supported Platforms:**
- NVIDIA Jetson (JetPack 6.x or later)
- NXP-i.MX95, NXP-i.MX93, NXP-i.MX8MP, NXP-i.MX8MQ, NXP-i.MX8MM
- TI-TDA4VM

:::warning
Platforms with older toolchains (e.g., GCC < 11 or glibc < 2.34) are not supported in the current release.  
Please use [legacy versions of VizionViewer™](https://download.technexion.com/vizionviewer/archived/) if needed.
:::

### Offline Installation

1. **Download the .deb packages:**  
   [Download for Linux ARM64](https://download.technexion.com/vizionviewer/linux_arm64/)
2. **Extract and install the packages:**
   ```bash
   tar -xf ./vizionviewersetup_{version}_linuxarm64.tar.xz
   sudo apt install ./vizionsdk.deb
   sudo apt install ./vizionviewer.deb
   ```
3. **APT repository and signing key will be installed automatically for future updates.**

### Online Installation

1. **Install repository and key:**
   ```bash
   sudo apt-get install wget gpg
   wget -qO- https://download.technexion.com/apt/technexion.asc | gpg --dearmor > packages.technexion.gpg
   sudo install -D -o root -g root -m 644 packages.technexion.gpg /etc/apt/keyrings/packages.technexion.gpg
   sudo sh -c 'echo "deb [arch=arm64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionsdk/ stable main" > /etc/apt/sources.list.d/vizionsdk.list'
   sudo sh -c 'echo "deb [arch=arm64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionviewer/ stable main" >> /etc/apt/sources.list.d/vizionsdk.list'
   ```
2. **Update package cache and install:**
   ```bash
   sudo apt update
   sudo apt install vizionsdk vizionviewer
   ```