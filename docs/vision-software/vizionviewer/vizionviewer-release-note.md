---
sidebar_position: 3
toc_min_heading_level: 2
toc_max_heading_level: 2
title: Release Notes
---

# Release Notes

---
## v25.08.2
**Release Date:** 2025-08-30

### Added
- Draggable layout functionality for customizable workspace
- Color theme selection feature
- Custom logo functionality

### Changed
- Completely redesigned user interface with modern design
- Streamlined interface by focusing on core functionality

### Download
- **Windows x64:** [Download](https://download.technexion.com/vizionviewer/windows_x64/vizionviewersetup_25.08.2.exe)
- **Linux x64:** [Download](https://download.technexion.com/vizionviewer/linux_x64/vizionviewer-25.08.2-linux64.tar.xz)
- **Linux ARM64:** [Download](https://download.technexion.com/vizionviewer/linux_x64/vizionviewer-25.08.2-linux64.tar.xz)

---

## v25.06.1
**Release Date:** 2025-06-30

### Changed
- Added support for more camera module firmware updates
- Fixed timeout issue when starting streaming at high resolution on i.MX8MM

### Download
- **Windows x64:** [Download](https://download.technexion.com/vizionviewer/windows_x64/vizionviewersetup_25.06.1.exe)
- **Linux x64:** [Download](https://download.technexion.com/vizionviewer/linux_x64/vizionviewer-25.06.1-linux64.tar.xz)
- **Linux ARM64:** [Download](https://download.technexion.com/vizionviewer/linux_arm64/vizionviewer-25.06.1-linuxarm64.tar.xz)

---

## v25.04.1
**Release Date:** 2025-04-10

### Added
- Integration with VizionSDK Vx API
- Focus Mode
- Exposure Range adjustment in Exposure Auto Mode
- Capture Info displaying current Timestamp and FrameCount

### Improved
- Real-time progress feedback during firmware update

### Download
- **Windows x64:** [Download](https://download.technexion.com/vizionviewer/windows_x64/vizionviewersetup_25.04.1.exe)
- **Linux x64:** [Download](https://download.technexion.com/vizionviewer/linux_x64/vizionviewer-25.04.1-linux64.tar.xz)
- **Linux ARM64:** [Download](https://download.technexion.com/vizionviewer/linux_arm64/vizionviewer-25.04.1-linuxarm64.tar.xz)

---

## v25.03.1
**Release Date:** 2025-03-18

### Added
- FPS display in streaming window
- Improved stability of TEVS firmware updates

### Fixed
- Jetson platform streaming issue
- Jetson Orin EVK TEVS Camera firmware update issue
- i.MX8MM streaming issue with large resolutions

### Download
- **NVIDIA Jetpack5.x, Jetpack6.x:** [Download](https://download.technexion.com/vizionviewer/archived/linux_nvidia_jetson/stable/vizionviewer_25.03.1_jetson_stable.tar.xz)

---

## v24.11.1
**Release Date:** 2024-11-13

### Added
- Support for UP Squared Pro 7000 platform
- Support for NVIDIA Jetpack6.x platform

### Fixed
- i.MX8MM unable to list MIPI Camera
- TEVS camera streaming issue after firmware upgrade

### Download
- **Linux (Debian) x64:** [Download](https://download.technexion.com/vizionviewer/archived/linux_x64/vizionviewer_24.11.1_amd64.tar.xz) (Compatible with UP Squared Pro 7000)
- **NXP-iMX (iMX8MP, iMX8MM, iMX93):** [Download](https://download.technexion.com/vizionviewer/archived/linux_nxp_imx/vizionviewer_24.11.1_imx8-9.tar.xz)
- **NVIDIA Jetpack5.x:** [Download](https://download.technexion.com/vizionviewer/archived/linux_nvidia_jetson/stable/vizionviewer_24.11.1_jetson_stable.tar.xz)
- **TI TDA4VM:** [Download](https://download.technexion.com/vizionviewer/archived/linux_ti/vizionviewer_24.11.1_ti.tar.xz)

---

## v24.08.3
**Release Date:** 2024-08-20

### Added
- AGC mode in exposure settings
- NV12 in preferred format settings
- VizionCTL in Windows package

### Fixed
- Non-UVC camera opening issue
- UVC updater loading speed
- Software update button unclickable after license activation

### Download
- **Windows 10/11 x64:** [Download](https://download.technexion.com/vizionviewer/archived/windows_x64/vizionviewersetup_24.08.3.exe)
- **Linux (Debian) x64:** [Download](https://download.technexion.com/vizionviewer/archived/linux_x64/vizionviewer_24.08.3_amd64.tar.xz)
- **NXP-iMX (iMX8MP, iMX8MM, iMX93):** [Download](https://download.technexion.com/vizionviewer/archived/linux_nxp_imx/vizionviewer_24.08.3_imx8-9.tar.xz)
- **NVIDIA Jetpack5.x:** [Download](https://download.technexion.com/vizionviewer/archived/linux_nvidia_jetson/stable/vizionviewer_24.08.3_jetson_stable.tar.xz)
- **TI TDA4VM:** [Download](https://download.technexion.com/vizionviewer/archived/linux_ti/vizionviewer_24.08.3_ti.tar.xz)

---

## v24.06.1
**Release Date:** 2024-06-06

### Added
- Preferred format setting
- NXP iMX93 support
- Option to disable networking requests on Windows

### Changed
- Exposure max value set to 1000000

### Fixed
- Exposure max value textbox length
- Pop-up window size issue

### Download
- **Windows 10/11 x64:** [Download](https://download.technexion.com/vizionviewer/archived/windows_x64/vizionviewersetup_24.06.1.exe)
- **Linux (Debian) x64:** [Download](https://download.technexion.com/vizionviewer/archived/linux_x64/vizionviewer_24.06.1_amd64.tar.xz)
- **NXP-iMX (iMX8MP, iMX8MM, iMX93):** [Download](https://download.technexion.com/vizionviewer/archived/linux_nxp_imx/vizionviewer_24.06.1_imx8-9.tar.xz)
- **NVIDIA Jetpack5.x:** [Download](https://download.technexion.com/vizionviewer/archived/linux_nvidia_jetson/stable/vizionviewer_24.06.1_jetson_stable.tar.xz)

---

## v24.03.2
**Release Date:** 2024-03-11

### Added
- Stable version for Jetpack4

### Fixed
- Video and image path editing
- FPS decimal point display

### Download
- **Windows 10/11 x64:** [Download](https://download.technexion.com/vizionviewer/archived/windows_x64/vizionviewersetup_24.03.2.exe)
- **Linux (Debian) x64:** [Download](https://download.technexion.com/vizionviewer/archived/linux_x64/vizionviewer_24.03.2_amd64.tar.xz)
- **NXP-iMX (iMX8MP, iMX8MM):** [Download](https://download.technexion.com/vizionviewer/archived/linux_nxp_imx/vizionviewer_24.03.2_imx8.tar.xz)
- **NVIDIA Jetpack5.x:** [Download](https://download.technexion.com/vizionviewer/archived/linux_nvidia_jetson/stable/vizionviewer_24.03.2_jetson_stable.tar.xz)
- **NVIDIA Jetpack4.x:** [Download](https://download.technexion.com/vizionviewer/archived/linux_nvidia_jetson/bionic/vizionviewer_24.03.2_jetson_bionic.tar.xz)

---

## v24.03.1
**Release Date:** 2024-03-05

### Fixed
- Application update not working on Windows
- Crash dialog on Linux
- USB2.0 ISP firmware update issue

### Download
- **Windows 10/11 x64:** [Download](https://download.technexion.com/vizionviewer/archived/windows_x64/vizionviewersetup_24.03.1.exe)
- **Linux (Debian) x64:** [Download](https://download.technexion.com/vizionviewer/archived/linux_x64/vizionviewer_24.03.1_amd64.tar.xz)
- **NXP-iMX (iMX8MP, iMX8MM):** [Download](https://download.technexion.com/vizionviewer/archived/linux_nxp_imx/vizionviewer_24.03.1_imx8.tar.xz)

---

## v24.02.1
**Release Date:** 2024-02-05

### Added
- OSP (On Sensor Profile) feature
- Firmware update management
- Multiple camera simulation
- UID in device list selection

### Changed
- Disabled automatic camera activation on startup
- Disabled automatic OS profile activation when opening camera

### Download
- **Windows 10/11 x64:** [Download](https://download.technexion.com/vizionviewer/archived/windows_x64/vizionviewersetup_24.02.1.exe)
- **Linux (Debian) x64:** [Download](https://download.technexion.com/vizionviewer/archived/linux_x64/vizionviewer_24.02.1_amd64.tar.xz)
- **NXP-iMX (iMX8MP, iMX8MM):** [Download](https://download.technexion.com/vizionviewer/archived/linux_nxp_imx/vizionviewer_24.02.1_imx8.tar.xz)