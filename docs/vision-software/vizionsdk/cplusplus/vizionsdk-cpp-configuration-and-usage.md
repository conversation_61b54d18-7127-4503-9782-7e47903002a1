---
title: Configuration & Usage
sidebar_position: 2
description: VisionSDK Configuration & Usage
---

# Configuration & Usage

---

## Configuration Setup

After installation, the configuration file `VxConfig.conf` will be located at:

- **Windows:**
  > `C:\Program Files\vizionsdk\bin\VxConfig.conf`
- **Linux, NXP-iMX, NVIDIA-Jetpack6.x and later:**
  > `/usr/share/vizionsdk/VxConfig.conf`
- **Local library (Tarball Extraction):**
  > `/vizionsdk-{version}-{os}/bin/VxConfig.conf`

This configuration file allows VizionSDK to recognize TechNexion series camera devices.

:::tip
You can also customize the location of `VxConfig.conf` by setting the `VIZIONSDK_CONFIG_PATH` environment variable.
:::

---

## How to use VizionSDK

You can link the VizionSDK library with CMake. For more details, see the [Sample Build Guide](https://github.com/TechNexion-Vision/vizionsdk/tree/main/Samples).

### Prerequisites
- CMake (version 3.0 or newer)
- C++ compiler

### Import the library
Add the VizionSDK header in your code:
```cpp
#include "VizionSDK.h"
```

### Build the project with CMake

#### Using System-Installed SDK (.exe or .deb Installed)
If you installed VizionSDK using the installer, use the following CMake commands:
```cmake
# Find VizionSDK package
find_package(vizionsdk REQUIRED)

# Link libraries
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    vizionsdk::VizionSDK
)
```

#### Using Extracted SDK Archive
If you are using the SDK directly from the extracted folder, update the paths manually:
```cmake
# Set path to the SDK library directory
# NOTE: Update this path to match your SDK location

target_include_directories(${PROJECT_NAME}
  PRIVATE
  <YOUR_SDK_PATH>/include/vizionsdk
)

target_link_directories(${PROJECT_NAME}
  PRIVATE
  <YOUR_SDK_PATH>/lib
)

# Link libraries
target_link_libraries(${PROJECT_NAME}
  PRIVATE
  VizionSDK
)
``` 

> For more detailed information and advanced usage, please refer to the API documentation or the examples on GitHub. 