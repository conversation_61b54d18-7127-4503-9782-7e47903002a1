---
title: Build and Run Samples
sidebar_position: 3
description: VisionSDK build and run samples
---

# Build and Run Samples

---

## Introduction
This guide explains how to build the sample code for VizionSDK. You can download the sample code from the [official repository samples directory](https://github.com/TechNexion-Vision/vizionsdk/tree/main/samples).

## Prerequisites
Before building and running the project, ensure you have the following installed:

### CMake
- Download from the [official CMake website](https://cmake.org/download/), extract or install it, and add it to your system PATH:

```bash
export PATH=/path/to/cmake/bin:$PATH
```

- **Linux (Ubuntu/Debian):**

```bash
sudo apt update
sudo apt install cmake
```

### C++ Compiler
- **Windows:** Install Visual Studio (with "Desktop development with C++") or MinGW.
- **Linux (Ubuntu/Debian):**

```bash
sudo apt install build-essential
```

- **Embedded:** Use cross-compilation on your host machine.

## Build Steps

1. **Open Terminal**  
   - Windows: Command Prompt or PowerShell  
   - Linux: Any terminal
2. **Go to sample directory:**

   ```bash
   cd path/to/sample1-hello_vizionsdk  # Or sample2-camera_capture / sample3-camera_control
   ```
3. **Create build directory:**

   ```bash
   mkdir build && cd build
   ```
4. **Run CMake:**

   ```bash
   cmake ..
   ```
   > *Ensure your `CMakeLists.txt` is configured to link against the correct SDK path. See configuration examples below.*
5. **Build:**

   ```bash
   cmake --build .
   ```

## CMake Config

### System SDK
```cmake
find_package(vizionsdk REQUIRED)

target_link_libraries(${PROJECT_NAME}
  PRIVATE
  vizionsdk::VizionSDK
)
```

### Extracted SDK
```cmake
# Adjust <YOUR_SDK_PATH> to your actual SDK location
target_include_directories(${PROJECT_NAME}
  PRIVATE
  <YOUR_SDK_PATH>/include/vizionsdk
)

target_link_directories(${PROJECT_NAME}
  PRIVATE
  <YOUR_SDK_PATH>/lib
)

target_link_libraries(${PROJECT_NAME}
  PRIVATE
  VizionSDK
)
```
> *Replace `${PROJECT_NAME}` with your actual CMake `add_executable()` name (e.g., `hello_vizionsdk`, `camera_capture`, etc.).*

## Run Samples
The executable will be located in the `build` directory.

- **Windows:**
  ```plaintext
  .\hello_vizionsdk.exe
  ```
- **Linux:**
  ```plaintext
  ./hello_vizionsdk
  ```
> *Replace `hello_vizionsdk` with the actual sample name if you are working with `camera_capture` or `camera_control`.*
