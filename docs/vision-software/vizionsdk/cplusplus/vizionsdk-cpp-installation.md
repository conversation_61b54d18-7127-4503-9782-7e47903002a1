---
title: Installation
sidebar_position: 1
description: VisionSDK Installation
---

# Installation

---

## Windows-x64

### Steps
1. **Download** VizionSDK for Windows from the [VizionSDK Download Page](https://github.com/TechNexion-Vision/vizionsdk/releases).
2. **Run the installer**:
   ```bash
   vizionsdk-{version}-win64.exe
   ```
3. **Default installation path:**
   > `C:\Program Files\vizionsdk`

---

## Linux-x64

### Supported Platforms
- General PCs
- UP Squared Pro 7000

### Offline Installation
1. **Download** the VizionSDK .deb package for Ubuntu from the [VizionSDK .deb packages Download Page](https://github.com/TechNexion-Vision/vizionsdk/releases).
2. **Extract the SDK (no installation required):**
   ```bash
   tar -xf ./vizionsdk-{version}-linux64.tar.xz
   ```
3. **Install the .deb package:**
   ```bash
   sudo apt install ./vizionsdk-linux64.deb
   ```
   > Installing the .deb package will automatically add the apt repository and signing key for future updates via the system package manager.

### Online Installation
1. **Add the repository and key:**
   ```bash
   sudo apt-get install wget gpg
   wget -qO- https://download.technexion.com/apt/technexion.asc | gpg --dearmor > packages.technexion.gpg
   sudo install -D -o root -g root -m 644 packages.technexion.gpg /etc/apt/keyrings/packages.technexion.gpg
   sudo sh -c 'echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionsdk/ stable main" > /etc/apt/sources.list.d/vizionsdk.list'
   sudo sh -c 'echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionviewer/ stable main" >> /etc/apt/sources.list.d/vizionsdk.list'
   ```
2. **Update and install:**
   ```bash
   sudo apt update
   sudo apt install vizionsdk
   ```

---

## Linux-ARM64

### Supported Platforms
- NXP-iMX8MP
- NXP-iMX8MM
- NXP-iMX93
- NXP-iMX95
- NVIDIA-Jetpack6.x and later

### Offline Installation
1. **Download** the VizionSDK .deb package for ARM from the [VizionSDK .deb packages Download Page](https://github.com/TechNexion-Vision/vizionsdk/releases).
2. **Extract the SDK (no installation required):**
   ```bash
   tar -xf ./vizionsdk-{version}-linuxarm64.tar.xz
   ```
3. **Install the .deb package:**
   ```bash
   sudo apt install ./vizionsdk-linuxarm64.deb
   ```
   > Installing the .deb package will automatically add the apt repository and signing key for future updates via the system package manager.

### Online Installation
> **Note for Jetpack users:** The stable main version is different from the legacy stable version. Please use the following instructions.

1. **Add the repository and key:**
   ```bash
   sudo apt-get install wget gpg
   wget -qO- https://download.technexion.com/apt/technexion.asc | gpg --dearmor > packages.technexion.gpg
   sudo install -D -o root -g root -m 644 packages.technexion.gpg /etc/apt/keyrings/packages.technexion.gpg
   sudo sh -c 'echo "deb [arch=arm64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionsdk/ stable main" > /etc/apt/sources.list.d/vizionsdk.list'
   sudo sh -c 'echo "deb [arch=arm64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionviewer/ stable main" >> /etc/apt/sources.list.d/vizionsdk.list'
   ```
2. **Update and install:**
   ```bash
   sudo apt update
   sudo apt install vizionsdk
   ```

---

## Troubleshooting

:::warning
The stable Jetson APT repository has been deprecated.  
Please use the stable main repository as shown in the Online Installation section.
:::


