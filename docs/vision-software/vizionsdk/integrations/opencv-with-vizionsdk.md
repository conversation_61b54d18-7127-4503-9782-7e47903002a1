---
title: "OpenCV with VizionSDK"
description: "OpenCV integrated with VizionSDK"
---
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

This article will introduce how to display with **OpenCV** and control the camera using **VizionSDK**.   
You can download the Sample Code [**here**](https://github.com/TechNexion-Vision/vizionsdk-opencv).

### OpenCV Version

These examples use OpenCV version 4.9 or newer.

### Load OpenCV and VizionSDK

Import the **OpenCV** and **VizionSDK** libraries to support the following examples.

<Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    #include "VizionSDK.h"
    #include <opencv2/highgui.hpp>
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    import pyvizionsdk
    from pyvizionsdk import VX_UVC_IMAGE_PROPERTIES, VX_IMAGE_FORMAT

    import cv2
    import numpy as np
    ```

    </TabItem>
</Tabs>


### Start the device

Discover the device and initial it. Then, set the device format to the minimum size for **MJPG** format.

<Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    // initial the camera
    int dev_idx = 0;
    auto cam = VxInitialCameraDevice(dev_idx);
    VxOpen(cam);
    
    // set format to min size for mjpg format
    std::vector<VxFormat> fmt_list;
    VxGetFormatList(cam, fmt_list);
    int min_width = 1920;
    int min_height = 1080;
    VxFormat min_fmt;
    for (auto fmt : fmt_list) {
        // find MJPG smallest size format
        if (fmt.format == VX_IMAGE_FORMAT::MJPG &&
            fmt.width * fmt.height < min_width * min_height) {
            min_width = fmt.width;
            min_height = fmt.height;
            min_fmt = fmt;
        }
    }
     
    if (VxSetFormat(cam, min_fmt) == 0) {
        std::cout << "Set Capture Format Success!" << std::endl;
    } else {
        std::cout << "Set Capture Format Failed!" << std::endl;
    }
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    # initialize camera device
    idx = 0
    camera = pyvizionsdk.VxInitialCameraDevice(idx)
    
    # open camera
    result = pyvizionsdk.VxOpen(camera)
    print("Open camera return code:", result)
    
    # get format
    result, format_list = pyvizionsdk.VxGetFormatList(camera)
    mjpg_format = None
    min_resolution = float('inf')
    for format in format_list:
        # get mjpg format and minimum resolution
        if format.format == VX_IMAGE_FORMAT.VX_IMAGE_FORMAT_MJPG:
            resolution = format.width * format.height
            if resolution < min_resolution:
                min_resolution = resolution
                mjpg_format = format
    print("Return code:", result)
    
    # set format
    result = pyvizionsdk.VxSetFormat(camera, mjpg_format)
    ```

    </TabItem>
</Tabs>

### Display the image

Use **VizionSDK** **VxGetImage** function to retrieve the image data and convert it into **OpenCV** mat to display in the window.

This example demonstrates how to retrieve the format, set it to a specific format, and display the image in a window.

<Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    // start streaming
    VxStartStreaming(cam);
    
    // get the min format image
    uint8_t* raw_data = new uint8_t[3840 * 2160 * 2];
    int raw_size = 0;
    VxGetImage(cam, raw_data, &raw_size, 2500);
    
    // retrieve the data into the mat array and display with cv2.imshow()
    cv::Mat matImg;
    matImg = cv::imdecode(cv::Mat(1, raw_size, CV_8UC1, raw_data), cv::IMREAD_COLOR);
    cv::imshow("MJPG Image", matImg);
    cv::waitKey(0);
    cv::destroyAllWindows();
    
    VxStopStreaming(cam);
    VxClose(cam);
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    # start streaming
    result = pyvizionsdk.VxStartStreaming(camera)
    
    # get the MJPG format image
    result, image = pyvizionsdk.VxGetImage(camera, 1000, mjpg_format)
    
    # retrieve the data to opencv and display with cv2.imshow()
    nparr = np.frombuffer(image, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    cv2.imshow("MJPG Image", image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    # stop streaming
    pyvizionsdk.VxStopStreaming(camera)
    # close camera
    pyvizionsdk.VxClose(camera)
    ```

    </TabItem>
</Tabs>

### OpenCV VideoCapture with VizionSDK

Adjust the image properties by **VizionSDK** and capture with **OpenCV** VideoCapture.

This example will show how to set the **brightness** and **capture** with the frame displayed in the window.

1. Capture the image before adjust **brightness**
    <Tabs groupId="api-code">
        <TabItem value="c++" label="C++">

        ```cpp
        // capture the video and display with cv2.imshow()
        cv::VideoCapture cap;
        #ifdef _WIN32
            cap = cv::VideoCapture(dev_idx, cv::CAP_DSHOW);
        #else
            cap = cv::VideoCapture(dev_idx);
        #endif
        cap.set(cv::CAP_PROP_FRAME_WIDTH, min_width);
        cap.set(cv::CAP_PROP_FRAME_HEIGHT, min_height);
        cv::Mat frameBefore;
        cap >> frameBefore;
        cv::imshow("Before setting", frameBefore);
        cv::waitKey(0);
        cv::destroyAllWindows();
        cap.release();
        ```
        </TabItem>
        <TabItem value="py" label="Python">

        ```py
        # import the platform library for video capture backend selection with cross-platform compatibility
        import platform
        
        min_width = mjpg_format.width
        min_height = mjpg_format.height
        
        # video capture backend selection for cross-platform campatibility
        if platform.system() == 'Windows':
            cap = cv2.VideoCapture(idx, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(idx)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, min_width)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, min_height)
        # Capture frame
        ret, frame = cap.read()
        cv2.imshow('Before setting', frame)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        cap.release()
        ```

        </TabItem>
    </Tabs>

2. Adjust **brightness** to 16
    <Tabs groupId="api-code">
        <TabItem value="c++" label="C++">

        ```cpp
        // get the brightness
        long value;
        int flag;
        VxGetUVCImageProcessing(cam, VX_UVC_IMAGE_PROPERTIES::UVC_IMAGE_BRIGHTNESS, value, flag);
        std::cout << "UVC brightness: " << value << std::endl;
        
        // set the brightness
        int result;
        result = VxSetUVCImageProcessing(cam, VX_UVC_IMAGE_PROPERTIES::UVC_IMAGE_BRIGHTNESS, 16, flag);
        std::cout << "Set UVC brightness return code: " << result << std::endl;
        
        // get the brightness
        VxGetUVCImageProcessing(cam, VX_UVC_IMAGE_PROPERTIES::UVC_IMAGE_BRIGHTNESS, value, flag);
        std::cout << "After setting UVC brightness: " << value << std::endl;
        ```
        </TabItem>
        <TabItem value="py" label="Python">

        ```py
        # get the brightness
        result, brightness, flag = pyvizionsdk.VxGetUVCImageProcessing(camera, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS)
        print("UVC brightness:", brightness)
        print("Flag:", flag)
        print("Return code:", result)
        
        # set the brightness
        result = pyvizionsdk.VxSetUVCImageProcessing(camera, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS, 16, 0)
        print("Set UVC brightness return code:", result)
        
        # get the brightness
        result, brightness, flag = pyvizionsdk.VxGetUVCImageProcessing(camera, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS)
        print("After setting UVC brightness:", brightness)
        print("Flag:", flag)
        print("Return code:", result)
        ```

        </TabItem>
    </Tabs>

3. Capture the image after adjusting **brightness**
    <Tabs groupId="api-code">
        <TabItem value="c++" label="C++">

        ```cpp
        // capture the video after setting the property and display with cv2.imshow()
        #ifdef _WIN32
            cap = cv::VideoCapture(dev_idx, cv::CAP_DSHOW);
        #else
            cap = cv::VideoCapture(dev_idx);
        #endif
        cap.set(cv::CAP_PROP_FRAME_WIDTH, min_width);
        cap.set(cv::CAP_PROP_FRAME_HEIGHT, min_height);
        cv::Mat frameAfter;
        cap >> frameAfter;
        cv::imshow("After setting", frameAfter);
        cv::waitKey(0);
        cv::destroyAllWindows();
        cap.release();
        
        VxClose(cam);
        ```
        </TabItem>
        <TabItem value="py" label="Python">

        ```py
        # captured by opencv
        if platform.system() == 'Windows':
            cap = cv2.VideoCapture(idx, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(idx)
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, min_width)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, min_height)
        # Capture frame
        ret, frame = cap.read()
        cv2.imshow('After setting', frame)
        cv2.waitKey(0)
        cap.release()
        cv2.destroyAllWindows()
        
        # close camera
        pyvizionsdk.VxClose(camera)
        ```

        </TabItem>
    </Tabs>
