---
title: "OpenCV with VizionSDK"
description: "OpenCV integrated with VizionSDK"
---
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

This article will introduce how to display with **OpenCV** and control the camera using **VizionSDK**.   
You can download the Sample Code [**here**](https://github.com/TechNexion-Vision/vizionsdk-opencv).

### OpenCV Version

These examples use OpenCV version 4.9 or newer.

### Load OpenCV and VizionSDK

Import the **OpenCV** and **VizionSDK** libraries to support the following examples.

<Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    #include "VizionSDK.h"
    #include <opencv2/highgui.hpp>
    #include <opencv2/opencv.hpp>
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    import pyvizionsdk
    from pyvizionsdk import VX_ISP_IMAGE_PROPERTIES, VX_IMAGE_FORMAT

    import cv2
    import numpy as np
    ```

    </TabItem>
</Tabs>


### Start the device

Discover the device and initial it. Then, set the device format to the minimum size for **MJPG** format.

<Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    // initial the camera
    int dev_idx = 0;
    auto cam = VxInitialCameraDevice(dev_idx);
    VxOpen(cam);
    
    // set format to min size for mjpg format
    std::vector<VxFormat> fmt_list;
    VxGetFormatList(cam, fmt_list);
    int min_width = 1920;
    int min_height = 1080;
    VxFormat min_fmt;
    for (auto fmt : fmt_list) {
        // find MJPG smallest size format
        if (fmt.format == VX_IMAGE_FORMAT::MJPG &&
            fmt.width * fmt.height < min_width * min_height) {
            min_width = fmt.width;
            min_height = fmt.height;
            min_fmt = fmt;
        }
    }
     
    if (VxSetFormat(cam, min_fmt) == 0) {
        std::cout << "Set Capture Format Success!" << std::endl;
    } else {
        std::cout << "Set Capture Format Failed!" << std::endl;
    }
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    # initialize camera device
    idx = 0
    camera = pyvizionsdk.VxInitialCameraDevice(idx)
    
    # open camera
    result = pyvizionsdk.VxOpen(camera)
    print("Open camera return code:", result)
    
    # get format
    result, format_list = pyvizionsdk.VxGetFormatList(camera)
    mjpg_format = None
    min_resolution = float('inf')
    for format in format_list:
        # get mjpg format and minimum resolution
        if format.format == VX_IMAGE_FORMAT.VX_IMAGE_FORMAT_MJPG:
            resolution = format.width * format.height
            if resolution < min_resolution:
                min_resolution = resolution
                mjpg_format = format
    print("Return code:", result)
    
    # set format
    result = pyvizionsdk.VxSetFormat(camera, mjpg_format)
    ```

    </TabItem>
</Tabs>

### Display the image

Use **VizionSDK** **VxGetImage** function to retrieve the image data and convert it into **OpenCV** mat to display in the window.

This example demonstrates how to retrieve the format, set it to a specific format, and display the image in a window.

<Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    // start streaming
    VxStartStreaming(cam);
    
    // get the min format image
    uint8_t* raw_data = new uint8_t[3840 * 2160 * 2];
    int raw_size = 0;
    VxGetImage(cam, raw_data, &raw_size, 2500);
    
    // retrieve the data into the mat array and display with cv2.imshow()
    cv::Mat matImg;
    matImg = cv::imdecode(cv::Mat(1, raw_size, CV_8UC1, raw_data), cv::IMREAD_COLOR);
    cv::imshow("MJPG Image", matImg);
    cv::waitKey(0);
    cv::destroyAllWindows();
    
    VxStopStreaming(cam);
    VxClose(cam);
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    # start streaming
    result = pyvizionsdk.VxStartStreaming(camera)
    
    # get the MJPG format image
    result, image = pyvizionsdk.VxGetImage(camera, 1000, mjpg_format)
    
    # retrieve the data to opencv and display with cv2.imshow()
    nparr = np.frombuffer(image, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    cv2.imshow("MJPG Image", image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    # stop streaming
    pyvizionsdk.VxStopStreaming(camera)
    # close camera
    pyvizionsdk.VxClose(camera)
    ```

    </TabItem>
</Tabs>

### OpenCV VideoCapture with VizionSDK

Adjust the image properties using **VizionSDK** and capture frames with **OpenCV**'s VideoCapture.

This example demonstrates how to open a video window displaying a live camera stream. While the stream is active, you can press keys to increase or decrease the brightness.

1. Initialize VideoCapture with opencv:
    <Tabs groupId="api-code">
        <TabItem value="c++" label="C++">

        ```cpp
        // Capture image with opencv VideoCapture API
        cv::VideoCapture cap;
        #ifdef _WIN32
            cap = cv::VideoCapture(dev_idx, cv::CAP_DSHOW);
        #else
            cap = cv::VideoCapture(dev_idx);
        #endif
        cap.set(cv::CAP_PROP_FRAME_WIDTH, min_width);
        cap.set(cv::CAP_PROP_FRAME_HEIGHT, min_height);
        
        ```
        </TabItem>
        <TabItem value="py" label="Python">

        ```py
        # import the platform library for video capture backend selection with cross-platform compatibility
        import platform
        
        # Capture image with opencv VideoCapture API
        if platform.system() == 'Windows':
            cap = cv2.VideoCapture(idx, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(idx)

        cap.set(cv2.CAP_PROP_FRAME_WIDTH, min_width)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, min_height)
        ```

        </TabItem>
    </Tabs>

2. Retrieve the current brightness value:
    
    The code below shows how to handle brightness, and you can follow the same structure for other properties, such as:

    - ISP_EHDR_EXPOSURE_MAX_TIME

    - ISP_IMAGE_EXPOSURE_TIME

    - ISP_IMAGE_GAIN

    For more image property examples (e.g., **eHDR**), refer to the full example [here](https://github.com/TechNexion-Vision/vizionsdk-opencv)

    <Tabs groupId="api-code">
        <TabItem value="c++" label="C++">

        ```cpp
        // Get the brightness range
        int min_brightness, max_brightness, step, def;
        VxGetISPImageProcessingRange(cam, VX_ISP_IMAGE_PROPERTIES::ISP_IMAGE_BRIGHTNESS, min_brightness,
                                    max_brightness, step, def);
        std::cout << "Brightness min: " << min_brightness << ", max: " << max_brightness
                << ", step: " << step << ", default: " << def << std::endl;

        // Get the brightness
        int brightness, flag;
        VxGetISPImageProcessing(cam, VX_ISP_IMAGE_PROPERTIES::ISP_IMAGE_BRIGHTNESS, brightness, flag);
        std::cout << "Intial ISP brightness: " << brightness << ", flag: " << flag << std::endl;
        
        ```
        </TabItem>
        <TabItem value="py" label="Python">

        ```py
        # Get the brightness range
        result, min_brightness, max_brightness, step, def_brightness = pyvizionsdk.VxGetISPImageProcessingRange(camera, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS)
        print(f"Brightness min: {min_brightness}, max: {max_brightness}, step: {step}, default: {def_brightness}, return code: {result}")

        # Get the brightness
        result, brightness, flag = pyvizionsdk.VxGetISPImageProcessing(camera, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS)
        print(f"Initial ISP brightness: {brightness}, flag: {flag}, return code: {result}")
        ```

        </TabItem>
    </Tabs>

3. Capture and display video in a window. Use the following keys during playback:
    - **u**: Increase brightness
    - **d**: Decrease brightness 
    - **q**: Stop playing video and exit the program.

    The **draw_label** helper function (included in the [**sample code**](https://github.com/TechNexion-Vision/vizionsdk-opencv)) is used to overlay text labels directly onto the video display window.

    <Tabs groupId="api-code">
        <TabItem value="c++" label="C++">

        ```cpp
        cv::Mat frame;
        while (true) {
            bool ret = cap.read(frame);
            if (ret) {
                std::string brightness_label = "brightness: " + std::to_string(brightness);
                std::string label_str = "u: increase, d: decrease, q: exit";
                draw_label(frame, brightness_label, cv::Point(20, 20));
                draw_label(frame, label_str, cv::Point(20, 40));
                cv::imshow("Adjust brightness", frame);
                int key = cv::waitKey(1) & 0xFF;
                if (key == 'u') {
                    if (brightness < max_brightness) {
                        brightness = brightness + step;
                    }
                    VxSetISPImageProcessing(cam, VX_ISP_IMAGE_PROPERTIES::ISP_IMAGE_BRIGHTNESS,
                                            brightness);
                    std::cout << "Set brightness to: " << brightness << std::endl;
                }
                if (key == 'd') {
                    if (brightness > min_brightness) {
                        brightness = brightness - step;
                    }
                    VxSetISPImageProcessing(cam, VX_ISP_IMAGE_PROPERTIES::ISP_IMAGE_BRIGHTNESS,
                                            brightness);
                    std::cout << "Set brightness to: " << brightness << std::endl;
                }
                if (key == 'q') {
                    break;
                }
            } else {
                std::cout << "Can't receive frame (stream end?). Exiting ..." << std::endl;
                break;
            }
        }
        ```
        </TabItem>
        <TabItem value="py" label="Python">

        ```py
        # Capture video and display
        while True:
            ret, frame = cap.read()
            if ret:
                __draw_label(frame, f"brightness: {brightness}", (20,20))
                __draw_label(frame, "u: increase, d: decrease, q: exit", (20,40))
                cv2.imshow(f"Adjust brightness", frame)
                key = cv2.waitKey(1) & 0xFF
                if key == ord('u'):
                    if brightness < max_brightness:
                        brightness = brightness + step
                    result = pyvizionsdk.VxSetISPImageProcessing(camera, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS, brightness)
                    print(f"Set brightness to: {brightness}")
                if key == ord('d'):
                    if brightness > min_brightness:
                        brightness = brightness - step
                    result = pyvizionsdk.VxSetISPImageProcessing(camera, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS, brightness)
                    print(f"Set brightness to: {brightness}")
                if key == ord('q'):
                    break
            else:
                print("Can't receive frame (stream end?). Exiting ...")
                break       
        ```

        </TabItem>
    </Tabs>

### 
