---
sidebar_position: 3
title: VizionSDK
---
import ProductList from '@site/src/components/ProductList.jsx';

## Introduction

The [VizionSDK](/docs/vision-software/vizionsdk/cplusplus/vizionsdk-cpp-installation) is a software development kit tailored for C++ developers. It is compatible with both Windows and Linux platforms and is specifically designed to support TechNexion cameras across various environments, including Windows10/11, Ubuntu, NXP-i.MX, NVIDIA-Jetson, and so on. It provides the methods to easily control AR Series Cameras for companies and individuals looking to develop custom applications.

Additionally, we offer [pyvizionsdk](/docs/vision-software/vizionsdk/python/pyvizionsdk-installation) for Python developers. This version provides Python bindings derived from the original C++ implementation of the VizionSDK and supports multiple Python versions across different platforms.

## Supported Cameras

| **TEVS Series** | **UVC Series** | **FPD-Link III Series** | **GMSL2 Series** |
| :---: | :---: | :---: | :---: |
| <ProductList series="TEVS"/> | <ProductList series="VCI"/> | <ProductList series="VLS3"/> | <ProductList series="VLS_GM2"/> |

## Supported Platforms

|<img src="/img/windows-logo.png" width="150px" alt="Windows Logo"/>| <img src="/img/ubuntu-logo.png" width="150px" alt="Ubuntu Logo"/> | <img src="/img/nxp-logo.png" width="150px" alt="NXP logo"/>| <img src="/img/nvidia-jetson-image.png" width="150px" alt="NVIDIA Jetson Image"/>| <img src="/img/upsquared-logo.png" width="150px" alt="UP Squared Logo"/>|
|:---:|:---:|:---:|:---:|:---:|
|Windows 10/11| Ubuntu| NXP i.MX8MP<br/>NXP i.MX8MM<br/>NXP i.MX93<br/>NXP i.MX95| JetPack 6.x| UP Squared Pro 7000|

## Supported Programming Languages

|<a href="/docs/vision-software/vizionsdk/cplusplus/vizionsdk-cpp-installation.md"><img src="/img/cplusplus-logo.png" width="150px" alt="C++ Logo"/></a>|<a href="/docs/vision-software/vizionsdk/python/pyvizionsdk-installation.md"><img src="/img/python-logo.png" width="150px" alt="Python Logo"/></a>|
|:---:|:---:|
|C++| Python 3.8<br/>Python 3.9<br/>Python 3.10<br/>Python 3.11<br/>Python 3.12<br/>Python 3.13|

## Integrations

|OpenCV|
|:---:|
|<a href="/docs/vision-software/vizionsdk/integrations/opencv-with-vizionsdk.md"><img src="/img/opencv-logo.png" width="100px" alt="OpenCV Logo"/></a>|
