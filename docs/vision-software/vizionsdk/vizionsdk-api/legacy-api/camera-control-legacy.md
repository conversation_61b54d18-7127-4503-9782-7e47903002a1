---
title: "Camera Control"
description: "Camera Control function (Legacy API ~v25.03.1)"
---
## Introduction

These functions are responsible for controlling various parameters of the camera.

### VcGetAutoExposureMode & VcSetAutoExposureMode

**Function:**

```cpp
int VcGetAutoExposureMode(VizionCam *vizion_cam, AE_MODE_STATUS &ae_mode)
int VcSetAutoExposureMode(VizionCam *vizion_cam, AE_MODE_STATUS ae_mode) 
```
**Function Description:**

* The **VcGetAutoExposureMode**() function is used to get the current auto exposure mode status of the VizionCam camera
* The **VcSetAutoExposureMode**() function is used to set the auto exposure mode of the camera.

**Parameter Description:**

* The first parameter of both functions is a pointer to the VizionCam object representing the camera.
* The second parameter of **VcGetAutoExposureMode()**  is a reference to an **AE_MODE_STATUS**  variable that is updated with the current auto exposure mode status of the camera.
* The second parameter of **VcSetAutoExposureMode()**  is an **AE_MODE_STATUS**  variable that represents the desired auto exposure mode to be set.
* return 0 = PASS, return -1 = FAIL.

    ```cpp
    enum class AE_MODE_STATUS{
        MANUAL_EXP = 0,
        AUTO_GAIN = 0x9,
        AUTO_EXP = 0xC,
    };
    ```
**Explanation of Modes:**

* **MANUAL_EXP**  
Allows manual control over exposure time and gain settings.

* **AUTO_GAIN**  
Automatically adjusts the camera's gain to maintain brightness while allowing manual control of the exposure time. This helps keep FPS stable in varying light conditions.

* **AUTO_EXP**  
Automatically adjusts both exposure time and gain to ensure balanced exposure across different lighting conditions.

**Example:**
```cpp
AE_MODE_STATUS ae_mode;
ae_mode = AE_MODE_STATUS::MANUAL_EXP;
VcSetAutoExposureMode(vizion_cam, ae_mode);
VcGetAutoExposureMode(vizion_cam, ae_mode);
if (ae_mode == AE_MODE_STATUS::AUTO_EXP) {
    printf("Get AE Mode: AUTO_EXP\n");
} else if (ae_mode == AE_MODE_STATUS::MANUAL_EXP) {
    printf("Get AE Mode: MANUAL_EXP\n");
} else if (ae_mode == AE_MODE_STATUS::AUTO_GAIN) {
    printf("Get AE Mode: AUTO_GAIN\n");
} else {
    printf("Get AE Mode: Unknown Mode\n");
}
```

### VcGetExposureTime & VcSetExposureTime

**Function:**

```cpp
int VcGetExposureTime(VizionCam* vizion_cam, uint32_t& exptime)
int VcSetExposureTime(VizionCam* vizion_cam, uint32_t exptime)
```
**Function Description:**

* **VcGetExposureTime**() retrieves the current exposure time of the camera and stores it in the exptime variable passed as a reference.
* **VcSetExposureTime**() sets the exposure time of the camera to the specified value in microseconds.

**Parameter Description:**

* The VizionCam pointer vizion_cam must be initialized and connected to a camera device before calling these functions.
* The **exposure time**  must be a value between 1 and 500000 microseconds.
* The **exptime parameter**  in **VcGetExposureTime**() must be a reference to a uint32_t variable that can store the retrieved exposure time value.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint32_t exp_time = 0;
VcSetExposureTime(vizion_cam, 13333); // Set exp time 5000 us.  Range: 1 ~ 500000 us
VcGetExposureTime(vizion_cam, exp_time);
printf("Get AE Exposure Time: %d us\n", exp_time);
```

### VcGetExposureGain & VcSetExposureGain

**Function:**

```cpp
int VcSetExposureGain(VizionCam *vizion_cam, uint8_t expgain)
int VcGetExposureGain(VizionCam *vizion_cam, uint8_t &expgain)
```
**Function Description:**

* The **VcSetExposureGain**  function is used to set the camera's exposure gain value.
* The **exposure gain**  controls the amplification of the signal that the camera receives, allowing the camera to adjust the brightness of the image.
* The **VcGetExposureGain**  function, on the other hand, retrieves the current exposure gain value of the camera.

**Parameter Description:**

* Both functions take the VizionCam object and a parameter for the exposure gain value as input.
* The **VcSetExposureGain**  function takes an unsigned integer value between 1 and 64, representing the exposure gain multiplied by 1x to 64x.
* The **VcGetExposureGain**  function takes an unsigned integer reference as input, which will store the retrieved exposure gain value.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint8_t exp_gain = 0;
VcSetExposureGain(vizion_cam, 3); // Set exp gain 3x.   Range: 1x ~ 64x
VcGetExposureGain(vizion_cam, exp_gain);
printf("Get Exp Gain: %d\n", exp_gain);
```

### VcGetExposureMinimum & VcSetExposureMinimum

**Function:**

```cpp
int VcGetExposureMinimum(VizionCam *vizion_cam, uint32_t &exptime)
int VcSetExposureMinimum(VizionCam *vizion_cam, uint32_t exptime) 
```
**Function Description:**

* **VcGetExposureMinimum**  retrieves the minimum exposure time value in Auto Exposure mode.
* **VcSetExposureMinimum**  sets the minimum exposure time value in Auto Exposure mode.
* These functions are only effective when the camera is in Auto Exposure mode, used to limit the range of automatic exposure adjustment.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **exptime** : The exptime parameter is in microseconds (us) and valid range is from 1 to 1000000 microseconds.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint32_t min_exp_time = 0;
VcSetExposureMinimum(vizion_cam, 1000); // Set minimum exposure time to 1000 us
VcGetExposureMinimum(vizion_cam, min_exp_time);
printf("Get AE Minimum Exposure Time: %d us\n", min_exp_time);
```

### VcGetExposureMaximum & VcSetExposureMaximum

**Function:**

```cpp
int VcGetExposureMaximum(VizionCam *vizion_cam, uint32_t &exptime)
int VcSetExposureMaximum(VizionCam *vizion_cam, uint32_t exptime)
```
**Function Description:**

* **VcGetExposureMaximum**  retrieves the maximum exposure time value in Auto Exposure mode.
* **VcSetExposureMaximum**  sets the maximum exposure time value in Auto Exposure mode.
* These functions are only effective when the camera is in Auto Exposure mode, used to limit the range of automatic exposure adjustment.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **exptime** : The exptime parameter is in microseconds (us) and valid range is from 1 to 1000000 microseconds.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint32_t max_exp_time = 0;
VcSetExposureMaximum(vizion_cam, 100000); // Set maximum exposure time to 100000 us
VcGetExposureMaximum(vizion_cam, max_exp_time);
printf("Get AE Maximum Exposure Time: %d us\n", max_exp_time);
```

### VcGetCurrentExposureTime

**Function:**

```cpp
int VcGetCurrentExposureTime(VizionCam *vizion_cam, uint32_t &exptime)
```
**Function Description:**

* **VcGetCurrentExposureTime**  retrieves the current actual exposure time value when the camera is in Auto Exposure mode.
* Unlike **GetExposureTime** , this function returns the actual exposure value being used by the camera in Auto mode, rather than the manually set value.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **exptime** : The exptime parameter is in microseconds (us) and valid range is from 1 to 1000000 microseconds.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint32_t current_exp_time = 0;
VcGetCurrentExposureTime(vizion_cam, current_exp_time);
printf("Current Auto Exposure Time: %d us\n", current_exp_time);
```

### VcGetCurrentExposureGain

**Function:**

```cpp
int VcGetCurrentExposureGain(VizionCam *vizion_cam, uint8_t &expgain) 
```
**Function Description:**

* **VcGetCurrentExposureGain**  retrieves the current actual exposure gain value when the camera is in Auto Exposure mode.
* Unlike **GetExposureGain** , this function returns the actual gain value being used by the camera in Auto mode, rather than the manually set value.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **exptime** : Valid range is from 1 to 64.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint8_t current_gain = 0;
VcGetCurrentExposureGain(vizion_cam, current_gain);
printf("Current Auto Exposure Gain: %dx\n", current_gain);
```

### VcGetMaxFPS & VcSetMaxFPS

**Function:**

```cpp
int VcSetMaxFPS(VizionCam *vizion_cam, uint8_t max_fps)
int VcGetMaxFPS(VizionCam *vizion_cam, uint8_t &max_fps) 
```
**Function Description:**

* The **VcSetMaxFPS** and **VcGetMaxFPS** APIs allow the user to set and retrieve the maximum frame rate supported by the camera.
* The maximum frame rate represents the upper limit for the number of frames that can be captured per second.

**Parameter Description:**

* The **VcSetMaxFPS**  API takes two parameters: a pointer to a VizionCam structure and the maximum frame rate value in frames per second (fps) that needs to be set. The fps value should be between 1 and 255.
* The **VcGetMaxFPS**  API also takes two parameters: a pointer to a VizionCam structure and a reference to an unsigned 8-bit integer variable. Upon successful execution, the maximum frame rate value is stored in the variable referenced by the second parameter.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint8_t max_fps = 0;
max_fps = 120;
VcSetMaxFPS(vizion_cam, max_fps); // Set max fps
VcGetMaxFPS(vizion_cam, max_fps);
printf("Get Max FPS: %d\n", max_fps);
```

### VcGetAutoWhiteBalanceMode & VcSetAutoWhiteBalanceMode

**Function** :

```cpp
int VcGetAutoWhiteBalanceMode(VizionCam *vizion_cam, AWB_MODE_STATUS &awb_mode)
int VcSetAutoWhiteBalanceMode(VizionCam *vizion_cam, AWB_MODE_STATUS awb_mode)
```
**Function Description:**

* The **VcGetAutoWhiteBalanceMode**  function gets the current automatic white balance mode of the camera, which can be one of the following options:
      
    * **MANUAL_TEMPERATURE_WB:** Manual white balance using color temperature.
    * **AUTO_WB:** Automatic white balance.

* The **VcSetAutoWhiteBalanceMode** function sets the automatic white balance mode of the camera to the specified value.

**Parameter Description:**

* **vizion_cam** : A pointer to a VizionCam object representing the camera.
* **awb_mode** : A reference to an AWB_MODE_STATUS variable that will hold the white balance mode. This parameter is both an input and an output parameter.
* return 0 = PASS, return -1 = FAIL.

    ```cpp
    enum class AWB_MODE_STATUS{ 
    MANUAL_TEMPERATURE_WB = 0x7, 
    AUTO_WB = 0xF,
    }; 
    ```

**Example:**
```cpp
AWB_MODE_STATUS awb_mode;
awb_mode = AWB_MODE_STATUS::MANUAL_TEMPERATURE_WB;
VcSetAutoWhiteBalanceMode(vizion_cam, awb_mode);
VcGetAutoWhiteBalanceMode(vizion_cam, awb_mode);
```

### VcGetTemperature & VcSetTemperature

**Function:**

```cpp
int VcSetTemperature(VizionCam *vizion_cam, uint16_t temp)
int VcGetTemperature(VizionCam *vizion_cam, uint16_t &temp)
```

**Function Description:**

* The **VcSetTemperature**  function is used to set the white balance temperature of a VizionCam. The function takes a pointer to a VizionCam object and the desired temperature in Kelvin as input parameters. The function sets the white balance temperature of the camera to the specified value.
* The **VcGetTemperature**  function is used to get the current white balance temperature of a VizionCam. The function takes a pointer to a VizionCam object and a reference to a variable that will store the temperature as input parameters. The function returns the current white balance temperature of the camera.

**Parameter Description:**

* **vizion_cam** : A pointer to a VizionCam object.
* **temp** : The desired white balance temperature in Kelvin. Valid range is 2300K to 15000K.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint16_t wb_temp = 0;
VcSetTemperature(vizion_cam, 6500);  // Set wb temperature 6500.   Range: 2300 ~ 15000K
VcGetTemperature(vizion_cam, wb_temp);
printf("Get WB Temperature: %d\n", wb_temp);
```

### VcGetFlipMode & VcSetFlipMode

**Function:**

```cpp
int VcGetFlipMode(VizionCam* vizion_cam, FLIP_MODE& flip)
int VcSetFlipMode(VizionCam* vizion_cam, FLIP_MODE flip)
```

**Function Description:**

* The **VcGetFlipMode**  function retrieves the current flip mode of a VizionCam instance and stores the result in the **FLIP_MODE**  variable passed as an argument.
* The **VcSetFlipMode**  function sets the flip mode of a VizionCam instance to the **FLIP_MODE**  passed as an argument.

**Parameter Description:**

* **vizion_cam** : a pointer to a VizionCam instance.
* **flip** : a reference to a FLIP_MODE variable.
* The **VcGetFlipMode**  function stores the current flip mode in this variable
* The **VcSetFlipMode**  function sets the flip mode to the value passed in this variable.
* **FLIP_MODE** : an enumeration that defines the flip modes supported by the VizionCam library.
* return 0 = PASS, return -1 = FAIL.

  ```cpp
  enum class FLIP_MODE{
  FLIP_NORMAL = 0, 
  FLIP_H_MIRROR,
  FLIP_V_MIRROR,
  FLIP_ROTATE_180,
  };
  ```

**Example:**
```cpp
// Set the flip mode to vertical mirror.
VcSetFlipMode(vizion_cam, FLIP_V_MIRROR);
// Get the current flip mode.
FLIP_MODE flip_mode;
VcGetFlipMode(vizion_cam, flip_mode);
printf("Current flip mode: %d\n", (int)flip_mode);
```

### VcGetEffectMode & VcSetEffectMode

**Function** :

```cpp
int VcGetEffectMode(VizionCam* vizion_cam, EFFECT_MODE& effect)
int VcSetEffectMode(VizionCam* vizion_cam, EFFECT_MODE effect)
```
**Function Description:**

* The **VcGetEffectMode**() function gets the current effect mode of the camera, while the VcSetEffectMode() function sets the effect mode of the camera.
* The **effect mode**  can be used to change the appearance of the camera image, such as making it black and white or negative.

**Parameter Description:**

* **vizion_cam** : a pointer to the VizionCam object representing the camera
* effect: a reference to the **EFFECT_MODE**  enum that will be used to get or set the effect mode
* return 0 = PASS, return -1 = FAIL.

    ```cpp
    enum class EFFECT_MODE{
    NORMAL_MODE = 0x00,
    BLACK_WHITE_MODE= 0x03,
    GRAYSCALE_MODE = 0x06, 
    NEGATIVE_MODE = 0x07, 
    SKETCH_MODE = 0x0F,
    }; 
    ```

**Example:**
```cpp
EFFECT_MODE effect_mode = EFFECT_MODE::GRAYSCALE_MODE;
VcSetEffectMode(vizion_cam, effect_mode);
VcGetEffectMode(vizion_cam, effect_mode);
printf("Current Effect Mode: %d\n", effect_mode);
```

### VcGetGamma & VcSetGamma

**Function:**

```cpp
int VcGetGamma(VizionCam* vizion_cam, double& gamma)
int VcSetGamma(VizionCam* vizion_cam, double gamma)
```

**Function Description:**

* The **VcGetGamma**  and **VcSetGamma**  functions are used to get and set the gamma correction value for the VizionCam. Gamma correction is a technique used to adjust the brightness levels of an image to compensate for differences in the way that cameras capture light compared to how the human eye perceives it.
* A **gamma**  value of 1.0 is considered "linear", meaning that the brightness values in the image are represented exactly as they were captured by the camera. A gamma value greater than 1.0 will result in darker shadows and brighter highlights, while a gamma value less than 1.0 will result in brighter shadows and darker highlights.

**Parameter Description:**

* **vizion_cam** : A pointer to a VizionCam object that represents the camera to which the gamma value will be applied.
* **gamma** : A double-precision floating point value that represents the desired gamma correction value. The range of valid values is from 0.0 to 2.5, with a default value of 1.0.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double gamma = 0.0;
VcSetGamma(vizion_cam, 1.5);  // Set gamma 1.5   Range: 0.0 ~ 2.5
VcGetGamma(vizion_cam, gamma);
printf("Get Gamma: %.4f\n", gamma);
```

### VcGetSaturation & VcSetSaturation

**Function:**

```cpp
int VcGetSaturation(VizionCam* vizion_cam, double& saturation)
int VcSetSaturation(VizionCam* vizion_cam, double saturation)
```
**Function Description:**

* The functions **VcGetSaturation**  and **VcSetSaturation**  are used to get and set the saturation level of the image captured by the VizionCam.

**Parameter Description:**

* **vizion_cam** : a pointer to the VizionCam object.
* **saturation** : a reference to a variable to store/get the saturation level. The value of saturation ranges from 0.0 to 2.0, with a default value of 1.0.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double saturation = 0.0;
VcSetSaturation(vizion_cam, 1.5);  // Set Saturatoin 1.5   Range: 0.0 ~ 2.0
VcGetSaturation(vizion_cam, saturation);
printf("Get Saturation: %.4f\n", saturation);
```

### VcGetContrast & VcSetContrast

**Function:**

```cpp
int VcGetContrast(VizionCam* vizion_cam, double& contrast)
int VcSetContrast(VizionCam* vizion_cam, double contrast)
```

**Function Description:**

* **VcGetContrast** : Get the current contrast value of the camera.  
* **VcSetContrast** : Set the contrast value of the camera.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.  
* **contrast** : A reference to a double variable that holds the contrast value.Range: -5.0 to 5.0.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double contrast = 0.0;
VcSetContrast(vizion_cam, -2.0);
VcGetContrast(vizion_cam, contrast);
printf("Get Contrast: %.4f\n", contrast);
```

### VcGetSharpening & VcSetSharpening

**Function:**

```cpp
int VcGetSharpening(VizionCam* vizion_cam, double& sharpness)
int VcSetSharpening(VizionCam* vizion_cam, double sharpness) 
```

**Function Description:**

* The **VcGetSharpening**  function retrieves the current sharpness level of the VizionCam, while the VcSetSharpening function sets the sharpness level of the VizionCam.
* Sharpness is a measure of the clarity of details in an image. Increasing sharpness enhances the edge contrast of the image, making it appear clearer and more defined, while decreasing sharpness can have the opposite effect, making the image appear softer and less detailed.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.  
      **sharpness** : A double-precision floating-point variable that holds the current or desired sharpness level.
* The valid range of sharpness values is from -2.0 to 2.0, where negative values decrease sharpness and positive values increase it.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double sharpening = 0.0;
VcSetSharpening(vizion_cam, -1.5);  // Set Sharpening 1.5   Range: -2.0 ~ 2.0
VcGetSharpening(vizion_cam, sharpening);
printf("Get harpening: %.4f\n", sharpening);
```

### VcGetDenoise & VcSetDenoise

**Function:**

```cpp
int VcGetDenoise(VizionCam* vizion_cam, double& denoise)
int VcSetDenoise(VizionCam* vizion_cam, double denoise)
```

**Function Description:**  

* The functions **VcGetDenoise**  and **VcSetDenoise**  are used to get and set the level of denoising applied to the image captured by the VizionCam device.

**Parameter Description:**  

* **vizion_cam** : A pointer to a VizionCam object representing the camera device.  
* **denoise** : A reference to a double variable that will be used to get or set the level of denoising. The value is in the range of -2.0 to 2.0, where a negative value indicates that the denoising effect is applied to the image.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double denoise = 0.0;
VcSetDenoise(vizion_cam, -1.5);  // Set Denoise 1.5   Range: -2.0 ~ 2.0
VcGetDenoise(vizion_cam, denoise);
printf("Get Denoise: %.4f\n", denoise);
```

### VcGetDigitalZoomType & VcSetDigitalZoomType

**Function:**

```cpp
int VcGetDigitalZoomType(VizionCam* vizion_cam, DZ_MODE_STATUS& zoom_type)
int VcSetDigitalZoomType(VizionCam* vizion_cam, DZ_MODE_STATUS zoom_type)
```

**Function Description:**

* These functions are used to get and set the digital zoom mode of a VizionCam object. The digital zoom mode can be set to either **DZ_FAST**  or **DZ_SMOOTH**.

**Parameter Description:**

* **vizion_cam** : a pointer to the VizionCam object  
* **zoom_type** : a reference to a **DZ_MODE_STATUS**  variable, which is an enumeration type representing the digital zoom mode. It can be either **DZ_FAST**  or **DZ_SMOOTH**.  
* return 0 = PASS, return -1 =FAIL.  
* **DZ_MODE_STATUS:**

    ```cpp
    enum class DZ_MODE_STATUS
    {
    DZ_IMMEDIATE = 0x8000,
    DZ_SLOW = 0x0040,
    DZ_FAST = 0x0200,
    };
    ```

**Example:**
```cpp
DZ_MODE_STATUS dz_mode;
dz_mode = DZ_MODE_STATUS::DZ_FAST;
VcSetDigitalZoomType(vzcam, dz_mode);
VcGetDigitalZoomType(vzcam, dz_mode);
```

### VcGetDigitalZoomTarget&VcSetDigitalZoomTarget

**Function:**

```cpp
int VcGetDigitalZoomTarget(VizionCam* vizion_cam, double& times)
int VcSetDigitalZoomTarget(VizionCam* vizion_cam, double times)
```

**Function Description:**

* The functions **VcGetDigitalZoomTarget**() and **VcSetDigitalZoomTarget**() are used to get and set the digital zoom target factor, respectively. The digital zoom target factor is the factor by which the camera image is zoomed in, i.e., the target zoom factor.

**Parameter Description:**

* **VizionCam** * vizion_cam: Pointer to a VizionCam object that represents the camera.  
* **times** : Reference to a double variable that will be used to store the digital zoom target factor.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double dz_tgt;
VcGetDigitalZoomTarget(vizion_cam, dz_tgt);
printf("Get Digital Zoom Target: %f ms\n", dz_tgt);
```

### VcGetDigitalZoom_CT_X & VcGetDigitalZoom_CT_Y & VcSetDigitalZoom_CT_X & VcSetDigitalZoom_CT_Y

**Function:**

```cpp
int VcGetDigitalZoom_CT_X(VizionCam* vizion_cam, double& ct_x)
int VcSetDigitalZoom_CT_X(VizionCam* vizion_cam, double ct_x)
int VcGetDigitalZoom_CT_Y(VizionCam* vizion_cam, double& ct_y)
int VcSetDigitalZoom_CT_Y(VizionCam* vizion_cam, double ct_y)
```

**Function Description:** 

* **VcGetDigitalZoom_CT_X** : This function is used to get the X coordinate of the center point of the digital zoom window.  
* **VcSetDigitalZoom_CT_X** : This function is used to set the X coordinate of the center point of the digital zoom window.  
* **VcGetDigitalZoom_CT_Y** : This function is used to get the Y coordinate of the center point of the digital zoom window.  
* **VcSetDigitalZoom_CT_Y** : This function is used to set the Y coordinate of the center point of the digital zoom window.

**Parameter Description:** 

* **vizion_cam** : A pointer to a VizionCam structure that represents the camera device.  
* **ct_x** : A reference to a double variable that will be used to store the X coordinate of the center point of the digital zoom window.  
* **ct_y** : A reference to a double variable that will be used to store the Y coordinate of the center point of the digital zoom window.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
double dz_ct_x, dz_ct_y;
VcGetDigitalZoom_CT_X(vizion_cam, dz_ct_x);
printf("Get Digital Zoom CT_X: %f\n", dz_ct_x);
VcGetDigitalZoom_CT_Y(vizion_cam, dz_ct_y);
printf("Get Digital Zoom CT_Y: %f\n", dz_ct_y);
VcSetDigitalZoom_CT_X(vizion_cam, 0.5);
VcSetDigitalZoom_CT_Y(vizion_cam, 0.5); 
```

### VcGetBacklightCompensation & VcSetBacklightCompensation

**Function:**

```cpp
int VcGetBacklightCompensation(VizionCam* vizion_cam, double& ae_comp)
int VcSetBacklightCompensation(VizionCam* vizion_cam, double ae_comp)
```

**Function Description:**

* **VcGetBacklightCompensation** :This function retrieves the current backlight compensation setting of a VizionCam device. Backlight compensation is a feature that adjusts the exposure in scenes where the background is significantly brighter than the subject of the image, thus preventing the subject from appearing in shadow.  
* **VcSetBacklightCompensation** : This function sets the backlight compensation level for a VizionCam device. Adjusting this setting can help to balance the exposure in challenging lighting conditions, ensuring that the subject of the image is neither too dark nor overexposed.

**Parameter Description:** 

* **vizion_cam** : A pointer to the VizionCam object.
* **ae_comp** : A reference to a double variable to store the backlight compensation value.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
// Get the current backlight compensation value
double ae_comp;
VcGetBacklightCompensation(vizion_cam, ae_comp);
printf("Backlight compensation value: %.2f\n", ae_comp);
// Set the backlight compensation value to 1.5
VcSetBacklightCompensation(vizion_cam, 1.5);
```

### VcGetJpegQual & VcSetJpegQual

**Function:**

```cpp
int VcGetJpegQual(VizionCam *vizion_cam, uint8_t &qual);
int VcSetJpegQual(VizionCam *vizion_cam, uint8_t qual);
```

**Function Description:**

* **VcGetJpegQual** : Retrieves the current JPEG quality setting of the camera and stores it in the qual variable passed as a reference.  
* **VcSetJpegQual** : Sets the JPEG quality of the camera to the specified value.

**Parameter Description:** 
* **vizion_cam** : A pointer to the VizionCam object.  
* **qual** : A reference to a uint8_t variable to store the JPEG quality value.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
// Get the current JPEG quality value
uint8_t qual = 0;
VcGetJpegQual(vizion_cam, qual);
printf("Backlight compensation value: %.d\n", qual);
// Set the JPEG quality value to 80
VcSetJpegQual(vizion_cam, 80);
```

### VcGetThroughPut & VcSetThroughPut

**Function:**

```cpp
int VcGetThroughput(VizionCam *vizion_cam, uint16_t &throughput);
int VcSetThroughput(VizionCam *vizion_cam, uint16_t throughput);
```

**Function Description:** 

* **VcGetThroughPut** : Retrieves the current throughput setting of the camera and stores it in the throughput variable passed as a reference.  
* **VcSetThroughPut** : Sets the throughput of the camera to the specified value.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.  
* **throughput** : A reference to a uint16_t variable to store the throughput value.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
// Get the current throughput value
uint16_t throughput = 0;
VcGetThroughPut(vizion_cam, throughput);
printf("Throughput value: %.d\n", throughput);
// Set the throughput value to 100
VcSetThroughPut(vizion_cam, 100);
```

### VcRecoverDefaultSetting

**Function:**

```cpp
int VcRecoverDefaultSetting(VizionCam* vizion_cam)
```

**Function Description:**

* The function **VcRecoverDefaultSetting**() is used to recover the default settings of the camera device.  
* This function resets all camera settings to their default values.

**Parameter Description:**

* The VizionCam* vizion_cam parameter is a pointer to the camera device that needs to recover its default settings.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
// Recover the default settings of the camera device
VcRecoverDefaultSetting(vizion_cam);
```

### VcLoadProfileSettingFromPath

**Function:**

```cpp
int VcLoadProfileSettingFromPath(VizionCam* vizion_cam, const char* profile_path) 
```

**Function Description:** 

* The **VcLoadProfileSettingFromPath**  function is used to load camera settings from a **JSON**  file located at a specified path and apply those settings to the camera.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.  
* **profile_path** : The path of the JSON file containing the profile settings.  
  3. return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
const char* profile_path = "/usr/local/bin/Profile.json";
std::ifstream file(profile_path);
VcLoadProfileSettingFromPath(vizion_cam, profile_path);  
```

### VcLoadProfileSetting

**Function:**

```cpp
int VcLoadProfileSetting(VizionCam* vizion_cam, const char* profile_string) 
```

**Function Description:**

* The function **VcLoadProfileSetting**  loads camera settings from a JSON-formatted string and applies them to the specified VizionCam instance.

**Parameter Description:**  

* **vizion_cam** : A pointer to the VizionCam instance to which the settings should be applied.  
* **profile_string** : A pointer to a null-terminated string containing the JSON-formatted camera settings to be loaded.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
const char* profile_path = "/usr/local/bin/Profile.json";
std::ifstream file(profile_path);
if (!file.is_open()) {
      std::cerr << "Failed to open file: " << profile_path << std::endl;
      return -1;
  }
std::string profile_str((std::istreambuf_iterator<char>(file)),
std::istreambuf_iterator<char>());
std::cout << "profile_str.c_str():::" << profile_str.c_str() << std::endl;
int ret = VcLoadProfileSetting(vizion_cam, profile_str.c_str());
if (ret != 0) {
      std::cerr << "Failed to load profile from string" << std::endl;
      return -1;
}
```

### VcSetProfileControlConfig

**Function:**

```cpp
int VcSetProfileControlConfig(VizionCam* vizion_cam)
```

**Function Description:**

* The **VcSetProfileControlConfig**  function is used to apply the camera control configuration settings stored in the current profile to the connected VizionCam object.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object that has been initialized and connected to a physical camera device.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
    :::info
    To use the **VcSetProfileControlConfig**  function, you must first load the profile settings using either **VcLoadProfileSetting**  or **VcLoadProfileSettingFromPath**. Once the profile settings have been loaded, call the **VcSetProfileControlConfig**  function to apply the settings to the connected VizionCam object.
    :::
```cpp
VcSetProfileControlConfig(vizion_cam);
```

### VcGotoSaveOSPProfile

**Function:**

```cpp
int VcGotoSaveOSPProfile(VizionCam *vizion_cam, uint8_t check)  
```

**Function Description:**

* The **VcGotoSaveOSPProfile**  function is to store the current ISP parameters into EEPROM.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object that has been initialized and connected to a physical camera device.  
* **check** : Profile check flag.

      * **0** : Disable 
      * **1** : Enable, but don't write OSP 
      * **2** : Enable, write OSP by controller

* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint8_t  check = 2 ;
VcGotoSaveOSPProfile(vizion_cam, check);
```

### VcGetOSPProfileConfig

**Function:**

```cpp
int VcGetOSPProfileConfig(VizionCam *vizion_cam, std::string &profile_str)
```

**Function Description:** 

* The **VcGetOSPProfileConfig**  function is used to retrieves the OSP profile configuration and saves it to the provided string.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object that has been initialized and connected to a physical camera device.  
* **profile_str** : a reference to a string where the profile configuration will be saved.  
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
std::string profile_str;
VcGetOSPProfileConfig(vizion_cam, profile_str);
```

### VcCheckOSPProfile

**Function:**

```cpp
int VcCheckOSPProfile(VizionCam *vizion_cam)
```

**Function Description:** 

* The **VcCheckOSPProfile**  function is used to Retrieve the value of profile check flag.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object that has been initialized and connected to a physical camera device.  
* return 0 = Disable, return 1 = Enable, but don't write OSP, return 2 = Enable, write OSP by controller, return -1 = FAIL.

**Example:**
```cpp
int profile_check;
profile_check = VcCheckOSPProfile(vizion_cam);
```

### VcReadTimeStamp

**Function:**

```cpp
int VcReadTimeStamp(VizionCam *vizion_cam, uint32_t &frameCount, uint32_t &timeStamp)
```

**Function Description:**

* The ReadTimeStamp function retrieves the frame count and timestamp from the camera using a UVC control query.

    :::info
    The timestamp may not represent the exact moment the frame is captured.
    :::

**Parameter Description:**

* **vizion_cam**: A pointer to the VizionCam object that has been initialized and connected to a physical camera device.
* **frameCount**: A reference variable that stores the retrieved frame count.
* **timeStamp**: A reference variable that stores the retrieved timestamp.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
uint32_t frameCount, timeStamp;
VcReadTimeStamp(vizion_cam, frameCount, timeStamp);
```