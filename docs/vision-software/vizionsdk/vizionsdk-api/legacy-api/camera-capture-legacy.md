---
title: "Camera Capture"
description: "Camera Capture function (Legacy API ~v25.03.1)"
---
## Introduction

These functions are responsible for capturing images from a camera.

### VcCreateVizionCamDevice

**Function:**

```cpp
VizionCam* VcCreateVizionCamDevice()
```
**Function Description:**

* This function is used to create an object of the **VizionCam** class.
* This function allocates memory for a new **VizionCam** object and returns a pointer to it.

**Parameter Description:**

* This function does not take any input parameters.

**Example:**

```cpp
VizionCam* vizion_cam; 
vizion_cam=VcCreateVizionCamDevice();
```

### VcGetVideoDeviceList

**Function:**

```cpp
int VcGetVideoDeviceList(VizionCam* vizion_cam, std::vector<std::wstring>& devname_list)
```
**Function Description:**

* This function retrieves a list of available video devices and their names, and stores the device names in a vector of wide strings.

    :::warning
    This function needs to be called before using **VcOpen** to initialize the VizionCam parameters
    :::

**Parameter Description:**

* **vizion_cam** : A pointer to a **VizionCam**  instance.
* **devname_list** : A vector of wide strings to store the retrieved device names.
* return 0 = PASS, return -1 = FAIL.

**Example:**

```cpp
// Declare a vector to store the device names
std::vector<std::wstring> devname_list;
// Call the function to retrieve the device names and store them in the vector
VcGetVideoDeviceList(vizion_cam, devname_list);
```

### VcOpen

**Function:**

```cpp
int VcOpen(VizionCam* vizion_cam, int dev_idx)
```
**Function Description:**
* This function is used to open a VizionCam device specified by the index in the device list obtained by calling VcGetVideoDeviceList().

    :::warning
    This function needs to be called before using any functional program except for **VcGetVideoDeviceList**.
    :::

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **list_idx** : An integer specifying the index of the device to be opened in the device list.
* return 0 = PASS, return -1 = FAIL.

**Example:**

```cpp
VcOpen(vizion_cam, 0);
```

### VcClose

**Function:** 

```cpp
int VcClose(VizionCam *vizion_cam)
```
**Function Description:**

* This function is used to close the camera device and release any allocated resources.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object representing the camera device to be closed.
* return 0 = PASS, return -1 = FAIL.

***Example:**

```cpp
VcClose(vizion_cam); // Close the camera device represented by the VizionCam object.
```

### VcGetCaptureFormatList

**Function:**

```cpp
int VcGetCaptureFormatList(VizionCam *vizion_cam, std::vector<VzFormat> &capformats)
```
**Function Description:**

* This function is used to retrieve the available capture formats of a VizionCam device.

    :::warning
    To retrieve the available capture formats of a VizionCam device, first create a vector of **VzFormat** structures, and then call **VcGetCaptureFormatList**  function passing in the VizionCam object and the vector as parameters
    :::

**Parameter Description:**

* **vizion_cam** : A pointer to a VizionCam device object.
* **capformats** : A vector of **VzFormat**  structures. The function will populate this vector with the available capture formats.
* **VzFormat**  `structures{uint16_t width; uint16_t height; uint16_t framerate};`
* return 0 = PASS, return -1 = FAIL.
* **VZ_IMAGE_FORMAT**  format:

    ```cpp
    enum VZ_IMAGE_FORMAT {
        YUY2,
        UYVY,
        NV12,
        MJPG,
      };
    ```
**Example:**

```cpp
std::vector<VzFormat> capfmtlist;
VcGetCaptureFormatList(vizion_cam, capfmtlist); // Get the capture formats list
```

### VcSetCaptureFormat

**Function:**

```cpp
int VcSetCaptureFormat(VizionCam *vizion_cam, VzFormat capformat)
```
**Function Description:**

* The VcSetCaptureFormat function sets the capture format of the camera to the specified format.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **format** : The capture format to be set. The format must be obtained from the **VcGetCaptureFormatList** function
* return 0 = PASS, return -1 = FAIL.

**Example:**

```cpp
VzFormat format = capfmtlist[0];
VcSetCaptureFormat(vizion_cam, format); // Set the capture format
```

### VcGetRawImageCapture

**Function:**

```cpp
int VcGetRawImageCapture(VizionCam *vizion_cam, uint8_t *raw_data, int *data_size, uint16_t timeout=2500)
```
**Function Description:**
* This function is used to get a single frame of raw image data. To capture multiple frames, the function should be repeatedly called.

    :::warning
    This function call requires calling **VcSetCaptureFormat** first.
    :::

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam object.
* **raw_data** : A pointer to a buffer that stores the raw image data.
* **data_size** : A pointer to an integer that stores the size of the raw image data.
* **timeout** (Option) : The timeout value in milliseconds, default value is 2500 ms.
* return 0 = PASS, return -1 = FAIL.

**Return Values:**

* **VZ_SUCCESS** = 0
* **VZ_TIMEOUT** = -1
* **VZ_CAM_OCCUPIED** = -2
* **VZ_FAIL** = -3
* **VZ_OTHER_ERROR** = -4
* **VZ_BUFFER_CORRUPTED** = -5
* **VZ_CAPTURE_FORMAT_ERROR** = -6

**Example:**

```cpp
uint8_t* img_data = new uint8_t[format.width * format.height * 3];
int data_size=0;
VcGetRawImageCapture(vizion_cam, img_data, &data_size, 2500);
```

### VcGetVizionCamDeviceName

**Function:**

```cpp
int VcGetVizionCamDeviceName(VizionCam *vizion_cam, wchar_t *devname)
```
**Function Description:**

* The function retrieves the device name of the specified VizionCam device.
* It is necessary to call **VcOpen()** before calling this function.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam instance.
* **devname** : A wide character buffer to store the device name string.
* return 0 = PASS, return -1 = FAIL.

**Example:**

```cpp
wchar_t  wchstr[256];
VcGetVizionCamDeviceName(vizion_cam, wchstr);
std::wcout << L"Device Name: " << std::wstring(wchstr) << std::endl;
```

### VcGetUSBFirmwareVersion

**Function:**

```cpp
int VcGetUSBFirmwareVersion(VizionCam* vizion_cam, char* fw_ver)
```
**Function Description:**

* This function retrieves the firmware version of the connected USB camera.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam structure obtained by calling VcOpen().
* **fw_ver** : A character array to store the firmware version. The array must have a minimum length of 16 bytes.
* return 0 = PASS, return -1 = FAIL.

**Example:**

```cpp
char fw_ver[16];
VcGetUSBFirmwareVersion(vizion_cam, fw_ver);
std::cout << "fw_ver ::"<< fw_ver << std::endl;
```

### VcGetUniqueSensorID

**Function:**

```cpp
int VcGetUniqueSensorID(VizionCam* vizion_cam, char* sensor_id);
```

**Function Description:**

* The function VcGetUniqueSensorID retrieves the unique sensor ID of the VizionCam device.

**Parameter Description:**

* **vizion_cam** : A pointer to the VizionCam instance obtained through VcOpen function.
* **sensor_id** : A pointer to a character array that receives the unique sensor ID of the VizionCam device.
* return 0 = PASS, return -1 = FAIL.

**Example:**
```cpp
char UID[24];
VcGetUniqueSensorID(vizion_cam, UID);
```