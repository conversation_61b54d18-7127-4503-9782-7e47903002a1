---
title: "Camera Information"
description: "Camera Information Setting"
sidebar_position: 3
---
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

These functions are retrieving the information from the camera device.

### VxGetUSBFirmwareVersion

**Function:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
int VxGetUSBFirmwareVersion(std::shared_ptr<VxCamera> vxcam, std::string& fwVer)
```
</TabItem>
<TabItem value="py" label="Python">

```py
def VxGetUSBFirmwareVersion(vxcam)
```

</TabItem>
</Tabs>

**Function Description:**

* This function retrieves the firmware version of the connected USB camera. 

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **fwVer** : A string to store the firmware version.
* Return:
   * **C++** : return 0 = PASS, return -1 = FAIL
   * **Python** : tuple(return_code, fwVer)
      > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
std::string fwVer;
// get the version of USB device firmware
VxGetUSBFirmwareVersion(vxcam, fwVer);
```
</TabItem>
<TabItem value="py" label="Python">

```py
# get the version of USB device firmware
result, fwVer = pyvizionsdk.VxGetUSBFirmwareVersion(vxcam)
print("USB firmware version:", fwVer)
print("Return code:", result)
```

</TabItem>
</Tabs>

### VxGetTEVSFirmwareVersion

**Function:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
int VxGetTEVSFirmwareVersion(std::shared_ptr<VxCamera> vxcam, std::string& fwVer)
```
</TabItem>
<TabItem value="py" label="Python">

```py
def VxGetTEVSFirmwareVersion(vxcam)
```

</TabItem>
</Tabs>


**Function Description:**

* This function retrieves the firmware version of the connected TEVS camera. 

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **fwVer** : A string to store the firmware version.
* Return:
   * **C++** : return 0 = PASS, return -1 = FAIL
   * **Python** : tuple(return_code, fwVer)
      > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
std::string fwVer;
// get the version of TEVS device firmware
VxGetTEVSFirmwareVersion(vxcam, fwVer);
```
</TabItem>
<TabItem value="py" label="Python">

```py
# get the version of TEVS device firmware
result, fwVer = pyvizionsdk.VxGetTEVSFirmwareVersion(vxcam)
print("TEVS firmware version:", fwVer)
print("Return code:", result)
```

</TabItem>
</Tabs>

### VxGetSensorUniqueID

**Function:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
int VxGetSensorUniqueID(std::shared_ptr<VxCamera> vxcam, std::string& uniqueId)
```
</TabItem>
<TabItem value="py" label="Python">

```py
def VxGetSensorUniqueID(vxcam)
```

</TabItem>
</Tabs>

**Function Description:**

* This function is used to retrieve the unique ID from the sensor.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **uniqueId** : A string to store the sensor ID.
* Return:
   * **C++** : return 0 = PASS, return -1 = FAIL
   * **Python** : tuple(return_code, uniqueId)
      > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
std::string uniqueId;
// get the sensor id
VxGetSensorUniqueID(vxcam, uniqueId);
```
</TabItem>
<TabItem value="py" label="Python">

```py
# get the sensor id
result, uniqueId = pyvizionsdk.VxGetSensorUniqueID(vxcam)
print("Sensor unique ID:", uniqueId)
print("Return code:", result)
```

</TabItem>
</Tabs>

### VxGetSensorFirmwareVersion

**Function:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
int VxGetSensorFirmwareVersion(std::shared_ptr<VxCamera> vxcam, std::string& fwVer)
```
</TabItem>
<TabItem value="py" label="Python">

```py
def VxGetSensorFirmwareVersion(vxcam)
```

</TabItem>
</Tabs>

**Function Description:**

* This function is used to retrieve the firmware version from the sensor.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **fwVer** : A string to store the firmware version.
* Return:
   * **C++** : return 0 = PASS, return -1 = FAIL
   * **Python** : tuple(return_code, fwVer)
      > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
<TabItem value="c++" label="C++">

```cpp
std::string fwVer;
// get the firmware version
VxGetSensorFirmwareVersion(vxcam, fwVer);
```
</TabItem>
<TabItem value="py" label="Python">

```py
# get the sensor id
result, fwVer = pyvizionsdk.VxGetSensorFirmwareVersion(vxcam)
print("Sensor firmware version:", fwVer)
print("Return code:", result)
```

</TabItem>
</Tabs>