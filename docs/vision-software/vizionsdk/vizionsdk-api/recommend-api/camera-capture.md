---
title: "Camera Capture"
description: "Camera Capture Setting"
sidebar_position: 2
---
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

These functions are responsible for capturing images from a camera.

### VxDiscoverCameraDevice

**Function:**
<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxDiscoverCameraDevices(std::vector<std::string>& devList)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxDiscoverCameraDevices()
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This Function retrieves a list of available camera devices and stores the device names in a vector of wide strings.

**Parameter Description:**

* **devList** : A vector of wide strings to store the retrieved devices.
* **Return:**
  * **C++** : devList.size()
  * **Python** : tuple(devList.size(), devList)

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // declare the vector to save the list of devices
  std::vector<std::string> dev_list;
  // discover the devices
  VxDiscoverCameraDevices(dev_list);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # discover the devices
  result, camera_list = pyvinzionsdk.VxDiscoverCameraDevices()
  print("Return code:", result)
  print("Discovered cameras:", camera_list)

  # print camera_list
  for camera in camera_list:
      print(camera)
  ```

  </TabItem>
</Tabs>

### VxInitialCameraDevice

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  std::shared_ptr<VxCamera> VxInitialCameraDevice(int devIdx)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxInitialCameraDevice(devIdx)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function initializes a **VxCamera** instance by selecting an index from the device list retrieved from **VxDiscoverCameraDevice()**.
* It returns a shared pointer to the created **VxCamera** object, enabling shared ownership and automatic memory management.

**Parameter Description:**

* **devIdx** : An integer specifying the index of the device to be opened in the device list.
* return a shared pointer to the initialized device, return nullptr = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int devIdx;
  // initial the camera to obtain the pointer point to camera device
  VxInitialCameraDevice(devIdx);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # initial the camera of index 0
  vxcam = pyvizionsdk.VxInitialCameraDevice(0)
  ```

  </TabItem>
</Tabs>

### VxOpen

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxOpen(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxOpen(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to open a device obtained by calling VxInitialCameraDevice().

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // initial the camera to obtain the pointer point to the camera device
  auto vxcam = VxInitialCameraDevice(dev_idx);
  // open the camera
  VxOpen(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # open camera
  pyvinzionsdk.VxOpen(vxcam)
  ```

  </TabItem>
</Tabs>

### VxClose

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxClose(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxClose(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to close a device obtained by calling VxInitialCameraDevice().

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // close the camera
  VxClose(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # close the camera
  pyvizionsdk.VxClose(vxcam)
  ```

  </TabItem>
</Tabs>

### VxIsVizionCamera

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxIsVizionCamera(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxIsVizionCamera(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* Verify whether the VxCamera device is **TechNexion UVC** or **MIPI** product or not. 

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // check the type of camera
  VxIsVizionCamera(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # check the type of camera
  pyvizionsdk.VxIsVizionCamera(vxcam)
  ```

  </TabItem>
</Tabs>

### VxGetDeviceName

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetDeviceName(std::shared_ptr<VxCamera> vxcam, std::string& deviceName)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetDeviceName(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to obtain the device name.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **deviceName** : A reference of string variable to store the device name.
* Return: 
  * **C++** : return 0 = PASS, return -1 = FAIL.
  * **Python** : tuple(return_code, deviceName)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  std::string deviceName;
  // get the device name of camera
  VxGetDeviceName(vxcam, deviceName);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the device name of camer
  result, name = pyvizionsdk.VxGetDeviceName(vxcam)
  print("Device name:", name)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetDeviceInterfaceType

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetDeviceInterfaceType(std::shared_ptr<VxCamera> vxcam, VX_CAMERA_INTERFACE_TYPE& type);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetDeviceInterfaceType(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to obtain the type of device interface.
* The device interface type is set as **VX_CAMERA_INTERFACE_TYPE** format.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **type** : A **VX_CAMERA_INTERFACE_TYPE** format variable, which is used to store the interface type.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, type)
    > **return_code** : return 0 = PASS, return -1 = FAIL
  
* **VX_CAMERA_INTERFACE_TYPE** format:

  <Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    enum class VX_CAMERA_INTERFACE_TYPE {
          INTERFACE_USB = 0,
          INTERFACE_MIPI_CSI2,
          INTERFACE_ETHERNET,
      };
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    class VX_CAMERA_INTERFACE_TYPE(Enum):
          INTERFACE_USB = 0
          INTERFACE_MIPI_CSI2 = 1
          INTERFACE_ETHERNET = 2
    ```

    </TabItem>
  </Tabs>

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  VX_CAMERA_INTERFACE_TYPE type;
  // get the device interface type of camera
  VxGetDeviceInterfaceType(vxcam, type);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  result, tyname = pyvizionsdk.VxGetDeviceInterfaceType(vxcam)
  print("Device Interface type name:", tyname)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetHardwareID

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetHardwareID(std::shared_ptr<VxCamera> vxcam, std::string& hwId)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetHardwareID(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to obtain the hardware ID of the device.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **hwId** : A reference of string variable to store the hardware ID.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, hwId)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  std::string hwId;
  // get the hardware ID of camera
  VxGetHardwareID(vxcam, hwId);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the hardware ID of camera
  result, hwId = pyvizionsdk.VxGetHardwareID(vxcam)
  print("Hardware ID:", hwId)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetUSBDeviceSpeed

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetUSBDeviceSpeed(std::shared_ptr<VxCamera> vxcam, VX_USB_DEVICE_SPEED& speed)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetUSBDeviceSpeed(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to obtain the speed of the USB device. 
* The speed is set as **VX_USB_DEVICE_SPEED** format.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **speed** : A reference of **VX_USB_DEVICE_SPEED** format variable to store the speed.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, speed)
    > **return_code** : return 0 = PASS, return -1 = FAIL

* **VX_USB_DEVICE_SPEED** format:

  <Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    enum class VX_USB_DEVICE_SPEED {
          USB_DEVICE_SPEED_LOW,
          USB_DEVICE_SPEED_FULL,
          USB_DEVICE_SPEED_HIGH,
          USB_DEVICE_SPEED_SUPER,
          USB_DEVICE_SPEED_SUPER_PLUS,
          USB_DEVICE_SPEED_UNKNOWN
      };
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    class VX_USB_DEVICE_SPEED(Enum):
          USB_DEVICE_SPEED_LOW = 0
          USB_DEVICE_SPEED_FULL = 1
          USB_DEVICE_SPEED_HIGH = 2
          USB_DEVICE_SPEED_SUPER = 3
          USB_DEVICE_SPEED_SUPER_PLUS = 4
          USB_DEVICE_SPEED_UNKNOWN = 5
    ```

    </TabItem>
  </Tabs>

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  VX_USB_DEVICE_SPEED speed;
  // get the hardware ID of camera
  VxGetUSBDeviceSpeed(vxcam, speed);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  result, speed = pyvizionsdk.VxGetUSBDeviceSpeed(vxcam)
  print("USB device speed:", speed)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetFormatList

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetFormatList(std::shared_ptr<VxCamera> vxcam, std::vector<VxFormat>& fmtList)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetFormatList(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to obtain the list of formats from the VxCamera device.
    :::info
    To retrieve the available formats of a VxCamera device, first create a vector of VxFormat structures, and then call **VxGetFormatList** function passing in the VxCamera object and the vector as parameters.
    :::

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **fmtList** : A vector of **VxFormat** structures. The function will populate this vector with the available formats.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, fmtList)
    > **return_code** : return 0 = PASS, return -1 = FAIL

* **VxFormat** structures:

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  struct VxFormat {
          uint8_t mediatypeIdx;
          uint16_t width;
          uint16_t height;
          uint16_t framerate;
          VX_IMAGE_FORMAT format;
      };
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  class VxFormat:
          def __init__(self, width, height, framerate, format):
              self.width = width
              self.height = height
              self.framerate = framerate
              self.format = format
  ```

  </TabItem>
</Tabs>


* **VX_IMAGE_FORMAT** format:

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  enum class VX_IMAGE_FORMAT {
        NONE = 0,
        YUY2,
        UYVY,
        NV12,
        MJPG,
    };
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  class VX_IMAGE_FORMAT(Enum):
        VX_IMAGE_FORMAT_NONE = 0
        VX_IMAGE_FORMAT_YUY2 = 1
        VX_IMAGE_FORMAT_UYVY = 2
        VX_IMAGE_FORMAT_NV12 = 3
        VX_IMAGE_FORMAT_MJPG = 4
  ```

  </TabItem>
</Tabs>

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  std::vector<VxFormat> fmt_list;
  // get the list of formats from the camera
  VxGetFormatList(vxcam, fmt_list);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the list of format
  result, format_list = pyvizionsdk.VxGetFormatList(vxcam)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxSetFormat

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxSetFormat(std::shared_ptr<VxCamera> vxcam, VxFormat fmt)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetFormat(vxcam, fmt)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to set the specified format to the VxCamera device. 

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **fmt** : A **VxFormat** format to be set. The format must be obtained from the**** VxGetFormatList().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // get format list
  std::vector<VxFormat> fmt_list;
  VxGetFormatList(cam, fmt_list);
  // set the format
  VxSetFormat(vxcam, fmt_list[0]);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get format
  result, format_list = pyvizionsdk.VxGetFormatList(camera)
  # set format
  result = pyvizionsdk.VxSetFormat(camera, format_list[0])
  print("Set format return code:", result)
  ```

  </TabItem>
</Tabs>

### VxStartStreaming

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxStartStreaming(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxStartStreaming(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for starting streaming on the device.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // start streaming the camera
  VxStartStreaming(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # start streaming the camera
  pyvizionsdk.VxStartStreaming(vxcam)
  ```

  </TabItem>
</Tabs>

### VxStopStreaming

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxStopStreaming(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxStopStreaming(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for stopping streaming on the VxCamera device.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // stop streaming the camera
  VxStopStreaming(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # stop streaming the camera
  pyvizionsdk.VxStopStreaming(vxcam)
  ```

  </TabItem>
</Tabs>

### VxGetImage

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  VX_CAPTURE_RESULT VxGetImage(std::shared_ptr<VxCamera> vxcam,
                                  uint8_t* data,
                                  int* dataSize,
                                  uint16_t timeout)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetImage(vxcam, timeout, fmt)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to capture the image from the VxCamera device.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **data** : A pointer to the buffer that stores the raw image data.
* **dataSize** : A pointer to an integer that stores the size of the raw image data.
* **timeout** : The timeout value in milliseconds.
* **fmt** : A **VxFormat** format to be set.

**Return Values:**

  * **C++:**
    * VX_SUCCESS = 0
    * VX_TIMEOUT = -1
    * VX_CAM_OCCUPIED = -2
    * VX_OTHER_ERROR = -3
    * VX_BUFFER_CORRUPTED = -4

  * **Python:**
    * tuple(return_code, img)
      > **return_code** : return value same as C++  
      > **img** is stored in a numpy array

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // get format list
  std::vector<VxFormat> fmt_list;
  VxGetFormatList(cam, fmt_list);
  // get mjpg format
  VxFormat mjpg_fmt;
  for (auto fmt : fmt_list) {
      // find MJPG format
      if (fmt.format == VX_IMAGE_FORMAT::MJPG) {
          mjpg_fmt = fmt;
      }
  }

  // set the MJPG format
  VxSetFormat(cam, mjpg_fmt);
  // start streaming
  VxStartStreaming(cam);

  // get the MJPG format image
  uint8_t* raw_data = new uint8_t[3840 * 2160 * 2];
  int raw_size = 0;
  VxGetImage(cam, raw_data, &raw_size, 2500);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get format
  result, format_list = pyvizionsdk.VxGetFormatList(camera)
  mjpg_format = None
  for format in format_list:
      # get mjpg format
      if format.format == VX_IMAGE_FORMAT.VX_IMAGE_FORMAT_MJPG:
          mjpg_format = format

  # set format
  result = pyvizionsdk.VxSetFormat(camera, mjpg_format)

  # start streaming
  result = pyvizionsdk.VxStartStreaming(camera)

  # get the MJPG format image
  result, image = pyvizionsdk.VxGetImage(camera, 1000, mjpg_format)
  ```

  </TabItem>
</Tabs>

### VxGetImageFd
:::warning
This function is only supported in **C++** and works only when the image buffer is allocated using a **DMA buffer**.
:::

**Function:**

```cpp
VX_CAPTURE_RESULT VxGetImageFd(std::shared_ptr<VxCamera> vxcam,
                                                int* dmaBufferFd,
                                                uint16_t timeout)
```

**Function Description:**

* This function is used to capture the image from the device and store it in a DMA buffer.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **dmaBufferFd** : A pointer to DMA buffer file descriptor that stores the raw image data.
* **timeout** : The timeout value in milliseconds.

**Return Values:**

  * **VX_SUCCESS** = 0
  * **VX_TIMEOUT** = -1
  * **VX_CAM_OCCUPIED** = -2
  * **VX_OTHER_ERROR** = -3
  * **VX_BUFFER_CORRUPTED** = -4

**Example:**

```cpp
int dmaBufferFd;
uint16_t timeout;
// get the image to DMA buffer
VxGetImageFd(vxcam, dmaBufferFd, timeout);
```

### VxReleaseImageFd
:::warning
This function is only supported in **C++** and works only when the image buffer is allocated using a **DMA buffer**.
:::

**Function:**

```cpp
int VxReleaseImageFd(std::shared_ptr<VxCamera> vxcam, int dmaBufferFd)
```

**Function Description:**

* This function is used to release the image stored in a DMA buffer.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **dmaBufferFd** : A DMA buffer file descriptor that stores the raw image data.
* return 0 = PASS, return -1 = FAIL.

**Example:**

```cpp
int dmaBufferFd;
// release the image from the DMA buffer
VxReleaseImageFd(vxcam, dmaBufferFd);
```