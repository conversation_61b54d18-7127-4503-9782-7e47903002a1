---
title: "Log File Setting"
description: "VizionSDK Log setting"
sidebar_position: 1
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

These functions are responsible for setting the log from a camera.

### HelloVizionSDK

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  void HelloVizionSDK()
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def HelloVizionSDK()
  ```

  </TabItem>
</Tabs>

**Function Description:**
    
* This function is used to get the version of the VizionSDK from the log.

**Parameter Description:**

* This function does not take any input parameters.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  HelloVizionSDK();
  // Hello VizionSDK!
  // Version: {VizionSDK Version}
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  pyvizionsdk.HelloVizionSDK()
  # Hello VizionSDK!
  # Version: {VizionSDK Version}
  ```

  </TabItem>
</Tabs>

### VxSetLogFile

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxSetLogFile(const std::string& folderPath)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetLogFile(folderPath)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to set the destination path where to save the log file.

**Parameter Description:**

* **folderPath** : A string constants of folder path.

* Return : return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  const std::string folderPath;
  // set the destination path of log file
  VxSetLogFile(folderPath);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  folderPath = "/saveFolder"
  # set the destination path of log file
  pyvizionsdk.VxSetLogFile(folderPath)
  ```

  </TabItem>
</Tabs>

### VxSetLogLevel

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  void VxSetLogLevel(VX_LOG_LEVEL level);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetLogLevel(level)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for setting the log level, determining the verbosity of log messages.

**Parameter Description:**

* **level** : The parameter is a **VX_LOG_LEVEL** variable that represents the level of the log.

  :::info
  Logging levels follow an order from low to high, meaning that setting a specific level enables logging for that level and all higher severity levels.
  :::

* **VX_LOG_LEVEL** format:

  <Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    // Ordered from Low to High
    enum class VX_LOG_LEVEL {
        VX_LOG_LEVEL_TRACE,
        VX_LOG_LEVEL_DEBUG,
        VX_LOG_LEVEL_INFO,
        VX_LOG_LEVEL_WARN,
        VX_LOG_LEVEL_ERROR,
        VX_LOG_LEVEL_CRITICAL,
        VX_LOG_LEVEL_OFF,
    };
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    # Ordered from Low to High
    class VX_LOG_LEVEL(Enum):    
        VX_LOG_LEVEL_TRACE = 0
        VX_LOG_LEVEL_DEBUG = 1
        VX_LOG_LEVEL_INFO = 2
        VX_LOG_LEVEL_WARN = 3
        VX_LOG_LEVEL_ERROR = 4
        VX_LOG_LEVEL_CRITICAL = 5
        VX_LOG_LEVEL_OFF = 6
    ```

    </TabItem>
  </Tabs>

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // Set the log level to information level
  VxSetLogLevel(VX_LOG_LEVEL::VX_LOG_LEVEL_INFO);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_LOG_LEVEL
  # Set the log level to information level
  pyvinzionsdk.VxSetLogLevel(VX_LOG_LEVEL.VX_LOG_LEVEL_INFO)
  ```

  </TabItem>
</Tabs>

**Explanation of Levels(Ordered from Low to High):**

  * **`VX_LOG_LEVEL_TRACE`** : Provides the most detailed logging, capturing step-by-step execution flow. 
  * **`VX_LOG_LEVEL_DEBUG`** : Includes debugging information for developers, offering insights into internal operations and variable states.
  * **`VX_LOG_LEVEL_INFO`** **(Default)** : Logs general operational events, such as initialization, configuration changes, and so on.
  * **`VX_LOG_LEVEL_WARN`** : Highlights potential issues that do not immediately impact functionality.
  * **`VX_LOG_LEVEL_ERROR`** : Captures critical issues that disrupt specific functionalities but do not cause system-wide failure.
  * **`VX_LOG_LEVEL_CRITICAL`** : Logs severe errors that may lead to system or application failure, requiring urgent attention.
  * **`VX_LOG_LEVEL_OFF`** : Disables all logging, preventing any messages from being recorded.