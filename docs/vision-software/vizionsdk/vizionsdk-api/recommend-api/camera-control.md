---
title: "Camera Control"
description: "Camera Control Setting"
sidebar_position: 4
---
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

These functions are responsible for controlling various parameters of the camera.

### VxResetUVC

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxResetUVC(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxResetUVC(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for resetting the UVC device.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // reset the UVC device
  VxResetUVC(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # reset the UVC device
  pyvizionsdk.VxResetUVC(vxcam)
  ```

  </TabItem>
</Tabs>

### VxGetUVCImageProcessingRange

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetUVCImageProcessingRange(std::shared_ptr<VxCamera> vxcam,
                                      VX_UVC_IMAGE_PROPERTIES propId,
                                      long& min,
                                      long& max,
                                      long& step,
                                      long& def)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetUVCImageProcessingRange(vxcam, propId)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the UVC device to get the range of image processing parameters.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **propId** : A **VX_UVC_IMAGE_PROPERTIES** format UVC image processing property for which the range is being queried.
* **min** : A long type variable to store the minimum allowable value for the property.
* **max** : A long type variable to store the maximum allowable value for the property.
* **step** : A long type variable to store the step value for the property.
* **def** : A long type variable to store the default value for the property.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, min, max, step, def)
    > **return_code** : return 0 = PASS, return -1 = FAIL

* **VX_UVC_IMAGE_PROPERTIES** format:

  <Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    enum class VX_UVC_IMAGE_PROPERTIES {
            UVC_IMAGE_BRIGHTNESS,
            UVC_IMAGE_CONTRAST,
            UVC_IMAGE_HUE,
            UVC_IMAGE_SATURATION,
            UVC_IMAGE_SHARPNESS,
            UVC_IMAGE_GAMMA,
            UVC_IMAGE_COLORENABLE,
            UVC_IMAGE_WHITEBALANCE,
            UVC_IMAGE_BACKLIGHT_COMPENSATION,
            UVC_IMAGE_GAIN,
            UVC_IMAGE_PAN,
            UVC_IMAGE_TILT,
            UVC_IMAGE_ROLL,
            UVC_IMAGE_ZOOM,
            UVC_IMAGE_EXPOSURE,
            UVC_IMAGE_IRIS,
            UVC_IMAGE_FOCUS,
            UVC_IMAGE_PROP_MAX
        };
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    class VX_UVC_IMAGE_PROPERTIES(Enum):
            UVC_IMAGE_BRIGHTNESS = 0
            UVC_IMAGE_CONTRAST = 1
            UVC_IMAGE_HUE = 2
            UVC_IMAGE_SATURATION = 3
            UVC_IMAGE_SHARPNESS = 4
            UVC_IMAGE_GAMMA = 5
            UVC_IMAGE_COLORENABLE = 6
            UVC_IMAGE_WHITEBALANCE = 7
            UVC_IMAGE_BACKLIGHT_COMPENSATION = 8
            UVC_IMAGE_GAIN = 9
            UVC_IMAGE_PAN = 10
            UVC_IMAGE_TILT = 11
            UVC_IMAGE_ROLL = 12
            UVC_IMAGE_ZOOM = 13
            UVC_IMAGE_EXPOSURE = 14
            UVC_IMAGE_IRIS = 15
            UVC_IMAGE_FOCUS = 16
            UVC_IMAGE_PROP_MAX = 17
    ```

    </TabItem>
  </Tabs>

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  long value = 0, min = 0, max = 0, step = 0, def = 0;
  auto propId = static_cast<VX_UVC_IMAGE_PROPERTIES>(0);
  // get the UVC image processing range
  VxGetUVCImageProcessingRange(vxcam, propId, min, max, step, def);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_UVC_IMAGE_PROPERTIES
  # get the UVC image processing range
  result, brightness_min, brightness_max, brightness_step, brightness_def = pyvizionsdk.VxGetUVCImageProcessingRange(vxcam, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS)
  print("UVC brightness range:", brightness_min, brightness_max, brightness_step, brightness_def)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetUVCImageProcessing

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetUVCImageProcessing(std::shared_ptr<VxCamera> vxcam,
                                  VX_UVC_IMAGE_PROPERTIES propId,
                                  long& value,
                                  int& flag)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetUVCImageProcessing(vxcam, propId)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the UVC device to get the image processing parameters’ current value and the flag to indicate whether the property is in auto mode or not.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **propId** : A **VX_UVC_IMAGE_PROPERTIES** format UVC image processing property for which being queried.
* **value** : A long type variable to store the current value of the property.
* **flag** : An int type variable to indicate whether the property is read only or not. If the flag is 1, it means the property is in auto mode for read-only.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, value, flag)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  long value = 0;
  int flag = 0;
  auto propId = static_cast<VX_UVC_IMAGE_PROPERTIES>(0);
  // get the UVC image processing
  VxGetUVCImageProcessing(vxcam, propId, value, flag);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_UVC_IMAGE_PROPERTIES
  # get the UVC image processing
  result, brightness, flag = pyvizionsdk.VxGetUVCImageProcessing(vxcam, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS)
  print("UVC brightness:", brightness)
  print("Flag:", flag)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxSetUVCImageProcessing

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxSetUVCImageProcessing(std::shared_ptr<VxCamera> vxcam,
                                  VX_UVC_IMAGE_PROPERTIES propId,
                                  long value,
                                  int flag)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetUVCImageProcessing(vxcam, propId, value, flag)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the UVC device to set image processing parameters value and the flag.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **propId** : A **VX_UVC_IMAGE_PROPERTIES** format UVC image processing property for which being queried.
* **value** : A long type variable to set the value of the property.
* **flag** : A int type variable to indicate whether the property is read only or not. If the flag is 1, it means the property is in auto mode for read-only.
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // set the brightness to 16
  VxsetUVCImageProcessing(vxcam, VX_UVC_IMAGE_PROPERTIES::UVC_IMAGE_BRIGHTNESS, 16, 0);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_UVC_IMAGE_PROPERTIES
  # set the UVC image processing
  result = pyvizionsdk.VxSetUVCImageProcessing(vxcam, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS, 12, 0)
  print("Set UVC brightness return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetISPImageProcessingRange

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetISPImageProcessingRange(std::shared_ptr<VxCamera> vxcam,
                                      VX_ISP_IMAGE_PROPERTIES propId,
                                      VxISPImageProcessingValue& min,
                                      VxISPImageProcessingValue& max,
                                      VxISPImageProcessingValue& step,
                                      VxISPImageProcessingValue& def)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetISPImageProcessingRange(vxcam, propId)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the ISP device to get the range of image processing parameters.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **propId** : A **VX_ISP_IMAGE_PROPERTIES** format ISP image processing property for which the range is being queried.
* **min** : A long type variable to store the minimum allowable value for the property.
* **max** : A long type variable to store the maximum allowable value for the property.
* **step** : A long type variable to store the step value for the property.
* **def** : A long type variable to store the default value for the property.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, min, max, step, def)
    > **return_code** : return 0 = PASS, return -1 = FAIL

* **VX_ISP_IMAGE_PROPERTIES** format:

  <Tabs groupId="api-code">
    <TabItem value="c++" label="C++">

    ```cpp
    enum class VX_ISP_IMAGE_PROPERTIES {
        ISP_IMAGE_BRIGHTNESS,
        ISP_IMAGE_CONTRAST,
        ISP_IMAGE_SATURATION,
        ISP_IMAGE_WHITEBALANCE_MODE,
        ISP_IMAGE_WHITEBALANCE_TEMPERATURE,
        ISP_IMAGE_EXPOSURE_MODE,
        ISP_IMAGE_EXPOSURE_TIME,
        ISP_IMAGE_EXPOSURE_MIN_TIME,
        ISP_IMAGE_EXPOSURE_MAX_TIME,
        ISP_IMAGE_EXPOSURE_GAIN,
        ISP_IMAGE_GAMMA,
        ISP_IMAGE_SHARPNESS,
        ISP_IMAGE_BACKLIGHT_COMPENSATION,
        ISP_IMAGE_SPECIAL_EFFECT_MODE,
        ISP_IMAGE_DENOISE,
        ISP_IMAGE_FLIP_MODE,
        ISP_IMAGE_PAN,
        ISP_IMAGE_TILT,
        ISP_IMAGE_ZOOM,
        ISP_IMAGE_FLICK_MODE,
        ISP_IMAGE_JPEG_QUALITY,
        ISP_IMAGE_PROP_MAX
      };
    ```
    </TabItem>
    <TabItem value="py" label="Python">

    ```py
    class VX_ISP_IMAGE_PROPERTIES(Enum):
        ISP_IMAGE_BRIGHTNESS = 0
        ISP_IMAGE_CONTRAST = 1
        ISP_IMAGE_SATURATION = 2
        ISP_IMAGE_WHITEBALANCE_MODE = 3
        ISP_IMAGE_WHITEBALANCE_TEMPERATURE = 4
        ISP_IMAGE_EXPOSURE_MODE = 5
        ISP_IMAGE_EXPOSURE_TIME = 6
        ISP_IMAGE_EXPOSURE_MIN_TIME = 7
        ISP_IMAGE_EXPOSURE_MAX_TIME = 8
        ISP_IMAGE_EXPOSURE_GAIN = 9
        ISP_IMAGE_GAMMA = 10
        ISP_IMAGE_SHARPNESS = 11
        ISP_IMAGE_BACKLIGHT_COMPENSATION = 12
        ISP_IMAGE_SPECIAL_EFFECT_MODE = 13
        ISP_IMAGE_DENOISE = 14
        ISP_IMAGE_FLIP_MODE = 15
        ISP_IMAGE_PAN = 16
        ISP_IMAGE_TILT = 17
        ISP_IMAGE_ZOOM = 18
        ISP_IMAGE_FLICK_MODE = 19
        ISP_IMAGE_JPEG_QUALITY = 20
        ISP_IMAGE_PROP_MAX = 21
    ```

    </TabItem>
  </Tabs>

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  long value = 0, min = 0, max = 0, step = 0, def = 0;
  auto propId = static_cast<VX_ISP_IMAGE_PROPERTIES>(0);
  // get the ISP image processing range
  VxGetISPImageProcessingRange(vxcam, propId, min, max, step, def);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_ISP_IMAGE_PROPERTIES
  # get the ISP image processing range
  result, brightness_min, brightness_max, brightness_step, brightness_def = pyvizionsdk.VxGetISPImageProcessingRange(vxcam, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS)
  print("ISP brightness range:", brightness_min, brightness_max, brightness_step, brightness_def)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetISPImageProcessing

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetISPImageProcessing(std::shared_ptr<VxCamera> vxcam,
                                  VX_ISP_IMAGE_PROPERTIES propId,
                                  VxISPImageProcessingValue& value,
                                  int& flag)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetISPImageProcessing(vxcam, propId)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the ISP device to get an image processing parameter value and the flag.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **propId** : A **VX_ISP_IMAGE_PROPERTIES** format ISP image processing property for which being queried.
* **value** : A **VxISPImageProcessingValue** format variable to store the current value of the property.
* **flag** : An int type variable to indicate whether the property is read only or not. If the flag is 1, it means the property is read-only.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, value, flag)
    > **return_code** : return 0 = PASS, return -1 = FAIL

* **VxISPImageProcessingValue** format
  ```cpp
  using VxISPImageProcessingValue = std::variant<int, double>;
  ```

**Example:**
<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  auto propId = static_cast<VX_ISP_IMAGE_PROPERTIES>(0);
  VxISPImageProcessingValue value;
  int flag = 0;
  // get the ISP image processing value and flag
  VxGetISPImageProcessing(vxcam, propId, value, flag);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_ISP_IMAGE_PROPERTIES
  # get the ISP image processing value and flag
  result, brightness, flag = pyvizionsdk.VxGetISPImageProcessing(vxcam, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS)
  print("ISP brightness:", brightness)
  print("Flag:", flag)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxSetISPImageProcessing

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxSetISPImageProcessing(std::shared_ptr<VxCamera> vxcam,
                                  VX_ISP_IMAGE_PROPERTIES propId,
                                  int value)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetISPImageProcessing(vxcam, propId, value)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the ISP device to set an image processing parameter value.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **propId** : A **VX_ISP_IMAGE_PROPERTIES** format ISP image processing property for which being queried.
* **value** : A int variable to set the value of the property.
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // set the brightness to 16
  VxSetISPImageProcessing(vxcam, VX_ISP_IMAGE_PROPERTIES::ISP_IMAGE_BRIGHTNESS, 16);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_ISP_IMAGE_PROPERTIES
  # set the brightness to 16
  result = pyvizionsdk.VxSetISPImageProcessing(vxcam, VX_ISP_IMAGE_PROPERTIES.ISP_IMAGE_BRIGHTNESS, 16)
  print("Set ISP brightness return code:", result)
  ```

  </TabItem>
</Tabs>

### VxSetISPImageProcessingDefault

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxSetISPImageProcessingDefault(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetISPImageProcessingDefault(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the ISP device to set an image processing parameter to the default value.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // set the ISP image processing to default value
  VxSetISPImageProcessingDefault(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # set the ISP image processing to default value
  result = pyvizionsdk.VxSetISPImageProcessingDefault(vxcam)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetCurrentGain

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetCurrentGain(std::shared_ptr<VxCamera> vxcam, uint8_t& gain)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetCurrentGain(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the device to obtain the current exposure gain value.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **gain** : A reference of the uint8_t variable that stores the gain value. 
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, gain)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  uint8_t gain;
  // get the gain value
  VxGetCurrentGain(vxcam, gain);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the gain value
  result, gain = pyvizionsdk.VxGetCurrentGain(vxcam)
  print("Gain value:", gain)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetCurrentExposure

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetCurrentExposure(std::shared_ptr<VxCamera> vxcam, uint32_t& exp)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetCurrentExposure(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for the device to obtain the current exposure time value.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **exp** : A reference of the uint32_t variable which stored the exposure value. 
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, exp)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  uint32_t exp;
  // get the exposure time value
  VxGetCurrentGain(vxcam, exp);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the exposure value
  result, exp = pyvizionsdk.VxGetCurrentExposure(vxcam)
  print("Exposure value:", exp)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetMaxFPS & VxSetMaxFPS

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetMaxFPS(std::shared_ptr<VxCamera> vxcam, uint8_t& fps)
  int VxSetMaxFPS(std::shared_ptr<VxCamera> vxcam, uint8_t fps)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetMaxFPS(vxcam)
  def VxSetMaxFPS(vxcam, fps)
  ```

  </TabItem>
</Tabs>

**Function Description:**

  * **VxGetMaxFPS** : This function is for retrieving the maximum frame rate supported by the camera.
  * **VxSetMaxFPS** : This function is for the device to set the maximum frame rate

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **fps** : A reference of a uint8_t variable that will be used to get or set the maximum frame rate. 
* **VxGetMaxFPS** :
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : return tuple(return_code, fps)
    > return_code: return 0 = PASS, return -1 = FAIL

  * **VxSetMaxFPS** : return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  uint8_t max_fps;
  // get the maximum frame rate
  VxGetMaxFPS(vxcam, max_fps);
  // Set the frame rate to the maximum value
  VxSetMaxFPS(vxcam, max_fps);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the maximum frame rate
  result, max_fps = pyvizionsdk.VxGetMaxFPS(vxcam, max_fps)
  print("Max FPS:", max_fps)
  print("Return code:", result)
  # Set the frame rate to the maximum value
  result = pyvizionsdk.VxSetMaxFPS(vxcam, max_fps)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetThroughPut & VxSetThroughPut

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetThroughPut(std::shared_ptr<VxCamera> vxcam, uint16_t& throughPut)
  int VxSetThroughPut(std::shared_ptr<VxCamera> vxcam, uint16_t throughPut)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetThroughPut(vxcam)
  def VxSetThroughPut(vxcam, throughPut)
  ```

  </TabItem>
</Tabs>

**Function Description:**

  * **VxGetThroughPut** : This function is for retrieving the throughput value from the camera.
  * **VxSetThroughPut** : This function is for the device to set the throughput value.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **throughPut** : A uint16_t variable that will be used to get or set the throughput value. 
* **VxGetThroughPut** :
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : return tuple(return_code, throughPut)
    > **return_code** : return 0 = PASS, return -1 = FAIL

* **VxSetThroughPut** : return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  uint16_t throughPut;
  // get the throughPut
  VxGetThroughPut(vxcam, throughPut);
  // set the throughPut
  VxSetThroughPut(vxcam, throughPut);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the throughput value
  result, throughPut = pyvizionsdk.VxGetThroughPut(vxcam)
  print("ThroughPut:", throughPut)
  print("Return code:", result)
  # Set the throughput value
  result = pyvizionsdk.VxSetThroughPut(vxcam, throughPut)
  print("Return code:", result)
  ```

  </TabItem>
</Tabs>

### VxGetTimestamp & VxGetFrameCount

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetTimestamp(std::shared_ptr<VxCamera> vxcam, float& timestamp)
  int VxGetFrameCount(std::shared_ptr<VxCamera> vxcam, uint8_t& frameCount)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetTimestamp(vxcam)
  def VxGetFrameCount(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

  * **VxGetTimeStamp** : This function is for retrieving the timestamp value from the camera.
  * **VxGetFrameCount** : This function is for retrieving the frame count value from the camera.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **timestamp** : A float variable that will be used to get the timestamp value. 
* **frameCount** : A uint8_t variable that will be used to get the frame count value. 
* **VxGetTimestamp & VxGetFrameCount**:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : 
    1. **VxGetTimestamp** : return tuple(return_code, timestamp)
    2. **VxGetFrameCount** : return tuple(return_code, frameCount)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // get image
  uint8_t* raw_data = new uint8_t[3840 * 2160 * 2];
  int raw_size = 0;
  float timestamp;
  uint8_t framecnt;
  VxGetImage(vxcam, raw_data, &raw_size, 3000)
  
  // get the timestamp and frame count value after getting image
  VxGetTimestamp(vxcam, timestamp);
  VxGetFrameCount(vxcam, framecnt);
  std::cout << "TimeStamp: " << timestamp << std::endl;
  std::cout << "framecnt: " << framecnt << std::endl;
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get image
  result, format_list = pyvizionsdk.VxGetFormatList(vxcam)
  mjpg_format = None
  min_resolution = float('inf')
  for format in format_list:
      # get mjpg format and minimum resolution
      if format.format == VX_IMAGE_FORMAT.VX_IMAGE_FORMAT_MJPG:
          resolution = format.width * format.height
          if resolution < min_resolution:
              min_resolution = resolution
              mjpg_format = format
  result, image = pyvizionsdk.VxGetImage(vxcam, 1000, mjpg_format)
  
  # get the timestamp and frame count value after getting image
  result, timestamp = pyvizionsdk.VxGetTimestamp(vxcam)
  print("Timestamp: ", timestamp)
  result, frameCount = pyvizionsdk.VxGetFrameCount(vxcam)
  print("FrameCount: ", frameCount)
  ```

  </TabItem>
</Tabs>

### VxResetTimestamp & VxResetFrameCount
:::warning
These reset functions must be used when the camera is **not** in **standby** mode.
:::

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxResetTimestamp(std::shared_ptr<VxCamera> vxcam)
  int VxResetFrameCount(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxResetTimestamp(vxcam)
  def VxResetFrameCount(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

  * **VxResetTimeStamp** : This function is for resetting the timestamp value from the camera.
  * **VxResetFrameCount** : This function is for resetting the frame count value from the camera.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **VxResetTimestamp & VxResetFrameCount**: return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // start streaming
  VxStartStreaming(vxcam);
  // reset the timestamp and frame count
  VxResetTimestamp(vxcam);
  VxResetFrameCount(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # start streaming
  pyvizionsdk.VxStartStreaming(vxcam)
  # reset the timestamp and frame count
  pyvizionsdk.VxResetTimestamp(vxcam)
  pyvizionsdk.VxResetFrameCount(vxcam)
  ```

  </TabItem>
</Tabs>