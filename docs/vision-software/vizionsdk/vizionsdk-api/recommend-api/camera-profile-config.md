---
title: "Camera Profile Config"
description: "Camera Profile Setting"
sidebar_position: 5
---
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## Introduction

These functions are responsible for configuring the camera profile.

### VxLoadProfileConfig

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxLoadProfileConfig(std::shared_ptr<VxCamera> vxcam, const std::string profile)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxLoadProfileConfig(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is for loading the profile configuration from a JSON-formatted string.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **profile** : A constant string variable that stores the profile configuration. 
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  const std::string profile;
  // load the profile config
  VxLoadProfileConfig(vxcam, profile);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # load the profile config
  pyvizionsdk.VxLoadProfileConfig(vxcam)
  ```

  </TabItem>
</Tabs>

### VxActivateProfileStreaming

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxActivateProfileStreaming(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxActivateProfileStreaming(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function enables streaming based on a specified camera profile. 

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // streaming base on the profile config
  VxActivateProfileStreaming(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # streaming base on the profile config
  pyvizionsdk.VxActivateProfileStreaming(vxcam)
  ```

  </TabItem>
</Tabs>

### VxActivateProfileImageProcessing

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxActivateProfileImageProcessing(std::shared_ptr<VxCamera> vxcam)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxActivateProfileImageProcessing(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function activates image processing based on a specified camera profile.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* return 0 = PASS, return -1 = FAIL.

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // activates image processing base on the profile config
  VxActivateProfileImageProcessing(vxcam);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # activates image processing base on the profile config
  pyvizionsdk.VxActivateProfileImageProcessing(vxcam)
  ```

  </TabItem>
</Tabs>

### VxGetOSPProfileConfig

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxGetOSPProfileConfig(std::shared_ptr<VxCamera> vxcam, std::string& profile)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxGetOSPProfileConfig(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

* This function is used to retrieve the OSP profile configuration and save it to the provided string.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **profile** : A reference of sting format variable which stores the profile configuration.
* Return:
  * **C++** : return 0 = PASS, return -1 = FAIL
  * **Python** : tuple(return_code, profile)
    > **return_code** : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  std::string profile;
  // get the OSP profile configuration
  VxGetOSPProfileConfig(vxcam, profile);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  # get the OSP profile configuration
  pyvizionsdk.VxGetOSPProfileConfig(vxcam)
  ```

  </TabItem>
</Tabs>

### VxSetOSPProfileFlag & VxGetOSPProfileFlag

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxSetOSPProfileFlag(std::shared_ptr<VxCamera> vxcam, VX_OSP_PROFILE_FLAG flag)
  int VxGetOSPProfileFlag(std::shared_ptr<VxCamera> vxcam, VX_OSP_PROFILE_FLAG& flag)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxSetOSPProfileFlag(vxcam, flag)
  def VxGetOSPProfileFlag(vxcam)
  ```

  </TabItem>
</Tabs>

**Function Description:**

  * **VxSetOSPProfileFlag** : This function is used to set the flag to the OSP profile configuration.
  * **VxGetOSPProfileFlag** : This function is for retrieving the flag from the OSP profile configuration.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **flag** : A **VX_OSP_PROFILE_FLAG** format variable which set and stored the flag value.
* **VX_OSP_PROFILE_FLAG** format:

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  enum class VX_OSP_PROFILE_FLAG { DISABLED, ENABLED, ENABLED_AND_SAVE };
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  class VX_OSP_PROFILE_FLAG(Enum):
      DISABLED = 0
      ENABLED = 1
      ENABLED_AND_SAVE = 2
  ```

  </TabItem>
</Tabs>

**Return Values:**
  
  * **C++:**
    * 0 = Disable
    * 1 = Enable, but don't write OSP
    * 2 = Enable, write OSP by controller
    * -1 = FAIL.

  * **Python:**
    * **VxSetOSPProfileFlag:** return value same as C++
    * **VxGetOSPProfileFlag:** tuple(return_code, flag)
    > **return_code** : return value same as C++

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  // set the OSP profile flag
  VxSetOSPProfileFlag(vxcam, VX_OSP_PROFILE_FLAG::ENABLED);
  VX_OSP_PROFILE_FLAG flag;
  // get the OSP profile flag
  VxGetOSPProfileFlag(vxcam, flag);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  from pyvizionsdk import VX_OSP_PROFILE_FLAG
  # set the OSP profile flag
  pyvizionsdk.VxSetOSPProfileFlag(vxcam, VX_OSP_PROFILE_FLAG.ENABLED)
  # get the OSP profile flag
  pyvizionsdk.VxGetOSPProfileFlag(vxcam)
  ```

  </TabItem>
</Tabs>

### VxExportSensorConfig & VxDecodeSensorConfig

**Function:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  int VxExportSensorConfig(std::shared_ptr<VxCamera> vxcam, std::string configPath)
  int VxDecodeSensorConfig(std::string configPath, std::string jsonPath)
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  def VxExportSensorConfig(vxcam, configPath)
  def VxDecodeSensorConfig(configPath, jsonPath)
  ```

  </TabItem>
</Tabs>

**Function Description:**

  * **VxExportSensorConfig** : This function is used to dump the sensor profile configuration to the binary file.
  * **VxDecodeSensorConfig** : This function is for decoding the binary file which dump from **VxExportSensorConfig** and save as **json** format file.

**Parameter Description:**

* **vxcam** : A pointer shared ownership of a camera obtained from VxInitialCameraDevice().
* **configPath** : The destination for binary file saving.
* **jsonPath** : The destination for json file saving
* Return : return 0 = PASS, return -1 = FAIL

**Example:**

<Tabs groupId="api-code">
  <TabItem value="c++" label="C++">

  ```cpp
  std::string configPath = "config.bin";
  // export sensor config
  VxExportSensorConfig(vxcam, configPath);
  
  std::string jsonPath = "config.json";
  // decode sensor config
  VxDecodeSensorConfig(configPath, jsonPath);
  ```
  </TabItem>
  <TabItem value="py" label="Python">

  ```py
  configPath = "config.bin"
  # export sensor config
  pyvizionsdk.VxExportSensorConfig(vxcam, configPath)
  
  jsonPath = "config.json"
  # decode sensor config
  pyvizionsdk.VxDecodeSensorConfig(configPath, jsonPath)
  ```

  </TabItem>
</Tabs>