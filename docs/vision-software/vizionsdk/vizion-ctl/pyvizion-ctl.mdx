---
title: "pyvizion-ctl"
description: "pyvizion-ctl User Guide"
sidebar_position: 3
---
## pyvizion-ctl Functionality Overview

pyvizion-ctl, which imports pyvizionsdk, is an interactive command-line tool that provides a menu interface with various functionalities and options for controlling video devices.

## Start the pyvizion-ctl tool

:::warning
For **Linux** users: Please ensure that **~/.local/bin** is included in your PATH environment variable.
:::

Enter the `pyvizion-ctl` in the terminal to access the menu and control device functions with the interactive pyvizion-ctl interface.
```bash
pyvizion-ctl
```


## pyvizion-ctl Introduction

### Home Menu

The home menu allows navigation to the Device List to retrieve all available devices or exit the menu.

<img src="/img/pyvizion-ctl-homemenu.png" alt="Home Menu" style={{ maxWidth: '80%' }} />

### Device List

Choose a device from the device list to control.

<img src="/img/pyvizion-ctl-devlist.png" alt="Device List" style={{ maxWidth: '80%' }} />

### Camera Control and Management Tool

There are multiple tools available for controlling the device. It can also go back to the device list to choose another device to control.

<img src="/img/pyvizion-ctl-camctl.png" alt="Management Menu" style={{ maxWidth: '80%' }} />

### Device Information

Display the device information on the terminal. It can also go back to the control and management tool menu to choose other options, or exit the python-ctl.

<img src="/img/pyvizion-ctl-caminfo.png" alt="Camera Information" style={{ maxWidth: '80%' }} />

### Camera Control

The start menu for Camera Control allows two options: **Get Controls** and **Set Controls**.

<img src="/img/pyvizion-ctl-camcontrol.png" alt="Camera Control" style={{ maxWidth: '80%' }} />

  * **Get Controls**

      The **Get Controls** menu allows to get the all of controls value or get the specific control value by entering the index of controls.

      <img src="/img/pyvizion-ctl-getctl.png" alt="Get Controls" style={{ maxWidth: '80%' }} />

      If choosing to get specific control value, please enter the index which is display on the terminal.

      <img src="/img/pyvizion-ctl-specctl.png" alt="Get Specific Control" style={{ maxWidth: '80%' }} />

  * #### Set Controls

      The **Set Controls** menu will display the current value, range, and description of mode for all the controls. Enter the property index to be set.

      <img src="/img/pyvizion-ctl-setcontrol.png" alt="Set Controls" style={{ maxWidth: '80%' }} />

### List Format

The menu displays all the formats supported by the device. There allows the **Set format** option to set the camera format by entering the index of format.

<img src="/img/pyvizion-ctl-listfmt.png" alt="List Format" style={{ maxWidth: '80%' }} />

### Image Operation

The Image Operation menu is for capturing the image from the device.

  1. Choose the **Save Image** option

      <img src="/img/pyvizion-ctl-saveimg.png" alt="Save Image" style={{ maxWidth: '80%' }} />

  2. Enter the path to save the image.

      :::warning
      Please ensure the destination path is set to a location with write permissions for normal users.
      :::

      <img src="/img/pyvizion-ctl-imgpath.png" alt="Image Path" style={{ maxWidth: '80%' }} />

  3. Select an index of format.

      <img src="/img/pyvizion-ctl-imgfmt.png" alt="Image Format" style={{ maxWidth: '80%' }} />

  4. Enter a number to **skip** the previous frames for storage. And enter a number to decide how many images to **store**.

      <img src="/img/pyvizion-ctl-skipimg.png" alt="Numbers of Image" style={{ maxWidth: '80%' }} />

### OSP Profile Management

The OSP Profile Management menu allows to get and set the OSP flag.

<img src="/img/pyvizion-ctl-osp.png" alt="OSP Profile" style={{ maxWidth: '80%' }} />

### Timestamp Operations

The Timestamp Operations menu provides the options for retrieving the timestamp and framecount. It can also reset the timestamp and framecount value.

:::warning
Please ensure the device is **streaming** or not. The reset functions must be used when the camera is streaming.
:::

<img src="/img/pyvizion-ctl-timestamp.png" alt="Timestamp" style={{ maxWidth: '80%' }} />

### Sensor Config Operations

The Sensor Config Operations menu provides the options for **exporting** the sensor config and **decoding** the sensor config to readable json format.

<img src="/img/pyvizion-ctl-sensorconf.png" alt="Sensor Config" style={{ maxWidth: '80%' }} />