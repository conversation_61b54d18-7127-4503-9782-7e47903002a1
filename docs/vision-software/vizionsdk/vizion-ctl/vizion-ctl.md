---
title: "vizion-ctl"
description: "About vizion-ctl"
sidebar_position: 2
---
## vizion-ctl Functionality Overview

**vizion-ctl** is a command-line tool that utilizes the VizionSDK to control video devices. It provides a wide range of functionalities and options for convenient configuration and control.

## General/Common Options

The following options provide general functionality for vizion-ctl:

| Flag                                    | Description |
|-----------------------------------------|-------------|
| **`-h`**                                | Display available options |
| **`-v`**, **`--version`**               | Show version information |
| **`-l`**, **`--list-devices`**          | List all connected camera devices |
| **`-d`**, **`--device <num>`**          | Select device number (default: 0) |
| **`-i`**, **`--info`**                  | Display selected device information |
| **`-g`**, **`--get-ctrl <name>`**       | Retrieve control value (use `--all` for all controls) |
| **`-s`**, **`--set-ctrl <ctrl=value>`** | Set control value |
| **`-f`**, **`--list-formats`**          | List supported capture formats |
| **`-si`**, **`--save-image`**           | Save camera image with `--path <filepath>` parameter |
| **`-hi`**, **`--header-info`**          | Display ISP header information |
| **`-go`**, **`--get-osp`**              | Get current OSP profile flag |
| **`-so`**, **`--set-osp <flag>`**       | Set OSP profile flag: `0=Disable`, `1=Enabled`, `2=EnableAndSave` |
| **`-ro`**, **`--reset-osp`**            | Reset OSP profile to default settings |
| **`-fu`**, **`--firmware-update`**      | Update firmware <br/>(optional `--target` is for UVC firmware, with possible target flags : SPI, RAM, I2C) |
| **`-fd`**, **`--firmware-download`**    | Download firmware interactively |
| **`--get-timestamp`**                   | Retrieve timestamp |
| **`--reset-timestamp`**                 | Reset timestamp (requires streaming) |
| **`--get-framecount`**                  | Retrieve framecount |
| **`--reset-framecount`**                | Reset framecount (requires streaming) |
| **`--debug`**                           | Enable debug logging |
|  **`--log-path`**                       | Specify log file storage path |
| **`-sc`**, **`--save-sensor`**          | Save sensor config to binary file |
| **`-sd`**, **`--sensor-decode`**        | Decode sensor config binary to JSON (requires `--bin` and `--json` parameters) |

## Control Parameters

Below are the parameters available for **`get/set`** controls:

| Control Name                   | min    | max      | step  | default | Description of Modes |  
|:------------------------------ |:-------|:---------|:------|:--------|:---------------------|  
| **brightness**                 | -10    | 10       | 1     | 0       |
| **contrast**                   | -50    | 50       | 1     | 0       |
| **saturation**                 | 0      | 50       | 1     | 10      |
| **gamma**                      | 4      | 79       | 1     | 22      |
| **sharpness**                  | -20    | 20       | 1     | 0       |
| **backlight**                  | -150   | 150      | 1     | 10      |
| **noise**                      | -20    | 20       | 1     | 0       |
| **white_balance_mode**         | 0      | 1        | 1     | 1       | Manual Temperature (0)<br/>Auto (1) |
| **white_balance_temperature**  | 2300   | 15000    | 1     | 5000    |
| **jpeg_quality**               | 0      | 255      | 1     | 233     |
| **exposure_mode**              | 0      | 2        | 1     | 1       | Manual Mode (0)<br/>Auto Mode (1)<br/>Auto gain (2) |
| **exposure_time**              | 1      | 1000000  | 1     | 33333   |
| **exposure_min_time**          | 1      | 1000000  | 1     | 16666   |
| **exposure_max_time**          | 1      | 1000000  | 1     | 66666   |
| **exposure_gain**              | 1      | 64       | 1     | 1       |
| **flick_mode**                 | 0      | 3        | 1     | 0       | Disable (0)<br/>50Hz (1)<br/>60Hz (2)<br/>Auto (3)|
| **special_effect**             | 0      | 4        | 1     | 0       | Normal Mode (0)<br/>Black White Mode (1)<br/>Grayscale Mode (2)<br/>Negative Mode (3)<br/>Sketch Mode (4) |
| **flip_mode**                  | 0      | 3        | 1     | 0       | Normal (0)<br/>H-Mirror (1)<br/>V-Mirror (2)<br/>Rotate-180 (3) |
| **pan_target**                 | 0      | 10       | 1     | 5       |   
| **tilt_target**                | 0      | 10       | 1     | 5       |   
| **zoom_target**                | 10     | 80       | 1     | 10      |   
| **throughput**                 | 10.0   | 1000.0   |
| **max_fps**                    | 1      | 120      | 
  
## Examples

Here are some examples illustrating the usage of vizionctl:

### Display the vizion-ctl version information
```bash
vizion-ctl -v
vizion-ctl --version
```
### List all available video devices
```bash
vizion-ctl -l
vizion-ctl --list-devices
```
### Get the device information
```bash
vizion-ctl -d 0 -i
vizion-ctl -d 0 --info
```
### Get the controls value
```bash
# Get All controls value
vizion-ctl -d 0 -g --all
vizion-ctl -d 0 --get-ctrl --all

# Get throughput value
vizion-ctl -d 0 -g throughput
vizion-ctl -d 0 --get-ctrl throughput
```
### Set the control value
```bash
# Set throughput value to 500
vizion-ctl -d 0 -s throughput=500
vizion-ctl -d 0 --set-ctrl throughput=500
```
### List the supported capture formats
```bash
vizion-ctl -d 0 -f
vizion-ctl -d 0 --list-formats
```
### Save the image captured from device
:::warning
Please ensure the destination path is set to a location with write **permissions for normal users**.
:::
```bash
vizion-ctl -d 0 -si --path=path_to_img
vizion-ctl -d 0 --save-image --path=path_to_img
```
### Get the ISP header information
```bash
vizion-ctl -d 0 -hi
vizion-ctl -d 0 --header-info
```
### Get the current OSP profile flag
```bash
vizion-ctl -d 0 -go
vizion-ctl -d 0 --get-osp
```
### Set the OSP profile flag
```bash
vizion-ctl -d 0 -so 1
vizion-ctl -d 0 --set-osp 1
```
### Reset the OSP profile flag to default value
```bash
vizion-ctl -d 0 -ro
vizion-ctl -d 0 --reset-osp
```
### Update the firmware
```bash
# update sensor
vizion-ctl -d 0 -fu sensor=firmware.bin

# update uvc
vizion-ctl -d 0 -fu uvc=firmware.img

# update uvc with target flag = RAM
vizion-ctl -d 0 -fu uvc=firmware.img --target RAM
```
### Download the firmware
:::warning
Please ensure the destination path is set to a location with write **permissions for normal users**.
:::
```bash
vizion-ctl -d 0 -fd --path path_to_fw
```
### Get the timestamp and framecount
```bash
vizion-ctl -d 0 --get-timestamp
vizion-ctl -d 0 --get-framcount
```
### Reset the timestamp and framecount
:::warning
Please ensure the device is **streaming** or not. The reset functions must be used when the camera is streaming.
:::
```bash
vizion-ctl -d 0 --reset-timestamp
vizion-ctl -d 0 --reset-framecount
```
### Set the debug level for vizion-ctl
```bash
vizion-ctl --debug
```
### Set the log file path
:::warning
Please ensure the destination path is set to a location with write **permissions for normal users**.
:::
```bash
vizion-ctl -d 0 --log-path path_to_log_file
```
### Export the sensor config
:::warning
Please ensure the destination path is set to a location with write **permissions for normal users**.
:::
```bash
vizion-ctl -d 0 --save-sensor config.bin
```
### Decode the sensor config
:::warning
Please ensure the destination path is set to a location with write **permissions for normal users**.
:::
```bash
vizion-ctl -d 0 --sensor-decode --bin config.bin --json config.json
```

