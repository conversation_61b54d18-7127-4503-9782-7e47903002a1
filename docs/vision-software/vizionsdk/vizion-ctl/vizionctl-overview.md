---
title: "Overview"
description: "VizionCTL Overview"
sidebar_position: 1
---
## Introduction

**VizionCTL** is a cross-platform command-line tool for controlling and monitoring cameras. Two different tools are available: [**vizion-ctl**](/docs/vision-software/vizionsdk/vizion-ctl/vizion-ctl.md), which imports the VizionSDK library and [**pyvizion-ctl**](/docs/vision-software/vizionsdk/vizion-ctl/pyvizion-ctl.mdx), which imports the pyvizionsdk library.

**vizion-ctl** allows users to modify camera parameters, retrieve camera values and information, and update firmware through command-line instructions. Similarly, **pyvizion-ctl** enables camera modifications using command-line interface options.

## Supported Platforms

* Windows 10/11
* Ubuntu
* NXP i.MX8MP
* NXP i.MX8MM
* NXP i.MX93
* NXP i.MX95
* Jetson Orin <PERSON>o (JetPack 6.x)
* UP Squared Pro 7000

## vizion-ctl Installation

### Windows-x64

1. **Download** VizionSDK for Windows from the [VizionSDK Download Page](https://github.com/TechNexion-Vision/vizionsdk/releases).
2. **Run the installer**:
   ```bash
   vizionsdk-{version}-win64.exe
   ```
3. **Default installation path:**
   > `C:\Program Files\vizionsdk`

4. Start the **vizion-ctl**: Enter the command in the terminal to access the vizion-ctl tool
    ```bash
    vizion-ctl
    ```

### Linux-x64

#### Supported platforms

- General PCs
- UP Squared Pro 7000

#### Offline Installation
1. **Download** the VizionSDK .deb package for Ubuntu from the [VizionSDK .deb packages Download Page](https://github.com/TechNexion-Vision/vizionsdk/releases).
2. **Extract the SDK (no installation required):**
   ```bash
   tar -xf ./vizionsdk-{version}-linux64.tar.xz
   ```
3. **Install the .deb package:**
   ```bash
   sudo apt install ./vizionsdk-linux64.deb
   ```
   > Installing the .deb package will automatically add the apt repository and signing key for future updates via the system package manager.

#### Online Installation
1. **Add the repository and key:**
   ```bash
   sudo apt-get install wget gpg
   wget -qO- https://download.technexion.com/apt/technexion.asc | gpg --dearmor > packages.technexion.gpg
   sudo install -D -o root -g root -m 644 packages.technexion.gpg /etc/apt/keyrings/packages.technexion.gpg
   sudo sh -c 'echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionsdk/ stable main" > /etc/apt/sources.list.d/vizionsdk.list'
   sudo sh -c 'echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionviewer/ stable main" >> /etc/apt/sources.list.d/vizionsdk.list'
   ```
2. **Update and install:**
   ```bash
   sudo apt update
   sudo apt install vizionsdk
   ```

---

### Linux-ARM64

#### Supported platforms

- NXP-iMX8MP
- NXP-iMX8MM
- NXP-iMX93
- NXP-iMX95
- NVIDIA-Jetpack6.x and later

#### Offline Installation
1. **Download** the VizionSDK .deb package for ARM from the [VizionSDK .deb packages Download Page](https://github.com/TechNexion-Vision/vizionsdk/releases).
2. **Extract the SDK (no installation required):**
   ```bash
   tar -xf ./vizionsdk-{version}-linuxarm64.tar.xz
   ```
3. **Install the .deb package:**
   ```bash
   sudo apt install ./vizionsdk-linuxarm64.deb
   ```
   > Installing the .deb package will automatically add the apt repository and signing key for future updates via the system package manager.

#### Online Installation
> **Note for Jetpack users:** The stable main version is different from the legacy stable version. Please use the following instructions.

1. **Add the repository and key:**
   ```bash
   sudo apt-get install wget gpg
   wget -qO- https://download.technexion.com/apt/technexion.asc | gpg --dearmor > packages.technexion.gpg
   sudo install -D -o root -g root -m 644 packages.technexion.gpg /etc/apt/keyrings/packages.technexion.gpg
   sudo sh -c 'echo "deb [arch=arm64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionsdk/ stable main" > /etc/apt/sources.list.d/vizionsdk.list'
   sudo sh -c 'echo "deb [arch=arm64 signed-by=/etc/apt/keyrings/packages.technexion.gpg] https://download.technexion.com/apt/vizionviewer/ stable main" >> /etc/apt/sources.list.d/vizionsdk.list'
   ```
2. **Update and install:**
   ```bash
   sudo apt update
   sudo apt install vizionsdk
   ```

---

## pyvizion-ctl Installation

### Windows (x64)

1. Download the wheel file from [pyvizionsdk Download Page](https://pypi.vizionsdk.com/root/pyvizionsdk).
2. Choose the file matching your Python version:  
   `pyvizionsdk-{version}-cp{python_version}-cp{python_version}-win_amd64.whl`
3. Open Command Prompt or PowerShell.
4. Install with pip:
   ```shell
   pip install pyvizionsdk-{version}-cp{python_version}-cp{python_version}-win_amd64.whl
   ```
5. Or install directly from the website:
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```
---

### Linux (x86_64)

#### Supported platforms
- General PCs
- UP Squared Pro 7000
:::warning
Please ensure that **~/.local/bin** is included in your PATH environment variable.
:::
1. Download the wheel file from [pyvizionsdk Download Page](https://pypi.vizionsdk.com/root/pyvizionsdk).
2. Choose the file matching your Python version:  
   `pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_31_x86_64.whl`
3. Open Terminal.
4. Install with pip:
   ```shell
   pip install pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_31_x86_64.whl
   ```
5. Or install directly from the website:
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```
---

### Linux (aarch64)

#### Supported platforms

- NXP-iMX8MP
- NXP-iMX8MM
- NXP-iMX93
- NXP-iMX95
- NVIDIA-Jetpack6.x and later

1. Download the wheel file from [pyvizionsdk Download Page](https://pypi.vizionsdk.com/root/pyvizionsdk).
2. Choose the file matching your Python version:  
   `pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_34_aarch64.whl`
3. Open Terminal.
4. Install with pip:
   ```shell
   pip install pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_34_aarch64.whl
   ```
5. Or install directly from the website:
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```

