---
title: Installation
sidebar_position: 1
description: pyvizionsdk Installation
---

# Installation

---

## Supported Python Versions

The **pyvizionsdk** package is a CPython extension, supporting the following Python versions:

- Python 3.8
- Python 3.9
- Python 3.10
- Python 3.11
- Python 3.12
- Python 3.13

## Installation Steps

:::info Note: 
Installing with a **Python virtual environment** is recommended.
:::

### Python Virtual Environment Setup

1. Open the terminal, create the virtual environment with `venv`
   ```shell
   python -m venv {env_name}
   ```
2. Activate the environment
   - Windows (x64)
      ```shell
      {env_name}\Scripts\activate
      ```
   - Linux (x86_64) and ARM (aarch64)
      ```shell
      source {env_name}/bin/activate
      ```
3. Install **pyvizionsdk** package
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```
4. Deactivate the environment
   ```shell
   deactivate
   ```

### Windows (x64)

1. Download the wheel file from [pyvizionsdk Download Page](https://pypi.vizionsdk.com/root/pyvizionsdk).
2. Choose the file matching your Python version:  
   `pyvizionsdk-{version}-cp{python_version}-cp{python_version}-win_amd64.whl`
3. Open Command Prompt or PowerShell.
4. Install with pip:
   ```shell
   pip install pyvizionsdk-{version}-cp{python_version}-cp{python_version}-win_amd64.whl
   ```
5. Or install directly from the website:
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```

### Linux (x86_64)

**Supported platforms:** General PCs, UP Squared Pro 7000

1. Download the wheel file from [pyvizionsdk Download Page](https://pypi.vizionsdk.com/root/pyvizionsdk).
2. Choose the file matching your Python version:  
   `pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_31_x86_64.whl`
3. Open Terminal.
4. Install with pip:
   ```shell
   pip install pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_31_x86_64.whl
   ```
5. Or install directly from the website:
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```

### ARM (aarch64)

**Supported platforms:** NXP-iMX8MP, NXP-iMX8MM, NXP-iMX93, NXP-iMX95, NVIDIA-6.x and later

1. Download the wheel file from [pyvizionsdk Download Page](https://pypi.vizionsdk.com/root/pyvizionsdk).
2. Choose the file matching your Python version:  
   `pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_34_aarch64.whl`
3. Open Terminal.
4. Install with pip:
   ```shell
   pip install pyvizionsdk-{version}-cp{python_version}-cp{python_version}-manylinux_2_34_aarch64.whl
   ```
5. Or install directly from the website:
   ```shell
   pip install pyvizionsdk --extra-index-url https://pypi.vizionsdk.com/root/pyvizionsdk/+simple/
   ```

