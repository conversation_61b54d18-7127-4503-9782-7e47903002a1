---
title: Run Sample
description: pyvizionsdk sample code
sidebar_position: 3
---

# Run Sample

---

## Introduction

This document demonstrates how to use the `pyvizionsdk` Python SDK with practical examples. You can download the complete sample code from the [official repository](https://github.com/TechNexion-Vision/vizionsdk-python).

## Running the Sample Project

To run the Python sample, open your terminal and execute the following command:

```bash
python pyvizionSample.py
```

## Example Usage

Below are some example usages of the `pyvizionsdk` functions as demonstrated in the sample project.

### 1. Start the Device

Discover available camera devices and start the device at index `0`:

```python
result, camera_list = pyvizionsdk.VxDiscoverCameraDevices()
print("Discovered cameras:", camera_list)

# Print each camera in the list
for camera in camera_list:
    print(camera)

# Initialize the camera device at index 0
camera = pyvizionsdk.VxInitialCameraDevice(0)

# Open the camera device
result = pyvizionsdk.VxOpen(camera)
print("Open camera return code:", result)
```

### 2. Retrieve Device Information

Get the device name and interface type:

```python
# Get the camera device name
result, name = pyvizionsdk.VxGetDeviceName(camera)
print("Device name:", name)

# Get the interface type name
result, tyname = pyvizionsdk.VxGetDeviceInterfaceType(camera)
print("Device Interface type name:", tyname)
```

### 3. Adjust Device Brightness

Retrieve and set the brightness of the device:

```python
# Get UVC image processing brightness
result, brightness, flag = pyvizionsdk.VxGetUVCImageProcessing(camera, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS)
print("UVC brightness:", brightness)
print("Flag:", flag)
print("Return code:", result)

# Set the brightness to 12
result = pyvizionsdk.VxSetUVCImageProcessing(camera, VX_UVC_IMAGE_PROPERTIES.UVC_IMAGE_BRIGHTNESS, 12, 0)
print("Set UVC brightness return code:", result)
```
