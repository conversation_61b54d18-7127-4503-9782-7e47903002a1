---
title: Configuration & Usage
sidebar_position: 2
description: pyvizionsdk Configuration & Usage
---

# Configuration & Usage

---

## Configuration Setup

After installation, the configuration file `VxConfig.conf` will be located at:

- **Windows:**  
  `C:\Users\<USER>\AppData\Local\Programs\Python{version}\Lib\site-packages\pyvizionsdk\VxConfig.conf`
- **Linux, NXP-iMX, NVIDIA-Jetpack6.x and later:**  
  `~/.local/lib/python{version}/site-packages/pyvizionsdk/VxConfig.conf`

This configuration file allows pyvizionsdk to recognize TechNexion series camera devices.

:::tip
You can also customize the location of `VxConfig.conf` by setting the `VIZIONSDK_CONFIG_PATH` environment variable.
:::

---

## Usage

Import the pyvizionsdk library and test if the installation is successful:

```python
import pyvizionsdk

# Test the library import
pyvizionsdk.HelloVizionSDK()
# Output:
# Hello VizionSDK!
# Version: {VizionSDK Version}
```

---

> For more detailed information and advanced usage, please refer to the API documentation or the examples on GitHub. 