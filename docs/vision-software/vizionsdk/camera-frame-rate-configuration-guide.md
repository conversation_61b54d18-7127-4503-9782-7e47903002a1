---
title: "Camera Frame Rate Configuration"
description: "Camera Frame Rate Configuration Guide: Adjusting FPS in Different Exposure Modes"
---

## Introduction

Setting the appropriate frame rate (FPS) is essential for achieving the desired video smoothness in camera applications. This guide will explain how to set a specific FPS based on different exposure modes, covering operations in **Auto Mode**, **AGC Mode**, and **Manual Mode**. By using the `vizion-ctl` tool, users can flexibly control the camera's frame rate and optimize image quality. Additionally, users can adjust FPS through **Exposure Control**, which can achieve the desired frame rates by adjusting exposure time and gain.

## Exposure Modes Overview

### 1. Auto Exposure Mode

**Description**

- In **Auto Mode**, the camera's exposure settings (exposure time and gain) are fully controlled by the **Image Signal Processor (ISP)**.
- Frame rate (FPS) and gain are dynamically adjusted in response to environmental changes.

**Use Cases:**

- **Dynamic Scenes**: Ideal for environments like conference rooms or events where lighting changes frequently.
- **Low Light Conditions**: Suitable for nighttime or poorly lit areas.

**Example Command Usage**

1. **Enable Auto Exposure Mode**:
   ```bash
   vizion-ctl -d 0 -s exposure_mode=1
   ```

2. **Set Minimum Exposure Time to 16666us (for FPS=60)**:
   ```bash
   vizion-ctl -d 0 -s exposure_min_time=16666
   ```

3. **Set Maximum Exposure Time to 16666us (for FPS=60)**:
   ```bash
   vizion-ctl -d 0 -s exposure_max_time=16666
   ```

**Advantages**

- Easy setup for changing lighting conditions.
- Automatically adjusts exposure for unpredictable environments.

**Disadvantages**

- Frame rate and image quality may fluctuate, especially in varying lighting conditions.

---

### 2. AGC Mode (Auto Gain Control)

**Description**

- In **AGC Mode**, the **gain** is adjusted automatically to maintain optimal brightness, while the **exposure time remains fixed**.
- Frame rate stays stable, and only gain changes as necessary.

**Use Cases**

- **Uneven Lighting Conditions**: Works well outdoors or in scenarios where lighting fluctuates.
- **Surveillance**: Useful for security applications requiring stable video feeds.

**Example Command Usage**

1. **Enable AGC Mode**:
   ```bash
   vizion-ctl -d 0 -s exposure_mode=2
   ```

2. **Set Fixed Exposure Time to 16666us (for FPS=60)**:
   ```bash
   vizion-ctl -d 0 -s exposure=16666
   ```

**Advantages**

- Stable image quality with controlled exposure time.
- Ideal for situations with fluctuating lighting.

**Disadvantages**

- Noise may increase in low-light conditions due to higher gain settings.

---

### 3. Manual Mode

**Description**

- In **Manual Mode**, the user manually sets both **exposure time** and **gain**.
- These settings remain fixed, and the frame rate is determined based on the exposure and gain values chosen.

**Use Cases**

- **Professional Environments**: Perfect for use in filmmaking, live streaming, or photography, where precise control over exposure and gain is required.
- **Controlled Lighting**: Best for use in settings with consistent and predictable lighting.

**Example Command Usage**

1. **Enable Manual Mode**:
   ```bash
   vizion-ctl -d 0 -s exposure_mode=0
   ```

2. **Set Exposure Time to 16666us (for FPS=60)**:
   ```bash
   vizion-ctl -d 0 -s exposure=16666
   ```

3. **Set Gain Level (Gain=2x)**:
   ```bash
   vizion-ctl -d 0 -s gain=2
   ```

**Advantages**

- Full control over exposure and gain, allowing for high-quality, tailored video output.
- Ideal for environments with controlled or predictable lighting.

**Disadvantages**

- Requires expertise to configure settings effectively.
- Less flexible than auto modes, especially in dynamic lighting situations.

---

## Summary of Key Features to Control

| Mode         | Exposure Time Control | Gain Control | Best Use Case                     |
|--------------|-----------------------|--------------|-----------------------------------|
| Auto Mode    | ISP-controlled         | ISP-controlled | Dynamic lighting conditions         |
| AGC Mode     | User-controlled        | ISP-controlled | Controlled exposure with automatic gain |
| Manual Mode  | User-controlled        | User-controlled | Fixed exposure requirements         |

Understanding these AE modes enables users to fine-tune camera settings for different applications. Select the appropriate mode based on your specific needs to achieve the best image quality.

For further assistance, refer to the official TechNexion documentation or contact technical support.
