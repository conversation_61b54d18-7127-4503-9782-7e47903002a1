---
title: "TechNexion USB Camera User Guide"
description: "TechNexion USB Camera User Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

## Introduction

This article guides you how to get started using TechNexion USB camera on Windows and Linux.

---

## Supported Camera Modules

| **Camera Series** | **Products** |
| :---: | :---: |
| VCS | <ProductList series="VCS"/> |
| VCI | <ProductList series="VCI"/> |
| UVC-VLS3 | <ProductList series="UVC_VLS3"/>  |

[More Camera Products Details...](https://www.technexion.com/products/)

---

## How to use

There are several methods to use the camera when you connect it to a computer or embedded development kit via a USB cable.

#### Method 1 - Using TechNexion VizionViewer (Windows/Linux)

[VizionViewer&trade;](/docs/vision-software/vizionviewer/vizionviewer.md)


#### Method 2 - Using V4L2 Control (Linux)

1. List all devices.

        ```shell
        $ v4l2-ctl --list-device

        VCI-AR0144-C (usb-xhci-hcd.1.auto-1.2):
                /dev/video0
                /dev/video1
                /dev/media0
        ```

2. List camera formats.

        ```shell
        $ v4l2-ctl -d /dev/video0 --list-formats-ext
        ioctl: VIDIOC_ENUM_FMT
                Type: Video Capture

                [0]: 'UYVY' (UYVY 4:2:2)
                        Size: Discrete 1280x800
                                Interval: Discrete 0.017s (60.000 fps)
                                Interval: Discrete 0.033s (30.000 fps)
                        Size: Discrete 1280x720
                                Interval: Discrete 0.017s (60.000 fps)
                                Interval: Discrete 0.033s (30.000 fps)
                        Size: Discrete 640x480
                                Interval: Discrete 0.017s (60.000 fps)
                                Interval: Discrete 0.033s (30.000 fps)
                [1]: 'MJPG' (Motion-JPEG, compressed)
                        Size: Discrete 1280x800
                                Interval: Discrete 0.017s (60.000 fps)
                                Interval: Discrete 0.033s (30.000 fps)
                        Size: Discrete 1280x720
                                Interval: Discrete 0.017s (60.000 fps)
                                Interval: Discrete 0.033s (30.000 fps)
                        Size: Discrete 640x480
                                Interval: Discrete 0.017s (60.000 fps)
                                Interval: Discrete 0.033s (30.000 fps)
        ```

3. List camera v4l2 controls.

        ```shell
        $ v4l2-ctl -d /dev/video0 --list-ctrls

        User Controls

                        brightness 0x00980900 (int)    : min=0 max=32 step=4 default=16 value=16
                                contrast 0x00980901 (int)    : min=0 max=10 step=1 default=5 value=5
                        saturation 0x00980902 (int)    : min=0 max=80 step=8 default=16 value=16
                white_balance_automatic 0x0098090c (bool)   : default=1 value=1
                                gamma 0x00980910 (int)    : min=0 max=40 step=1 default=18 value=18
                                gain 0x00980913 (int)    : min=1 max=48 step=1 default=1 value=1
                power_line_frequency 0x00980918 (menu)   : min=0 max=2 default=0 value=0 (Disabled)
        white_balance_temperature 0x0098091a (int)    : min=2300 max=15000 step=1 default=5000 value=5000 flags=inactive
                        sharpness 0x0098091b (int)    : min=0 max=64 step=8 default=32 value=32
                backlight_compensation 0x0098091c (int)    : min=0 max=480 step=8 default=256 value=256

        Camera Controls

                        auto_exposure 0x009a0901 (menu)   : min=0 max=3 default=0 value=0 (Auto Mode)
                exposure_time_absolute 0x009a0902 (int)    : min=1 max=10000 step=1 default=330 value=100 flags=inactive
                        pan_absolute 0x009a0908 (int)    : min=-648000 max=648000 step=3600 default=0 value=0
                        tilt_absolute 0x009a0909 (int)    : min=-648000 max=648000 step=3600 default=0 value=0
                        zoom_absolute 0x009a090d (int)    : min=100 max=800 step=1 default=100 value=100
        ```

4. Camera streaming test with **UYVY** format.

        ```shell
        $ v4l2-ctl -d /dev/video0 --set-fmt-video=pixelformat=UYVY,width=1280,height=800 --stream-mmap
        <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 59.84 fps
        <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 59.85 fps
        ```

        :::note
        Using "**_Ctrl+C_** " to exit v4l2 streaming.
        :::

5. Camera streaming test with **MJPG** format.

        ```shell
        $ v4l2-ctl -d /dev/video0 --set-fmt-video=pixelformat=MJPG,width=1280,height=800 --stream-mmap
        <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 59.84 fps
        <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< 59.85 fps
        ```

        :::note
        Using "**_Ctrl+C_** " to exit v4l2 streaming.
        :::


#### Method 3 - Using GStreamer (Linux)

1. List all devices and formats.

        ```shell
        $ gst-device-monitor-1.0

        Device found:

                name  : VCI-AR0144-C
                class : Video/Source
                caps  : video/x-raw, format=UYVY, width=1280, height=800, pixel-aspect-ratio=1/1, framerate={ (fraction)60/1, (fraction)30/1 }
                        video/x-raw, format=UYVY, width=1280, height=720, pixel-aspect-ratio=1/1, framerate={ (fraction)60/1, (fraction)30/1 }
                        video/x-raw, format=UYVY, width=640, height=480, pixel-aspect-ratio=1/1, framerate={ (fraction)60/1, (fraction)30/1 }
                        image/jpeg, parsed=true, width=1280, height=800, pixel-aspect-ratio=1/1, framerate={ (fraction)60/1, (fraction)30/1 }
                        image/jpeg, parsed=true, width=1280, height=720, pixel-aspect-ratio=1/1, framerate={ (fraction)60/1, (fraction)30/1 }
                        image/jpeg, parsed=true, width=640, height=480, pixel-aspect-ratio=1/1, framerate={ (fraction)60/1, (fraction)30/1 }
                properties:
                        udev-probed = true
                        device.bus_path = platform-xhci-hcd.1.auto-usb-0:1.2:1.0
                        sysfs.path = /sys/devices/platform/soc@0/32f10108.usb/38200000.usb/xhci-hcd.1.auto/usb2/2-1/2-1.2/2-1.2:1.0/video4linux/video0
                        device.bus = usb
                        device.subsystem = video4linux
                        device.vendor.id = 3407
                        device.vendor.name = TechNexion
                        device.product.id = 0144
                        device.product.name = VCI-AR0144-C
                        device.serial = TechNexion_VCI-AR0144-C_000000008E604600A159178868A11A1A
                        device.capabilities = :capture:
                        device.api = v4l2
                        device.path = /dev/video0
                        v4l2.device.driver = uvcvideo
                        v4l2.device.card = VCI-AR0144-C
                        v4l2.device.bus_info = usb-xhci-hcd.1.auto-1.2
                        v4l2.device.version = 393527 (0x00060137)
                        v4l2.device.capabilities = 2225078273 (0x84a00001)
                        v4l2.device.device_caps = 69206017 (0x04200001)
                gst-launch-1.0 v4l2src ! ...
        ```

2. Preview with **UYVY** format.

        ```shell
        $ gst-launch-1.0 v4l2src device=/dev/video0 ! "video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)800" ! videoconvert ! autovideosink sync=false
        ```

3. Preview with **MJPG** format.

        ```shell
        $ gst-launch-1.0 v4l2src device=/dev/video0 ! "image/jpeg, width=(int)1280, height=(int)800" ! jpegdec ! autovideosink sync=false
        ```

        :::note
        **GStreamer element** \
        The element depends on your system, so the example may not be suitable for your system.
        :::

---

## Troubleshooting

* **Auto-suspend**

        Since the camera's standby power consumption is low, the system may disconnect camera through the auto-suspend function as like below.

        ```shell
        [86452.959044] usb 2-1.2: new SuperSpeed USB device number 5 using xhci-hcd
        [86452.983505] usb 2-1.2: LPM exit latency is zeroed, disabling LPM.
        [86453.022662] usb 2-1.2: Found UVC 1.10 device VCI-AR0144-C (3407:0144)
        [86453.139540] input: VCI-AR0144-C as /devices/platform/soc@0/32f10108.usb/38200000.usb/xhci-hcd.1.auto/usb2/2-1/2-1.2/2-1.2:1.0/input/input8
        [86467.470212] usb 2-1.2: USB disconnect, device number 5
        [86468.959511] usb 2-1.2: new SuperSpeed USB device number 6 using xhci-hcd
        [86468.983960] usb 2-1.2: LPM exit latency is zeroed, disabling LPM.
        [86469.022673] usb 2-1.2: Found UVC 1.10 device VCI-AR0144-C (3407:0144)
        [86469.139501] input: VCI-AR0144-C as /devices/platform/soc@0/32f10108.usb/38200000.usb/xhci-hcd.1.auto/usb2/2-1/2-1.2/2-1.2:1.0/input/input9
        [86472.078213] usb 2-1.2: USB disconnect, device number 6
        ```

        You can first check flag of auto-suspend and modify it to turn off this function.

        ```shell
        $ cat /sys/module/usbcore/parameters/autosuspend
        2

        $ echo -1 > /sys/module/usbcore/parameters/autosuspend
        ```

        After turning off auto-suspend, you need to reconnect the camera.

* **Floating framerate**

        Because of the product feature "_Designed for Low Light Applications_ ", the camera will increase exposure time via automatic exposure mode to maintain image quality in low-light environment.
        You can change the camera to manual exposure mode to keep the framerate. However, if you increase exposure gain in low-light environment, you will see more noise.

        ```shell
        $ v4l2-ctl -d /dev/video0 --set-ctrl auto_exposure=1
        ```

        The other way, you can use [VizionViewer™](/docs/vision-software/vizionviewer/vizionviewer-usage-guide#set-up-agc-exposure-mode-to-maintain-constant-framerate) or [VizionCTL](/docs/vision-software/vizionsdk/vizion-ctl) to change to AGC mode (Auto gain control, Manual exposure time).
