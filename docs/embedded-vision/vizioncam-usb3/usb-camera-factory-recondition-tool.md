---
title: "USB Camera Factory Recondition Tool"
description: "Tool to restore UVC cameras to factory settings"
sidebar_position: 2
---

## Introduction

This USB Camera Factory Reconditioning Tool is used to restore UVC cameras to factory settings. It can also be used with serdes cameras through single-port USB serdes framegrabbers.  It is available in both GUI (Graphical User Interface) and CLI (Command Line Interface) versions.

## Supported Products

| Product Type | Description |
|---|---|
| **VCI series** | USB3 cameras based on TEVI series image sensors. Also includes UVCI series (version without enclosure).|
| **VCS series** | USB3 cameras based on TEVS series image sensor modules.  Also includes UVCS (version without enclosure). |
| **UVC-VLS3** | VLS3 (FPD Link III) series cameras using a single-port USB to FPD Link III frame grabber. |
| **UVC-VLS-GM2** | VLS-GM2 (GMSL2) series cameras using a single-port USB to GMSL2 frame grabber. |

## Downloads

| Platform | Requirements | Link |
| :---: | :---: | :---: |
| Windows x64 | Windows 10 or later | [Download](https://download.technexion.com/camera_tools/TechNexion_USB_Camera_Factory_Recondition_Tool/windows_x64/) |
| Linux x64 | GUI: GLIBC 2.35+<br/>CLI: GLIBC 2.31+ | [Download](https://download.technexion.com/camera_tools/TechNexion_USB_Camera_Factory_Recondition_Tool/linux_x64/) |
| Linux ARM64 | GUI: GLIBC 2.35+<br/>CLI: GLIBC 2.31+ | [Download](https://download.technexion.com/camera_tools/TechNexion_USB_Camera_Factory_Recondition_Tool/linux_arm64/) |

## Driver Installation

Before using the tool, you need to install the required drivers:

**Windows:**
```bash
installdriver.bat
```

**Linux:**
```bash
./installdriver.sh
```

## Usage

**Windows:**
```bash
TechNexionUSBCameraFactoryReconditionTool.exe
```

**Linux (GUI):**
```bash
./TechNexionUSBCameraFactoryReconditionTool.sh
```

**Linux (CLI):**
```bash
./TechNexionUSBCameraFactoryReconditionToolCLI.sh
```

**CLI Version (Linux only):**

![CLI Screenshot](/img/camera_recondition_tool_cli.png)

**GUI Version (Windows or Linux):**

![GUI Screenshot](/img/camera_recondition_tool_gui.png)
