---
title: MIPI-CSI2 Cameras
description: MIPI-CSI2 Cameras
sidebar_position: 2
---
# Overview

This article provides a visual and technical overview of the schematics to help developers better understand the internal architecture and interfaces of our products.

:::warning
TEVI Series is not recommended for new designs.  Please consider TEVS Series.  These sensors are much improved.
:::

## Camera Pin Defintion

| TEVI Series | TEVS Series |
| ---- | ---- |
| [![pin_tev-camera](/img/tevi-70-pin.jpg)](/img/tevi-70-pin.jpg) | <a href="/img/tevs-70-pin.jpg" target="_blank"><img src="/img/tevs-70-pin.jpg" className="img-white-bg" /></a> |


## Camera Adapters

### 15-pin connectors

| TEVI-RPI15 | TEVS-RPI15 |
| ---- | ---- |
| [![pin_tevi-rpi15](//img/pin_tevi-rpi15.jpg)](//img/pin_tevi-rpi15.jpg) | [![pin_tevs-rpi15](//img/pin_tevs-rpi15.jpg)](//img/pin_tevs-rpi15.jpg) |

#### IO Expander Pin definition (I2C Address: 0x21)
| No. | Pin Define                | Description                          |
|-----|---------------------------|--------------------------------------|
| 0   | Reserved                  | Reserved pin                         |
| 1   | Reserved                  | Reserved pin                         |
| 2   | STANDBY                   | Standby pin of TEVS Camera           |
| 3   | Reserved                  | Reserved pin                         |
| 4   | CSI_FRAME_SYNC            | Framesync Trigger pin of TEVS Camera |
| 5   | Reserved                  | Reserved pin                         |
| 6   | Reserved                  | Reserved pin                         |
| 7   | Reserved                  | Reserved pin                         |


### 22-pin connector

![pin_tev-rpi22](//img/pin_tev-rpi22.jpg)

#### IO Expander Pin definition (I2C Address: 0x27)

| No. | Pin Define                | Description                          |
|-----|---------------------------|--------------------------------------|
| 0   | Reserved                  | Reserved pin                         |
| 1   | Reserved                  | Reserved pin                         |
| 2   | Reserved                  | Reserved pin                         |
| 3   | CSI_FRAME_SYNC            | Framesync Trigger pin of TEVS Camera |
| 4   | Reserved                  | Reserved pin                         |
| 5   | Reserved                  | Reserved pin                         |
| 6   | STANDBY                   | Standby pin of TEVS Camera           |
| 7   | Reserved                  | Reserved pin                         |


### RPI Series adapter switches
The main purpose of this is to establish the precise supply voltage for the camera module. Kindly refer to the table below for configuring it based on the sensor's model.

:::warning
Failure to configure it according to the sensor's model may potentially result in abnormalities within the camera module.
:::
![Switch_List](//img/Switch_List.png)

## TEVS Series Power Up Sequence

Apply 3.3 V and 1.8 V first. After t1 enable 1.15 V. After t2 enable 2.8 V while keeping CSI_nRST asserted low. Pull CSI_nRST high after t3 once all rails and the reference clock are stable. The module then asserts System Ready after t4, indicating the host can begin I²C initialization and start MIPI streaming. Keep STANDBY at low level throughout startup.

![TEVS_PWR_SEQ](//img/tevs_power_seq.png)


## Need more Help?

If you have questions about the schematics or how to use them for your development work, feel free to reach out via:
- Contact Email: <EMAIL>