---
title: "TI Starter Kit EVM Board TEVS Camera Usage Guide"
description: "TI Starter Kit EVM Board TEVS Camera Usage Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

## 🚀 Introduction

This article guides you how to get started using TechNexion camera modules on **Ti starter kit**.

---
## 📸 Supported Camera Modules

| **Camera Series** | **Products** |
|:---:|-------|
| TEVS | <ProductList series="TEVS"/> |

:::tip  **More Camera Products Details**
Check here ! 🔗 [TechNexion Embedded Vision Solutions](https://www.technexion.com/products/embedded-vision/)<br/>
Find out the solution which can accelerate your time to market for embedded vision projects.
:::


:::note  **Ti Processor SDK version**
TechNexion is using `ti-processor-sdk-linux` with **09.01.00.0X (Dec 2023)**, if you need to support other versions, you can grab the source codes from 🔗 [GitHub – TechNexion TI EVK camera driver](https://github.com/TechNexion-Vision/ti_evk_camera/tree/main) and build it by yourself.
:::
---
## 🧩 Supported Ti Developer Kit

  **SoM**  | **Board**   |
:---:|-------|
AM62x  | [Ti SK-AM62X](https://www.ti.com/tool/SK-AM62)
AM62A  | [Ti SK-AM62A-LP](https://www.ti.com/tool/SK-AM62A-LP)
TDA4VM  |[Ti SK-TDA4VM](https://www.ti.com/tool/SK-TDA4VM)

* * *
## 🔧 Hardware Setup Instructions

### 1. Connect the Adapter Board and Camera Cable

With different board you need prepare match adapter board and cable for the connect pins.<br/>
Here is the table for you can easily to confirm what you need for your solution.

**Developer Kit**| **Adapter board**| **Camera Kit**
---|---|---
Ti SK-AM62| TEV-RPI15 Adaptor| [TechNexion EVK RPI15](https://www.technexion.com/?s=RPI15&post_type=product)
Ti SK-AM62A-LP| TEV-RPI22 Adaptor| [TechNexion EVK RPI22](https://www.technexion.com/?s=RPI22&post_type=product)
Ti SK-TDA4VM| TEV-RPI15 Adaptor| [TechNexion EVK RPI15](https://www.technexion.com/?s=RPI15&post_type=product)

Follow this demo video to connect adapter and cable to the CSI connector on board.

<iframe width="640" height="480" src="https://www.youtube.com/embed/xkC3rHdgO_I" title="TI Sitara AM62A Embedded Vision (camera) Solutions" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
---
## 💻 Prepare Yocto demo image for testing TechNexion camera

To test TechNexion TEVS Series cameras, you need a demo image that includes the required Device Tree Blobs (DTBs) and camera drivers.

### 🔽 Download and Select the Correct Image for Your EVK

Prebuilt demo images can be available for download via TechNexion's server.
#### **Download Link**:
🔗 [SK-AM62X Image Download Link](https://download.technexion.com/demo_software/EVK/TI/SK-AM62X/sk-am62x_yocto-4.0_20240926.zip)<br/>
🔗 [SK-AM62A-LP Image Download Link](https://download.technexion.com/demo_software/EVK/TI/SK-AM62A/sk-am62a_yocto-4.0_20240926.zip)<br/>
🔗 [SK-TDA4VM Image Download Link](https://download.technexion.com/demo_software/EVK/TI/SK-TDA4VM/sk-tda4vm_yocto-4.0_20240926.zip)

---

### 💾 Flashing the Image
You can flash the image to an SD Card using the following methods.
:::tip  Micro-SD Card
The capacity must be higher **16GB**.
:::
### 1. For Windows Users

Use **balenaEtcher** to write the image to your SD card:

🔗 [<img src="/img/balenaetcher-vector-logo.png" width="250" height="50" />](https://www.balena.io/etcher/)

### 2. For Linux Users

Use `dd` flashing process to the SD card:

```bash
$ sudo dd of=/dev/<sd card> if=<Yocto prebuild image> bs=1M status=progress
```

* * *
## 📸 Camera Testing Instructions

### Specify Camera DTBO in uEnv.txt

1. Find `uEnv.txt` file in prebuild image.

```shell
# Open on SK-TDA4VM, the directory should be here:
$ /run/media/BOOT-mmcblk1p1/uEnv.txt
# Open on HOST-PC, the directory should be here:
$ /media/username/BOOT/uEnv.txt
```
2. Add camera dtb specifies the `name_overlays` variable in `uEnv.txt`.

```shell
#SK-AM62X
    name_overlays= k3-am625-sk-csi2-tevs.dtbo
#SK-AM62A
    name_overlays= k3-am62a7-sk-csi2-tevs.dtbo
#SK-TDA4VM
    name_overlays= k3-j721e-sk-tevs.dtbo
```
---
## 🎥 Start Camera Video Stream via GStreamer
### Check Camera Availability

Use the v4l2-ctl tool to list connected video devices.
```shell
$ v4l2-ctl --list-device
```
Example output:
```shell
# CSI 0 (cam1)
j721e-csi2rx (platform:4504000.ticsi2rx):
        /dev/video{X}
# CSI 1 (cam2)
j721e-csi2rx (platform:4510000.ticsi2rx):
        /dev/video{X}
```
---
### Launch GStreamer Pipeline

The following is example.
<img src="/img/tevs-ti-sk-v4l2-ctl-example.png" width="400" height="450" />

Specify the capture device you just get `/dev/video2` and start gstreamer to get video stream with **640x480** resolution on the display :
```shell
$ gst-launch-1.0 v4l2src device=/dev/video2 ! \
video/x-raw,width=640,height=480 ! \
waylandsink sync=false
```
* * *

## 🚨 Troubleshooting

:::info **Usecase**
Take **TEVS-AR0234** on **SK-TDA4VM CAM2** connector for example.
:::
:::warning Cannot Run Edge AI Demo Application
The pre-built image is based on TI's setup, so the **default demo application** does not support TechNexion cameras.
To use TechNexion cameras, you can **disable the demo service** and launch the Weston desktop **manually**.
```shell
$ systemctl disable edgeai-init.service
$ systemctl restart weston
$ reboot
```
:::
---
### Find out the Camera

Boot up SK-TDA4VM and check initialization of camera driver.
it shows below messages, the driver is initialized correctly. If the drivers haven't been probed, please check the connector plugin correct.
```shell
$ dmesg -t | grep tevs
```

The message similar to the following :
```shell
root@tda4vm-sk:/opt/edgeai-gst-apps# dmesg|grep tevs
tevs 7-0048: tevs_probe() device node: tevs@48
tevs 7-0048: Version:********
tevs 7-0048: Product:TEVS-AR0234, HeaderVer:3, MIPI_Rate:800
tevs 7-0048: probe success
```

And then try to using `media-ctl` command to print the device topology from camera to CSI and the end to video device node.
```shell
$ media-ctl -d /dev/media1 -p
```

We can get which v4l2-subdev is camera in this print message and then we can use `v4l2-ctl` by it.
![image.png](//img/tevs-subdev-entity-ti-sk.png)

---
### Change Resolution
For kernel 6.1 architecture you cannot easily change resolution in gsteamer pipeline. <br/>
You must need to using `v4l2-ctl` to view and set the camera's supported resolutions.

#### 1. Check camera supported resolutions via v4l2-ctl
```shell
$ v4l2-ctl -d /dev/v4l-subdev2 --list-subdev-framesize pad=0
root@tda4vm-sk:/opt/edgeai-gst-apps# v4l2-ctl -d /dev/v4l-subdev2 --list-subdev-framesize pad=0
ioctl: VIDIOC_SUBDEV_ENUM_FRAME_SIZE (pad=0,stream=0)
Size Range: 640x480 - 640x480
Size Range: 1280x720 - 1280x720
Size Range: 1920x1080 - 1920x1080
Size Range: 1920x1200 - 1920x1200
```
#### 2. Set Format
```shell
$ media-ctl --set-v4l2 "'tevs 7-0048':0 [fmt:UYVY8_2X8/1280X720@1/30 field:none]"
```









