---
title: Host Environment Setting
description: Host environment setup for NVIDIA
sidebar_class_name: hidden
unlisted: true
sidebar_position: 100
---

## Prepare ubuntu computer
Ubuntu 22.04.4 LTS (Jammy Jellyfish) is recommended.
You can also use ubuntu virtual machine.

## Create workspace
#### Create and enter the nvidia workspace folder
```bash
$ mkdir <nvidia_folder> && cd <nvidia_folder>
```

## Download the TN-Jetpack using the download script
### Get the download script
1. Go to [TechNexion Jetson Jetpack script github](https://github.com/TechNexion-Vision/TEV-Jetson_Jetpack_script)
![image.png](//img/image%2862%29.png)

2. Click the ```'<> Code'``` button
![image.png](//img/image%2861%29.png)
3. Copy the URL of HTTPS
Or using SSH if match the requirement.
4. Using git command to download the script and named 'download_script'
```bash
$ git clone <URL> download_script
Cloning into 'download_script'...
remote: Enumerating objects: 186, done.
remote: Counting objects: 100% (101/101), done.
remote: Compressing objects: 100% (64/64), done.
remote: Total 186 (delta 63), reused 71 (delta 37), pack-reused 85
Receiving objects: 100% (186/186), 49.06 KiB | 1.63 MiB/s, done.
Resolving deltas: 100% (107/107), done.
```
### Run the download script
1. change the permission of the script
```bash
$ chmod a+x download_script/technexion_jetpack_download.sh
```
2. copy the script and board conf to nvidia workspace folder (current folder)
```bash
$ pwd
<nvidia_folder>
$ cp -rv download_script/technexion_jetpack_download.sh ./
$ cp -rv download_script/*.conf ./
```
3. Run script and wait for complete
You must enter the required option to make it start.
```bash
./technexion_jetpack_download.sh
download the Technexion Jetpack -b <baseboard>
-b: baseboard <TEK6020-ORIN-NANO/ TEK6040-ORIN-NANO/ TEK6070-ORIN-NX/ TEK6100-ORIN-NX
               TEV-RPI22-TEVI/ TEV-RPI22-TEVS/ VLS3-ORIN-EVK-VLS3/ VLS3-ORIN-EVK-VLI>

Jetson Orin series:
TEK6020-ORIN-NANO| TEK6040-ORIN-NANO| TEK6070-ORIN-NX| TEK6100-ORIN-NX

Jetson Orin EVK series:
TEV-RPI22-TEVI| TEV-RPI22-TEVS| VLS3-ORIN-EVK-VLS3| VLS3-ORIN-EVK-VLI

-t: tag for sync code <>

--qspi-only: do not create/ flash rootfs, for qspi image only
```
:::info
```bash
$ ./technexion_jetpack_download.sh -b TEK6100-ORIN-NX
```
```bash
$ ./technexion_jetpack_download.sh -b VLS3-ORIN-EVK-VLS3
```
:::

You can set and switch to certain tag using ```-t <tag name>```
:::info
| tag name | tag description |
| --- | --- |
| r35.3.ga | TechNexion SW with Jetpack 5.1.1 Nvidia L4T 35.3.1 |

example:
```bash
$ ./technexion_jetpack_download.sh -b VLS3-ORIN-EVK-VLS3 -t r35.3.ga
```
:::
Once it done, this folder will contain:
* TechNexion camera source code
* Device-tree of TechNexion product
* Modified NVIDIA kernel
* Bootable system image that ready to be deployed into device.
> system image must be the default storage type.
> TEK-ORIN series: NVMe
> ORIN-EVK series: SD

## Troubleshooting
* You might need to enter the sudo password *twice* if the script takes too much time.
* Script will exit once it got any error return, you will see the error description on console.