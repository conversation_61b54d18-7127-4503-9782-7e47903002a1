---
title: Build TechNexion Camera Drivers for JetPack5.x
description: How to Build TechNexion Camera Drivers for NVIDIA JetPack5.x
sidebar_position: 1
---

## Introduction
This article will introduce how to build the camera drivers for the NVIDIA Jetson platforms and how to port the driver for your JetPack version. TechNexion platforms are using JetPack 5.1.1. If you want to modify drivers for different vesion of JetPack, you can follow the guide to rebuild the camera drivers.

:::warning
If you want to port to driver to other JetPack version such as JP 4.x, you also follow the guide but somewhere you need to modify for different JetPack version.
:::

## Preparasion
First, you need to setup the environments on your host PC.

1. Download toolchain for JetPack 5.x.
```shell
wget -O bootlin-toolchain-gcc-93.tar.gz  https://developer.nvidia.com/embedded/jetson-linux/bootlin-toolchain-gcc-93
mkdir /opt/bootlin-toolchain-gcc-93 && tar xf bootlin-toolchain-gcc-93.tar.gz -C /opt/bootlin-toolchain-gcc-93/
```

2. Download JetPack BSP for your interested version.

For JetPack 5.1.2:
```shell
wget https://developer.nvidia.com/downloads/embedded/l4t/r35_release_v4.1/release/jetson_linux_r35.4.1_aarch64.tbz2
tar xf jetson_linux_r35.4.1_aarch64.tbz2
```

## Build Camera Drivers with Jetson BSP
There is an example for building TechNexion TEVS camera driver for JetPack 5.1.2 on Jetson Orin Nano DevKit.

We can download BSP sources by source_sync.sh:
For JetPack 5.1.2:
```shell
cd Linux_for_Tegra/
./source_sync.sh -k -t jetson_35.4.1
cd sources/
```
After download the kernel source codes by source_sync.sh, you will see that:
```
sources/
├── hardware
├── kernel
└── tegra
```

Then we need to clone TechNexion camera driver, follow the steps:
```shell
pushd kernel
git clone https://github.com/TechNexion-Vision/TEV-Jetson_Camera_driver.git technexion
echo technexion >> kernel-5.10/kernel-overlays.txt
echo technexion >> kernel-5.10/kernel-int-overlays.txt
popd
```

Then we need to modify device tree:
```shell
pushd hardware/nvidia/platform/t23x/p3768/kernel-dts/
git remote add technexion https://github.com/TechNexion-Vision/TEV-JetsonOrin-Nano_device-tree.git
git fetch technexion
git checkout tn_l4t-r35.3.1.ga_kernel-5.10
popd
```

Finally, we can add the script and run it to build the BSP.

```shell
vim nv_build.sh
```

```bash
#/bin/bash -e
# Build TechNexion Camera Drivers for NVIDIA JetPack 5.x BSP

TOOLCHAIN_PREFIX=/opt/bootlin-toolchain-gcc-93/bin/aarch64-buildroot-linux-gnu-
JETSON_NANO_KERNEL_SOURCE=$PWD
TEGRA_KERNEL_OUT=$JETSON_NANO_KERNEL_SOURCE/build
KERNEL_MODULES_OUT=$JETSON_NANO_KERNEL_SOURCE/modules
BUILD_ARG=""
BUILD_CMD="make -C kernel/kernel-5.10/ ARCH=arm64 O=$TEGRA_KERNEL_OUT LOCALVERSION=-tegra CROSS_COMPILE=${TOOLCHAIN_PREFIX} "
MOD_INSTALL_DIR=$TEGRA_KERNEL_OUT"/modules-5.10"

for arg in "$@"; do
        BUILD_ARG="$BUILD_ARG $arg"
done

if [[ "$BUILD_ARG" == *"modules_install"* ]]; then
    rm -rf $MOD_INSTALL_DIR/*
    BUILD_ARG="$BUILD_ARG INSTALL_MOD_PATH=$MOD_INSTALL_DIR"
fi

if [[ "$BUILD_ARG" == *"Image"* || "$BUILD_ARG" == *"dtbs"* || "$BUILD_ARG" == *"modules"* ]]; then
    BUILD_CMD=$BUILD_CMD"-j8 --output-sync=target "
fi

$BUILD_CMD$BUILD_ARG
```
Run it:
```shell
# Setup tegra_defconfig
./nv_build.sh tegra_defconfig

# Enable TechNexion TEVS Camera Config
cp kernel/kernel-5.10/scripts/config ./build/scripts/
pushd build
./scripts/config -m CONFIG_VIDEO_TEVS
popd

# Build kernel Image, modules, and dtbs
./nv_build.sh Image modules dtbs
```

After building, you can find the tevs driver module:
```
./build/drivers/media/i2c/tevs/tevs.ko
```