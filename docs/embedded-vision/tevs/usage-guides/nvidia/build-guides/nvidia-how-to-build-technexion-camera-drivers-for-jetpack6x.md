---
title: Build TechNexion Camera Drivers for JetPack6.x
description: How to Build TechNexion Camera Drivers for NVIDIA JetPack6.x
sidebar_position: 2
---

## Introduction
This article will introduce how to build the camera drivers for the NVIDIA Jetson platforms and how to port the driver for your JetPack version. TechNexion platforms are using JetPack 6.1. If you want to modify drivers for different vesion of JetPack, you can follow the guide to rebuild the camera drivers.

:::warning
If you want to port to driver to other JetPack version such as JP 5.x, you can follow the [guides](docs/embedded-vision/tevs/usage-guides/nvidia/build-guides/nvidia-how-to-build-technexion-camera-drivers-for-jetpack5.md).
:::

## Preparasion
First, you need to setup the environments on your host PC.

1. Download toolchain for JetPack 6.x.
```shell
wget -O aarch64--glibc--stable-2022.08-1.tar.bz2 https://developer.nvidia.com/downloads/embedded/l4t/r36_release_v3.0/toolchain/aarch64--glibc--stable-2022.08-1.tar.bz2
mkdir /opt/aarch64--glibc--stable-2022.08-1 && tar xf aarch64--glibc--stable-2022.08-1.tar.bz2 -C /opt/aarch64--glibc--stable-2022.08-1/
```

2. Download JetPack BSP for your interested version.

For JetPack 6.1:
```shell
wget https://developer.nvidia.com/downloads/embedded/l4t/r36_release_v4.0/release/Jetson_Linux_R36.4.0_aarch64.tbz2
tar xf Jetson_Linux_R36.4.0_aarch64.tbz2
```

## Build Camera Drivers with Jetson BSP
There is an example for building TechNexion TEVS camera driver for JetPack 6.1 on Jetson Orin Nano DevKit.

We can download BSP sources by source_sync.sh:
For JetPack 6.1:
```shell
cd Linux_for_Tegra/source/
./source_sync.sh -k -t jetson_36.4
```
After download the kernel source codes by source_sync.sh, you will see that:
```
source/
├── dtc-src
├── generic_rt_build.sh
├── hardware
├── hwpm
├── kernel
├── kernel_src_build_env.sh
├── Makefile
├── nvbuild.sh
├── nvdisplay
├── nvethernetrm
├── nvgpu
├── nvidia-oot
├── nv_src_build.sh
└── source_sync.sh
```

Then we need to clone TechNexion camera driver, follow the steps:
```shell
pushd nvidia-oot
git submodule add -b tn_l4t-r36.4.ga_kernel-5.15 https://github.com/TechNexion-Vision/TEV-Jetson_Camera_driver.git drivers/media/i2c/technexion
echo 'obj-m += technexion/' >> drivers/media/i2c/Makefile
popd
```

Then we need to modify device tree:
```
pushd hardware/nvidia/t23x/nv-public/
git remote add technexion https://github.com/TechNexion-Vision/TEV-JetsonOrin-Nano_device-tree.git
git fetch technexion
git checkout tn_l4t-r36.4.ga_kernel-5.15
popd
```

Finally, we can run `nv_build.sh` to build the BSP.

```shell
export CROSS_COMPILE=/opt/aarch64--glibc--stable-2022.08-1/aarch64--glibc--stable-2022.08-1/bin/aarch64-linux-
./nvbuild.sh -o build
```

After building, you can find the tevs driver module:
```
./build/nvidia-oot/drivers/media/i2c/technexion/tevs/tevs.ko
```