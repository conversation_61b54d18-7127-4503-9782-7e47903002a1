---
title: TechNexion Camera Modules for JetPack 5.1.x
description: TechNexion Camera Modules for JetPack 5.1.x
---
import ProductList from '@site/src/components/ProductList.jsx';

## Instruction

[TechNexion Embedded Vision Solutions](https://www.technexion.com/products/embedded-vision/) provide embedded system developers access to high-performance, industrial-grade camera solutions to accelerate their time to market for embedded vision projects.

## Supported Camera Modules

| **Camera Series** | **Products** |
| :---: | :---: |
| TEVI Series | <ProductList series="TEVI"/> |
| TEVS Series | <ProductList series="TEVS"/> |
| FPD Link III Series | <ProductList series="VLS3"/> |
| GMSL2 Series | <ProductList series="VLS_GM2"/> |

:::warning
TEVI Series is not recommended for new designs.  Please consider TEVS.  These sensors are much improved.
:::

[More Camera Products Details...](https://www.technexion.com/products/embedded-vision)

---
## Supported NVIDIA Jetson Developer Kit

- [NVIDIA Jetson Orin Nano](https://developer.nvidia.com/embedded/learn/get-started-jetson-orin-nano-devkit)

---

## Install TN Camera on Jetson Developer Kit

#### Adaptor for NVIDIA Jetson Orin Nano Development Kit

**TEV-RPI22 Adaptor**
> Connect TEVS camera and TEV-RPI22 adaptor to Jetson Orin Nano Developer Kit directly.

<a href="https://www.technexion.com/products/embedded-vision/mipi-csi2/evk/tevs-ar0144-c-s83-ir-rpi22/" target="_blank">
  <img src="/img/tevs-ar0144-c-s83-ir-rpi22.png" width="360" height="360" />
</a>

**VLS3-ORIN-EVK Adaptor for VLS3 Cameras**

Follow the video tutorial below to learn how to connect **VLS3 cameras** and the **VLS3-ORIN-EVK adapter** to the **Jetson Orin Nano Developer Kit**.

<iframe width="640" height="360" src="https://www.youtube.com/embed/Ggu97E-KmsA" title="VLS3-ORIN-EVK" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

---

#### Method 1 - Using TechNexion Pre-built Image

We provide pre-built images to install quickly on Jetson Orin Nano Developer Kit.

[TEV-RPI22 + TEVS Cameras](https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/TEV-RPI22_Camera_Series/DiskImage/TEV-RPI22-TEVS_ubuntu-20.04_dp_SD_diskimg.zip)

[VLS3-ORIN-EVK + VLS3 Cameras](https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/VLS3-ORIN-EVK/DiskImage/VLS3-ORIN-EVK-VLS3_ubuntu-20.04_dp_SD_diskimg.zip)

---

#### Method 2 - Using TechNexion Pre-built modules

**Jetson Orin Nano**
:::info
We recommend following the [Getting Started Guide](https://developer.nvidia.com/embedded/learn/get-started-jetson-orin-nano-devkit) for Jetson Orin Nano Developer Kit.
After that, you can follow the below method to install TechNexion Cameras Driver.
:::
1. Download pre-built modules.

**JetPack 5.1.1(R35.3.1)**
```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/pre-built-modules/latest/JP511/tn_camera_modules_jp511.tar.gz
```
**JetPack 5.1.2(R35.4.1)**
```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/pre-built-modules/latest/JP512/tn_camera_modules_jp512.tar.gz
```
**JetPack 5.1.3(R35.5)**
```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/pre-built-modules/latest/JP513/tn_camera_modules_jp513.tar.gz
```
**JetPack 5.1.4(R35.6)**
```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/pre-built-modules/latest/JP514/tn_camera_modules_jp514.tar.gz
```

2. uncompress the modules.

```shell
tar -xf tn_camera_modules_<JP_Ver>.tar.gz
```

3. Run installation script.

```shell'
cd tn_camera_modules_<built_time>/
./tn_install.sh
```

4. After you agree to continue the installation, select the pre-installed modules that you want. The default module is TEVS cameras.

```shell
$ sh tn_install.sh
****** TechNexion Camera Driver Installation ******
This installation is easy to install TechnNexion Camera Drivers for NVIDIA
Jetson Orin NANO Development Kits. Before start to install camera driver,
You should BACKUP your image to avoid any file you lost while installing process.
Do you want to continue?[Y/n]Y
Continuing with the installation...
Install TN-CAM modules: vizionlink.ko
Install TN-CAM modules: tevs.ko
Install TN-CAM modules: tevi_ap1302.ko
Install TN-CAM modules: tevi_ov5640.ko
Install TN-CAM DTB file: tevs
Installed TN-CAM DTB file Done.
Install TN-CAM DTB file: vl316-vls
Installed TN-CAM DTB file Done.
Install TN-CAM DTB file: tevi-ap1302
Installed TN-CAM DTB file Done.
Install TN-CAM DTB file: tevi-ov5640
Installed TN-CAM DTB file Done.
Select modules:
    [1]: TEVS: TEVS Series MIPI Cameras with RPI22 Adaptor
    [2]: VLS3: VLS3 Series Cameras with VLS3-ORIN-EVK Adaptor
    [3]: TEVI-AP1302: TEVI-AR Series Cameras with TEV-RPI22 Adaptor
    [4]: TEVI-OV5640: TEVI-OV5640 Cameras with TEV-RPI22 Adaptor
Which modules do you select?[default:1]
```

Note: You should reboot the device after installation.

---

#### Method 3 - Build drivers from source code

Please follow the [guide](//docs/embedded-vision/tevs/usage-guides/nvidia/how-to-build-technexion-camera-drivers-for-nvidia-jetpack5) to build camera driver modules.

---

## Bring up Camera by GStreamer

If you succeed in initialing the camera, you can follow the steps to open the camera.

1. Check the supported resolutions:

```shell
$ gst-device-monitor-1.0 Video/Source
Device found:

        name  : vi-output, tevs 9-0048
        class : Video/Source
        caps  : video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)800, framerate=(fraction)60/1;
                video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)720, framerate=(fraction)60/1;
                video/x-raw, format=(string)UYVY, width=(int)640, height=(int)480, framerate=(fraction)60/1;
                video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)800, framerate=(fraction)60/1;
                video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)720, framerate=(fraction)60/1;
                video/x-raw, format=(string)UYVY, width=(int)640, height=(int)480, framerate=(fraction)60/1;
                video/x-raw, format=(string)NV16, width=(int)1280, height=(int)800, framerate=(fraction)60/1;
                video/x-raw, format=(string)NV16, width=(int)1280, height=(int)720, framerate=(fraction)60/1;
                video/x-raw, format=(string)NV16, width=(int)640, height=(int)480, framerate=(fraction)60/1;
        properties:
                udev-probed = true
                device.bus_path = platform-tegra-capture-vi
                sysfs.path = /sys/devices/platform/tegra-capture-vi/video4linux/video0
                device.subsystem = video4linux
                device.product.name = "vi-output\,\ tevs\ 9-003e"
                device.capabilities = :capture:
                device.api = v4l2
                device.path = /dev/video0
                v4l2.device.driver = tegra-video
                v4l2.device.card = "vi-output\,\ tevs\ 9-003e"
                v4l2.device.bus_info = platform:tegra-capture-vi:1
                v4l2.device.version = 330344 (0x00050a68)
                v4l2.device.capabilities = 2216689665 (0x84200001)
                v4l2.device.device_caps = 69206017 (0x04200001)
        gst-launch-1.0 v4l2src ! ...
```
2. Bring up the camera (/dev/video0) with 1280x720 by Gstreamer pipeline:

```shell
DISPLAY=:0 gst-launch-1.0 v4l2src device=/dev/video0 ! \
"video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)720" ! \
nvvidconv ! nv3dsink sync=false
```

## Troubleshooting

**1. Cannot Find Cameras**

If you cannot bring up the cameras, you can check if the video device does exist.

```shell
$ ls /dev/video*  # List all video devices
/dev/video0  /dev/video1
```
If you cannot see the devices, you should check if the drivers have been probed.

**2. Occur Error: Could not get EGL display connection**

If you occurred the errors `nvbufsurftransform: Could not get EGL display connection` by rununing the Gstreamer command, you can modify the parameter 'DISPLAY' by the command:

```shell
# Check and Set environment parameter for 'DISPLAY'
$ export DISPLAY=$(w| tr -s ' '| cut -d ' ' -f 3 | grep :)
# Run Gstreamer pipeline
$ gst-launch-1.0 v4l2src device=/dev/video0 ! \
"video/x-raw, format=(string)UYVY, width=(int)1280, height=(int)720" ! \
nvvidconv ! nv3dsink sync=false
```