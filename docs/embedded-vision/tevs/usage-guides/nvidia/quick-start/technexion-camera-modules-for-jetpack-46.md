---
title: TechNexion Camera Modules for NVIDIA JetPack 4.6
description: TechNexion Camera Modules for NVIDIA JetPack 4.6
---

import ProductList from '@site/src/components/ProductList.jsx';

## Instruction

TechNexion Embedded Vision Solutions provide embedded system developers access to high-performance, industrial-grade camera solutions to accelerate their time to market for embedded vision projects.

:::warning

**We are no longer maintaining JetPack 4.6.x**

We don't maintain the camera drivers for JetPack 4.6.x anymore.  To use the latest drivers for TechNexion cameras on Jetson Nano, you will need to port the drivers from supported driver branches.

:::

## Supported Camera Modules

| **Camera Series** | **Products** |
| :---: | :---: |
| TEVI Series | <ProductList series="TEVI"/>TEVI-OV5640 |
| TEVS Series | <ProductList series="TEVS"/> |

:::warning
TEVI Series is not recommended for new designs.  Please consider TEVS.  These sensors are much improved.
:::

[More Camera Products Details...](https://www.technexion.com/products/)

## Supported NVIDIA Jetson Developer Kit

- [NVIDIA Jetson Jetson Nano](https://developer.nvidia.com/embedded/jetson-nano-developer-kit)

## Install TN Camera on Jetson Developer Kit

#### Adaptor for NVIDIA Jetson Nano Development Kit

**TEV-RPI15 Adaptor**
Connect TN camera with adaptor to Jetson Nano Developer Kit directly.

<a href="https://www.technexion.com/products/embedded-vision/mipi-csi2/evk/tevs-ar0144-c-s83-ir-rpi15/" target="_blank">
<img src="/img/tevi-ar0144-c-s83-ir-rpi15.png" width="400" height="400" />
</a>

#### Using TechNexion Pre-built modules

**Jetson Nano**
:::info
We recommend following the [Getting Started Guide](https://developer.nvidia.com/embedded/learn/get-started-jetson-nano-devkit) for Jetson Nano Developer Kit.
After that, you can follow the below method to install TechNexion Cameras Driver.
:::

1. Download pre-built modules.

```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/JetsonNano/pre-built-modules/latest/tn-camera-modules-jetson-nano.tar.gz
```

2. uncompress the modules.

```shell
tar -xf tn-camera-modules-jetson-nano.tar.gz
```

3. Run installation script.

```shell'
cd tn-camera-modules-jetson-nano/
sh tn_install.sh
```

4. After you agree to continue the installation, select the pre-installed modules that you want. The default module is TEVS cameras.

```shell
$ sh tn_install.sh
[sudo] password for ubuntu:
****** TechNexion Camera Driver Installation ******
This installation is easy to install TechnNexion Camera Drivers for NVIDIA
Jetson NANO Development Kits. Before start to install camera driver,
You should BACKUP your image to avoid any file you lost while installing process.
Do you want to continue?[Y/n]Y
Continuing with the installation...
Install EEPROM modules
Install TN-CAM modules: tevi_ov5640.ko
Install TN-CAM modules: tevi_ap1302.ko
Install TN-CAM modules: tevs.ko
Install TN-CAM DTB file: tn
Installed TN-CAM DTB file Done.
Install TN-CAM DTBO file: tevi-ov5640
Installed TN-CAM DTBO file Done.
Install TN-CAM DTBO file: tevi-ap1302
Installed TN-CAM DTBO file Done.
Install TN-CAM DTBO file: tevs
Installed TN-CAM DTBO file Done.
Select modules:
    [1]: TEVS: TEVS Series MIPI Cameras with TEVS-RPI15 Adaptor
    [2]: TEVI-AP1302: TEVI-AR Series Cameras with TEV-RPI15 Adaptor
    [3]: TEVI-OV5640: TEVI-OV5640 Cameras with TEV-RPI15 Adaptor
Which modules do you select?[default:1]
```

Note: You should reboot the device after installation.

---

## Bring up Camera by GStreamer

If you succeed in initialing the camera, you can follow the steps to open the camera.

1. Check the supported resolutions:

```shell
$ gst-device-monitor-1.0 Video/Source
Device found:

        name  : vi-output, tevi-ov5640 7-003c
        class : Video/Source
        caps  : video/x-raw, format=(string)YUY2, width=(int)2592, height=(int)1944, framerate=(fraction)15/1;
                video/x-raw, format=(string)YUY2, width=(int)1920, height=(int)1080, framerate=(fraction)30/1;
                video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)60/1;
                video/x-raw, format=(string)YUY2, width=(int)2592, height=(int)1944, framerate=(fraction)15/1;
                video/x-raw, format=(string)YUY2, width=(int)1920, height=(int)1080, framerate=(fraction)30/1;
                video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720, framerate=(fraction)60/1;
        properties:
                udev-probed = true
                device.bus_path = platform-54080000.vi
                sysfs.path = /sys/devices/50000000.host1x/54080000.vi/video4linux/video0
                device.subsystem = video4linux
                device.product.name = "vi-output\,\ tevi-ov5640\ 7-003c"
                device.capabilities = :capture:
                device.api = v4l2
                device.path = /dev/video0
                v4l2.device.driver = tegra-video
                v4l2.device.card = "vi-output\,\ tevi-ov5640\ 7-003c"
                v4l2.device.bus_info = platform:54080000.vi:0
                v4l2.device.version = 264701 (0x000409fd)
                v4l2.device.capabilities = 2216689665 (0x84200001)
                v4l2.device.device_caps = 69206017 (0x04200001)
        gst-launch-1.0 v4l2src ! ...
```
2. Bring up the camera (/dev/video0) with 1280x720 by Gstreamer pipeline:

```shell
DISPLAY=:0 gst-launch-1.0 v4l2src device=/dev/video0 ! "video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720" ! xvimagesink sync=false
```

## Troubleshooting

**1. Cannot Find Cameras**

If you cannot bring up the cameras, you can check if the video device does exist.

```shell
$ ls /dev/video*  # List all video devices
/dev/video0  /dev/video1
```
If you cannot see the devices, you should check if the drivers have been probed.

**2. Occur Error: Could not get EGL display connection**

If you occurred the errors `nvbufsurftransform: Could not get EGL display connection` by rununing the Gstreamer command, you can modify the parameter 'DISPLAY' by the command:

```shell
# Check and Set environment parameter for 'DISPLAY'
$ export DISPLAY=$(w| tr -s ' '| cut -d ' ' -f 3 | grep :)
# Run Gstreamer pipeline
$ gst-launch-1.0 v4l2src device=/dev/video0 ! "video/x-raw, format=(string)YUY2, width=(int)1280, height=(int)720" ! xvimagesink sync=false
```