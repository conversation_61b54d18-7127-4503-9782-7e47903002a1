---
title: TechNexion Camera Modules for JetPack 6.x
description: TechNexion Camera Modules for JetPack 6.x
---
import ProductList from '@site/src/components/ProductList.jsx';

## Instruction

TechNexion Embedded Vision Solutions provide embedded system developers access to high-performance, industrial-grade camera solutions to accelerate their time to market for embedded vision projects.

## Supported Camera Modules

| **Camera Series** | **Products** |
| :---: | :---: |
| TEVS Series | <ProductList series="TEVS"/> |
| FPD Link III Series | <ProductList series="VLS3"/> |
| GMSL2 Series | <ProductList series="VLS_GM2"/> |

[More Camera Products Details...](https://www.technexion.com/products/)

---
## Supported NVIDIA Jetson Developer Kit

- [NVIDIA Jetson Orin Nano](https://developer.nvidia.com/embedded/learn/get-started-jetson-orin-nano-devkit)

---

## Install TN Camera on Jetson Developer Kit

#### Adaptor for NVIDIA Jetson Orin Nano Development Kit

**TEV-RPI22 Adaptor**
> Connect TEVS camera and TEV-RPI22 adaptor to Jetson Orin Nano Developer Kit directly.

<a href="https://www.technexion.com/products/embedded-vision/mipi-csi2/evk/tevs-ar0144-c-s83-ir-rpi22/" target="_blank">
  <img src="/img/tevs-ar0144-c-s83-ir-rpi22.png" width="360" height="360" />
</a>

**VLS3-ORIN-EVK Adaptor for VLS3 Cameras**

Follow the video tutorial below to learn how to connect **VLS3 cameras** and the **VLS3-ORIN-EVK adapter** to the **Jetson Orin Nano Developer Kit**.

<iframe width="640" height="360" src="https://www.youtube.com/embed/Ggu97E-KmsA" title="VLS3-ORIN-EVK" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

**VLS-GM2-ORIN-EVK Adaptor for VLS-GM2 Cameras**
> Follow the video to connect VLS-GM2 cameras and VLS-GM2-ORIN-EVK adaptor to Jetson Orin Nano Developer Kit.

---

#### Method 1 - Using TechNexion Pre-built modules

**Jetson Orin Nano**
:::info
We recommend following the [Getting Started Guide](https://developer.nvidia.com/embedded/learn/get-started-jetson-orin-nano-devkit) for Jetson Orin Nano Developer Kit.
After that, you can follow the below method to install TechNexion Cameras Driver.
:::
1. Download pre-built modules.

**JetPack 6.1**
```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/pre-built-modules/latest/JP61/tn_camera_modules_jp61.tar.gz
```

**JetPack 6.2**
```
wget https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/pre-built-modules/latest/JP62/tn_camera_modules_jp62.tar.gz
```

2. uncompress the modules.

```shell
tar -xf tn_camera_modules_<jp_ver>.tar.gz
```

3. Run installation script.

```shell'
cd tn_camera_modules_<release_time>/
./tn_install.sh
```

4. After you agree to continue the installation, select the pre-installed modules that you want. The default module is TEVS cameras.

```shell
$ ./tn_install.sh
****** TechNexion Camera Driver Installation ******
This installation is easy to install TechnNexion Camera Drivers for Nvidia
Jetson Orin NANO Development Kits. Before start to install camera driver,
You should BACKUP your image to avoid any file you lost while installing process.
Do you want to continue?[Y/n]Y
Continuing with the installation...
Install TN-CAM modules: max96724.ko
Install TN-CAM modules: max96717.ko
Install TN-CAM modules: vls3.ko
Install TN-CAM modules: tevs.ko
Install TN-CAM DTBO file: tevs-dual
Installed TN-CAM DTB file Done.
Install TN-CAM DTBO file: vls3
Installed TN-CAM DTB file Done.
Install TN-CAM DTBO file: vls-gm2
Installed TN-CAM DTB file Done.
Install TN-CAM DTBO file: vls-gm2-fsync
Installed TN-CAM DTB file Done.
Install TN-CAM DTBO file: vls-gm2-tunnel
Installed TN-CAM DTB file Done.
Install TN-CAM DTBO file: vls-gm2-tunnel-fsync
Installed TN-CAM DTB file Done.
Select modules:
    [1]: TEVS: TEVS Series MIPI Cameras with TEV-RPI22 Adaptor
    [2]: VLS3: VLS3 Series Cameras with VLS3-ORIN-EVK Adaptor
    [3]: VLS-GM2: VLS-GM2 Series Cameras with VLS-GM2-ORIN-EVK Adaptor
Which modules do you select?[default:1]
```

Note: You should reboot the device after installation.

---

#### Method 2 - Build drivers from source code

Please follow the [guide](technexion-camera-modules-for-jetpack-6x) to build camera driver modules.

---

## Bring up Camera by GStreamer

If you succeed in initialing the camera, you can follow the steps to open the camera.

1. Check the supported resolutions:

```shell
$ gst-device-monitor-1.0 Video/Source
Device found:

        name  : vi-output, tevs 10-0048
        class : Video/Source
        caps  : video/x-raw, format=UYVY, width=640, height=480, framerate=120/1
                video/x-raw, format=UYVY, width=1280, height=720, framerate=120/1
                video/x-raw, format=UYVY, width=1920, height=1080, framerate=60/1
                video/x-raw, format=UYVY, width=1920, height=1200, framerate=60/1
                video/x-raw, format=NV16, width=640, height=480, framerate=120/1
                video/x-raw, format=NV16, width=1280, height=720, framerate=120/1
                video/x-raw, format=NV16, width=1920, height=1080, framerate=60/1
                video/x-raw, format=NV16, width=1920, height=1200, framerate=60/1
                video/x-raw, format=UYVY, width=640, height=480, framerate=120/1
                video/x-raw, format=UYVY, width=1280, height=720, framerate=120/1
                video/x-raw, format=UYVY, width=1920, height=1080, framerate=60/1
                video/x-raw, format=UYVY, width=1920, height=1200, framerate=60/1
        properties:
                object.path = v4l2:/dev/video0
                device.api = v4l2
                media.class = Video/Source
                api.v4l2.path = /dev/video0
                api.v4l2.cap.driver = tegra-video
                api.v4l2.cap.card = "vi-output\,\ tevs\ 10-0048"
                api.v4l2.cap.bus_info = platform:tegra-capture-vi:1
                api.v4l2.cap.version = 5.15.148
                api.v4l2.cap.capabilities = 84200001
                api.v4l2.cap.device-caps = 04200001
                device.id = 33
                node.name = v4l2_input.platform-tegra-capture-vi
                node.description = "vi-output\,\ tevs\ 10-0048"
                factory.name = api.v4l2.source
                node.pause-on-idle = false
                factory.id = 10
                client.id = 32
                clock.quantum-limit = 8192
                media.role = Camera
                node.driver = true
                object.id = 34
                object.serial = 34
        gst-launch-1.0 pipewiresrc path=34 ! ...
```
2. Bring up the camera (/dev/video0) with 1280x720 by Gstreamer pipeline:

```shell
DISPLAY=:0 gst-launch-1.0 v4l2src device=/dev/video0 ! \
"video/x-raw, format=UYVY, width=1280, height=720" ! xvimagesink sync=false
```

## Troubleshooting

**1. Cannot Find Cameras**

If you cannot bring up the cameras, you can check if the video device does exist.

```shell
$ ls /dev/video*  # List all video devices
/dev/video0  /dev/video1
```
If you cannot see the devices, you should check if the drivers have been probed.

**2. Occur Error: Could not get EGL display connection**

If you occurred the errors `nvbufsurftransform: Could not get EGL display connection` by rununing the Gstreamer command, you can modify the parameter 'DISPLAY' by the command:

```shell
# Check and Set environment parameter for 'DISPLAY'
$ export DISPLAY=$(w| tr -s ' '| cut -d ' ' -f 3 | grep :)
# Run Gstreamer pipeline
$ gst-launch-1.0 v4l2src device=/dev/video0 ! \
"video/x-raw, format=UYVY, width=1280, height=720" ! xvimagesink sync=false
```