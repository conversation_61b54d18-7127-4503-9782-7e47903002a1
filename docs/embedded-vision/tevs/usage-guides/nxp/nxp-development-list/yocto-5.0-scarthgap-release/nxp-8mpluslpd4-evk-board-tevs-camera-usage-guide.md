---
title: "NXP 8MPLUSLPD4-EVK board TEVS Camera Usage Guide"
description: "NXP 8MPLUSLPD4-EVK board TEVS Camera Usage Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<center>
<img src="/img/nxp-imx8m-plus-evk_1000x571.jpg" width="800" height="457" />
</center>

## 🚀 Introduction

This article guides you how to get started using TechNexion camera modules on **NXP 8MPLUSLPD4-EVK** board.

---
## 📸 Supported Camera Modules

<img src="/img/camera-seneor-comparsion.jpg" style={{float: 'right', margin: '50 50 10px 10px'}} width="580" height="290" />

|  **Camera Series**  |   **Products**    |
|:---:|-------|
|TEVS| <ProductList series="TEVS"/>|


:::info
**TEVI-OV5640 and TEVI-AR Series Cameras are deprecated starting from Yocto 4.2**<br/>
Support for TEVI-OV5640 and TEVI-AR Series Cameras is available only up to **Yocto 4.0**.
If you need to use these camera modules, please refer to the 🔗 [**Yocto 4.0 (Kirkstone) 2024Q1 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp40-2024q1.md).
 :::

* * *
## 🔧 Hardware Setup Instructions

### Connect the Adapter Board and Camera Cable

To set up TechNexion camera, connect the adapter board and camera cable to the **MIPI CSI1** port on the EVK board.

[<img src="/img/tevs-ar0144-c-s83-ir-mini-sas.png" style={{float: 'left', margin: '50 50 10px 10px'}} width="240" height="240" />](https://www.technexion.com/shop/embedded-vision/mipi-csi2/evk/tevs-ar0144-c-s83-ir-nxp/)

<iframe style={{float: 'right', margin: '50 50 10px 10px'}} width="492" height="277" width="492" height="277" src="https://www.youtube.com/embed/IjM6YU2Dkhs?start=47" title="NXP i.MX8M Plus Embedded Vision (camera) Solutions" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>
<br/>

:::tip **Why Use Only MIPI CSI1?**
**MIPI CSI2 is disabled by default.**<br/>
CSI1 and CSI2 **share the same reset pin(CSI_RST)**, so the EVK board **can't run both cameras at the same time**.
:::
:::tip Recommended
**MIPI CSI2** uses the **ISI1 video channel**, which only supports up to **2K resolution**.<br/>
Want to use MIPI CSI2? You’ll need to **manually update the device tree**.
:::

:::info Better Option for Production Use
If you're moving to mass production, we suggest switching to the 🔗 [WB-EDM-G-IMX8M-PLUS](https://www.technexion.com/products/system-on-modules/evk/wb-edm-g-imx8m-plus/) platform.<br/>
It’s more compact and better suited for custom applications.
:::
* * *
## 🧰 Camera Installation Instructions

We provide some methods to install camera driver and device tree blobs.
Depending on your needs, you can choose the most suitable method. If you are new to this, we recommend starting with [**Method 1**](#method-1---using-technexion-pre-built-image). If you want to customized your Yocto project or Linux kernel, you can try [**Method 2**](#method-2---build-yocto-image-with-technexion-bsp) or [**Method 3**](#method-3---build-linux-kernel-with-technexion-source-code). If you have a custom Linux kernel and device tree, you can try [**Method 4**](#method-4---build-custom-linux-kernel-with-camera-driver).
* * *
##
### Method 1 - Using TechNexion Pre-built image

#### Prepare Yocto demo image for testing TechNexion camera
To test TechNexion TEVS Series cameras, you need a demo image that includes the required Device Tree Blobs (DTBs) and camera drivers.

#### Download and Select the Correct Image for Your EVK

There are two kinds of DRAM types for i.mx8mp evk boards: it's equipped with DDR4 or LPDDR4.<br/>
Prebuilt demo images can be available for download via TechNexion's server.

#### **Download Link**:
🔗 [Supported Release List (Instructions for IMX8MP-DDR4)](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-DDR4/archived/)<br/>
🔗 [Supported Release List (Instructions for IMX8MP-LPDDR4)](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-LPDDR4/archived/)

#### Flashing the Image
You can flash the image to an SD Card using the following methods:

:::tip  **Boot Mode**
Make sure your EVK board is set to **boot from SD card**.<br/>
Refer to the official NXP guide 🔗 [Getting Started with the i.MX 8M Plus EVK](https://www.nxp.com/document/guide/getting-started-with-the-i-mx-8m-plus-evk:GS-iMX-8M-Plus-EVK)
:::

<Tabs groupId="Flashing the Image">
  <TabItem value="windows" label="For Windows Users">
Use **balenaEtcher** to write the image to your SD card:

🔗 [<img src="/img/balenaetcher-vector-logo.png" width="250" height="50" />](https://www.balena.io/etcher/)
  </TabItem>
  <TabItem value="linux" label="For Linux Users">
Use `bmap-tools` for a fast and reliable flashing process to the SD card:

```bash
sudo apt-get update
sudo apt-get install bmap-tools
```
  </TabItem>
</Tabs>

* * *
### Method 2 - Build Yocto image with TechNexion BSP

TechNexion supports building a Yocto-based Linux image tailored for camera modules using the following kernel and branch.

If you are using prebuilt demo image, you can skip this section. Please refer to [**📸 Camera Testing Instructions**](#-camera-testing-instructions) section for camera testing steps.

#### Supported Linux Kernel

| Linux Kernel Version | Yocto Branch                            |
|----------------------|------------------------------------------|
| 6.6.52               | `tn-imx_6.6.52_2.2.0-stable`              |

#### Source and Build Instructions

🔗 [Fetch Yocto source](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/scarthgap_6.6.y-stable)<br/>
📖 [Build Yocto (Instructions for NXP i.MX 8M Plus EVK)](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/scarthgap_6.6.y-stable?tab=readme-ov-file#for-nxp-evk-with-technexion-tevs-and-vls-camera-support)

---
### Method 3 - Build Linux kernel with TechNexion source code
#### Clone the Kernel Source
Fetch and compile Linux kernel.

:::info Build Enviroment
- Host OS: **Ubuntu 18.04/20.04**
- Toolchain: `gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu`
:::
```shell
# For Kernel 6.6.52
$ git clone https://github.com/TechNexion/linux-tn-imx.git -b tn-imx_6.6.52_2.2.0-stable
```

#### Set cross-compile environment & the kernel config:
```shell
$ export PATH=$PATH:/opt/gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu/bin
$ export ARCH=arm64
$ export CROSS_COMPILE=/opt/gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-
$ cd linux-tn-imx
~/linux-tn-imx$ make imx_v8_defconfig
```
#### Compile the kernel & module driver:
```shell
# compile kernel
~/linux-tn-imx$ make
# create kernel module folder
~/linux-tn-imx$ mkdir -p ./modules
# compile module driver
~/linux-tn-imx$ rm -rf ./modules/*
~/linux-tn-imx$ make INSTALL_MOD_PATH=./modules modules_install
```
#### Change kernel Image, DTB & modules:
```shell
~/linux-tn-imx/$ cp arch/arm64/boot/Image  <mount_folder>/boot/Image
~/linux-tn-imx/$ cp arch/arm64/boot/dts/freescale/imx8mp-evk-tevs.dtb  <mount_folder>/boot/
~/linux-tn-imx/$ sudo cp -r ./modules/lib/modules/*  <mount_folder>/root/lib/modules/
```
---
### Method 4 - Build custom Linux kernel with camera driver

1. Download the camera driver and device tree blobs.

    ```shell
    $ git clone https://github.com/TechNexion-Vision/nxp_evk_camera.git -b tn-imx_6.6.52_2.2.0-stable
    ```

2. Copy to your kernel source code.

    ```shell
    $ cd nxp_evk_camera/
    ~/nxp_evk_camera$ cp -r driver/media/i2c/tevs/ <fetch_kernel_folder>/driver/media/i2c/

    ~/nxp_evk_camera$ cp arch/arm64/boot/dts/freescale/imx8mp-evk-tevs.dts <fetch_kernel_folder>/arch/arm64/boot/dts/freescale/
    ```

3. Modify makefile to add driver.

    ```shell
    $ cd <fetch_kernel_folder>/driver/media/i2c/
    ~/<fetch_kernel_folder>/driver/media/i2c/$ vi Makefile
    ```

    Add this line in Makefile.
    ```shell
    obj-$(CONFIG_VIDEO_TEVS) += tevs/
    ```

:::info
you can refer to [linux-tn-imx/drivers/media/i2c/Makefile](https://github.com/TechNexion/linux-tn-imx/blob/tn-imx_6.6.52_2.2.0-stable/drivers/media/i2c/Makefile)
:::

4. Modify Kconfig to add camera config.

    ```shell
    ~/<fetch_kernel_folder>/driver/media/i2c/$ vi Kconfig
    ```

    Add this part under "Camera sensor devices" menu in Kconfig.
    ```shell
    config VIDEO_TEVS
      tristate "TechNexion TEVS sensor support"
      depends on OF
      depends on GPIOLIB && I2C && VIDEO_V4L2_SUBDEV_API
      depends on MEDIA_CAMERA_SUPPORT
      select V4L2_FWNODE
      default y
      help
        This is a Video4Linux2 sensor driver for the TechNexion
        TEVS camera sensor with a MIPI CSI-2 interface.
    ```

:::info Build Enviroment
you can refer to [linux-tn-imx/drivers/media/i2c/Kconfig](https://github.com/TechNexion/linux-tn-imx/blob/tn-imx_6.6.52_2.2.0-stable/drivers/media/i2c/Kconfig)
:::

5. Modify makefile to add device tree.

    ```shell
    $ cd <fetch_kernel_folder>/arch/arm64/boot/dts/freescale/
    ~/<fatch_kernel_folder>/arch/arm64/boot/dts/freescale/$ vi Makefile
    ```

    Add this line in Makefile.
    ```shell
    dtb-$(CONFIG_ARCH_MXC) += imx8mp-evk-tevs.dtb
    ```

:::info Build Enviroment

you can refer to [linux-tn-imx/arch/arm64/boot/dts/freescale/Makefile](https://github.com/TechNexion/linux-tn-imx/blob/tn-imx_6.6.52_2.2.0-stable/arch/arm64/boot/dts/freescale/Makefile)
:::

6. Compile the kernel & module driver.

    Finally, you can start compiling your new Image files, then copy and replace the Image files in the SD card.

---
## 📸 Camera Testing Instructions

### Specify Camera DTBO in U-Boot
####  Manual DTBO Setup
1. Connect the **debug console cable** to the baseboard (via UART/USB).

2. **Power on the board** and interrupt the boot process.
   Keep pressing `Enter` when the following message appears:
   `Hit any key to stop autoboot:`

3. Specify the appropriate device tree for your camera using the `fdtfile` environment variable in U-Boot:

```shell
u-boot=> setenv fdtfile imx8mp-evk-tevs.dtb
```

4. Save and continue the boot process:

```shell
u-boot=> saveenv
u-boot=> boot
```
---

## 🎥 Start Camera Video Stream via GStreamer
### Check Camera Availability

Use the v4l2-ctl tool to list connected video devices.
```bash
$ v4l2-ctl --list-device
```
Example output:
```shell
mxc-isi-cap (platform:32e00000.isi:cap_devic):

/dev/video{X}
```

The following is example.

 <img src="/img/mxc-isi-cap-snapshot-tevs-nxp-imx8mp-evk.png" width="480" height="320" />

---

### Launch GStreamer Pipeline
Replace `{X}`, `<res_w>` and `<res_h>` with your capture device number and desired resolution:
```shell

$ gst-launch-1.0 v4l2src device=/dev/video{X} ! \
  video/x-raw,width=<x>,height=<y> ! waylandsink
```
* * *

## 🚨 Troubleshooting

Boot up **NXP 8MPLUSLPD4-EVK** and check initialization of **TEVS driver**.
If it shows below messages, the driver is initialized correcly.
```shell
$ dmesg -t | grep tevs

  tevs 1-0048: tevs_probe() device node: tevs@48
  tevs 1-0048: Version:********
  tevs 1-0048: Product:TEVS-AR0234, HeaderVer:3, MIPI_Rate:800
  tevs 1-0048: probe success
```

Ensure camera device tree blob(DTB) is specified correctly.
```shell
$ dmesg -t | grep -i model
  Machine model: NXP i.MX8MPlus EVK board with TechNexion TEVS camera
```