---
title: "PICO-IMX8M TEVS Camera Usage Guide"
description: "PICO-IMX8M TEVS Camera Usage Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## 🚀 Introduction

This article guides you how to get started using TechNexion camera modules on **PICO-IMX8M Mini** board.

:::warning Background knowledge needed
 You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS).
:::

:::info e.g. PICO-PI Baseboard
This article uses the **PICO-PI** baseboard as an example, based on the [**Yocto 4.2 (Mickledore) 2024Q2 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp42-2024q2.md).
The corresponding Linux kernel version is **6.1.55_2.2.0**.
:::
---
## 📸 Supported Camera Modules


|  **Camera Series**  |   **Products**    |
|:---:|-------|
|TEVS| <ProductList series="TEVS"/>|

---
## 🧩 Supported Boards

  **SoM**  | **Board**   |
:---:|-------|
PICO-IMX8M-MINI| PICO-PI <br/> PICO-WIZARD


* * *
## 🔧 Hardware Setup Instructions

Kindly refer to the following pages:

🔗 [PICO-PI-IMX8M-MINI](/docs/system-on-modules/pico/pico-imx8m-mini/development-kits/pico-pi-imx8m-mini.md) <br/>
🔗 [PICO-WIZARD-IMX8M-MINI](/docs/system-on-modules/pico/pico-imx8m-mini/development-kits/pico-wizard-imx8m-mini.md)

---

## 💻 Prepare Yocto demo image for testing TechNexion camera

To test TechNexion cameras, you will need a Yocto-based demo image that includes the necessary device tree blobs and camera drivers.

### 🔽 Downloading the Demo Image
Prebuilt demo images are available for download on TechNexion's official server.

#### **Download Link**:
🔗 [Demo Image for PICO-IMX8M Mini](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-4.2-qt6_qca9377_mipi5-1280x720_20240820.zip)

---

### 💾 Flashing the Image

You can flash the image to either **e.MMC** or an **SD Card** using one of the following methods:

### 1. Using `uuu` Tool (Universal Update Utility)

TechNexion provides a guide to flash the image using the `uuu` tool:
🔗 [How to Flash with UUU](/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

:::tip  **Boot Mode**
Before flashing, ensure the board is set to **Serial Download Mode** in the boot configuration. You can refer to [Hardware Setup Instructions](#-hardware-setup-instructions) for more details.
:::

---

### 2. Using `ums` Command in U-Boot (USB Mass Storage)

Alternatively, you can write the image directly to flash storage over **USB-OTG** using U-Boot’s `ums` command:
🔗 [Using UMS in U-Boot](/docs/embedded-software/linux/usage-guides/loading-software/using-u-boots-ums-command-to-write-flash-storage-over-usb-otg)

:::tip  **U-Boot Requirement**
The board must be booted with a version of U-Boot that supports the `ums` command. Typically, this is done from the existing e.MMC.
:::

* * *

## 🛠️ Build Yocto

TechNexion supports building a Yocto-based Linux image tailored for camera modules using the following kernel and branch.

### 📦 Supported Linux Kernel
| Linux Kernel Version | Yocto Branch                            |
|----------------------|------------------------------------------|
| 6.1.55               | `tn-imx_6.1.55_2.2.0-stable`              |

---

### 📁 Source and Build Instructions

🔗 [Fetch Yocto Source](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/mickledore_6.1.y-stable)<br/>
📖 [Build Yocto (Instructions for PICO-IMX8MM)](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/mickledore_6.1.y-stable?tab=readme-ov-file#for-pico-imx8mm)<br/>

* * *

## 📸 Camera Testing Instructions

### Specify Camera DTBO in U-Boot

1. Connect the **debug console cable** to the baseboard.
2. **Power on the board** and interrupt the boot process.
   Keep pressing `Enter` when the following message appears:
   `Hit any key to stop autoboot:`

3. **Set the correct camera overlay (DTBO)** using the U-Boot environment variable:

  ```bash
  u-boot=> setenv dtoverlay $dtoverlay tevs
  ```

4. Save and continue the boot process:
  ```bash
  u-boot=> saveenv
  u-boot=> boot
  ```
---

## 🎥 Start Camera Video Stream via GStreamer
### Check Camera Availability

Use the v4l2-ctl tool to list connected video devices.

```bash
$ v4l2-ctl --list-device
```
Example output:

```bash
i.MX6S_CSI (platform:32e20000.csi1_bridge):
        /dev/video0
```
In this example, /dev/video0 is the capture device connected via CSI.

---

### Launch GStreamer Pipeline
Replace `<res_w>`, `<res_h>`, `<x>`, and `<y>` with your desired resolution and screen dimensions:

```shell
$ gst-launch-1.0 v4l2src device=/dev/video0 ! \
  video/x-raw, width=<res_w>, height=<res_h> ! \
  imxvideoconvert_g2d ! \
  waylandsink window-width=<x> window-height=<y> sync=false
```

* * *

## 🚨 Troubleshooting

Ensure camera device tree blob overlay(DTBO) is specified correctly in u-boot.<br/>
  ```shell
    u-boot=> printenv dtoverlay
    dtoverlay=ili9881c tevs
  ```

We can check whether camera have been initialized correctly.<br/>
For **TEVS cameras**:
```shell
  $ dmesg| grep tevs
  [    2.886537] tevs 1-0048: tevs_probe() device node: tevs@48
  [    3.457747] tevs 1-0048: Version:*********
  [    3.468184] tevs 1-0048: Product:TEVS-AR1335, HeaderVer:3, MIPI_Rate:800
  [    3.505338] mxc_mipi-csi 32e30000.mipi_csi: Registered sensor subdevice: tevs 1-0048
  [    3.567084] tevs 1-0048: probe success
```


Ensure camera device tree blob(DTB) is specified correctly.
```shell
  $ dmesg -t | grep -i model
  Machine model: TechNexion PICO-IMX8MM and PI baseboard
```

