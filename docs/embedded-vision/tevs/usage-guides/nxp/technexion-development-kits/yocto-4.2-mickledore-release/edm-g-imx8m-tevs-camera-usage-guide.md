---
title: "EDM-G-IMX8M TEVS Camera Usage Guide"
description: "EDM-G-IMX8M TEVS Camera Usage Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## 🚀 Introduction

This article guides you how to get started using TechNexion camera modules on **EDM-G-IMX8M Plus/Mini** board.

:::warning Background knowledge needed
 You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS).
:::

:::info e.g. EDM-G-WB Baseboard
This article uses the **EDM-G-WB** baseboard as an example, based on the [**Yocto 4.2 (Mickledore) 2024Q2 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp42-2024q2.md).
The corresponding Linux kernel version is **6.1.55_2.2.0**.
:::
---
## 📸 Supported Camera Modules

<Tabs groupId="supported-cameras">
  <TabItem value="tevs" label="TEVS Series">
|  **Camera Series**  |   **Products**    |
|:---:|-------|
|TEVS| <ProductList series="TEVS"/>|
  </TabItem>
  <TabItem value="vls3" label="VLS3 Series">
|  **Camera Series**  |   **Products**    |
|:---:|-------|
|VLS3| <ProductList series="VLS3"/>|
  </TabItem>
</Tabs>

:::info
**TEVI-OV5640 and TEVI-AR Series Cameras are deprecated starting from Yocto 4.2**<br/>
Support for TEVI-OV5640 and TEVI-AR Series Cameras is available only up to **Yocto 4.0**.
If you need to use these camera modules, please refer to the 🔗 [**Yocto 4.0 (Kirkstone) 2024Q1 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp40-2024q1.md).
 :::
---
## 🧩 Supported Boards

  **SoM**  | **Board**   |
:---:|-------|
EDM-IMX8M-MINI| EDM-G-WB <br/> EDM-G-WIZARD
EDM-IMX8M-PLUS| EDM-G-WB <br/> EDM-G-WIZARD

* * *
## 🔧 Hardware Setup Instructions

### 1. Power Supply Preparation

- Use a **DC 5V or 12V** power cable to supply power to the board.
- If you're using **FPD-Link III** SerDes, a **12V power adapter (minimum 3A)** is required. Connect it via the **barrel jack connector**.

 <img src="/img/wb-edm-g-12vdc-power.png" width="360" height="300" />


:::info
The **EDM-G-WB** can also be powered via **USB Type-C (5V)**.
:::
---
### 2. Debug Console Connection

- Prepare a **USB-to-UART cable** if you're connecting to a PC.
- Connect the cable to the **debug console port** on the **EDM-G-WB** board.
---
### 3. Camera Interface (CSI) Overview

- The board features **two MIPI-CSI-2 interfaces**, referred to as **CSI1** and **CSI2**.
- These use **70-pin board-to-board connectors** manufactured by **Hirose**.
- The image below shows a **TEVS camera** connected to the **CSI1** interface.

 <img src="/img/wb-edm-g-imx8mp-csi1-camera-connected.png" width="480" height="360" />


* * *

## 💻 Prepare Yocto demo image for testing TechNexion camera

To test TechNexion cameras, you will need a Yocto-based demo image that includes the necessary device tree blobs and camera drivers.

### 🔽 Downloading the Demo Image
Prebuilt demo images are available for download on TechNexion's official server.

#### **Download Link**:
🔗 [Supported Release List (Instructions for EDM-G-IMX8MP)](https://ftp.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/)<br/>
🔗 [Supported Release List (Instructions for EDM-G-IMX8MM)](https://ftp.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/)

---

### 💾 Flashing the Image

You can flash the image to either **e.MMC** or an **SD Card** using one of the following methods:

### 1. Using `uuu` Tool (Universal Update Utility)

TechNexion provides a guide to flash the image using the `uuu` tool:
🔗 [How to Flash with UUU](/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

:::tip  **Boot Mode**
Before flashing, ensure the board is set to **Serial Download Mode** in the boot configuration. You can refer to 🔗 [Boot Mode Selection of EDM-G-WB](/docs/system-on-modules/edm/edm-g-imx8m-plus/development-kits/wb-edm-g-imx8m-plus.md) for more details.
:::

---

### 2. Using `ums` Command in U-Boot (USB Mass Storage)

Alternatively, you can write the image directly to flash storage over **USB-OTG** using U-Boot’s `ums` command:
🔗 [Using UMS in U-Boot](/docs/embedded-software/linux/usage-guides/loading-software/using-u-boots-ums-command-to-write-flash-storage-over-usb-otg)

:::tip  **U-Boot Requirement**
The board must be booted with a version of U-Boot that supports the `ums` command. Typically, this is done from the existing e.MMC.
:::

* * *

## 🛠️ Build Yocto

TechNexion supports building a Yocto-based Linux image tailored for camera modules using the following kernel and branch.

### 📦 Supported Linux Kernel
| Linux Kernel Version | Yocto Branch                            |
|----------------------|------------------------------------------|
| 6.1.55               | `tn-imx_6.1.55_2.2.0-stable`              |

---

### 📁 Source and Build Instructions

🔗 [Fetch Yocto Source](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/mickledore_6.1.y-stable)<br/>
📖 [Build Yocto (Instructions for EDM-G-IMX8MP)](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/mickledore_6.1.y-stable?tab=readme-ov-file#for-edm-g-imx8mp)<br/>
📖 [Build Yocto (Instructions for EDM-G-IMX8MM)](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/mickledore_6.1.y-stable?tab=readme-ov-file#for-edm-g-imx8mm)<br/>

* * *

## 📸 Camera Testing Instructions

### Specify Camera DTBO in U-Boot

:::tip **Auto Camera Detection**
If you are using **TEVI AR series** or **TEVS** cameras, you can skip this step. These models support automatic camera detection in U-Boot.
:::

####  Manual DTBO Setup

1. Connect the **debug console cable** to the baseboard (via UART).
2. **Power on the board** and interrupt the boot process.
   Keep pressing `Enter` when the following message appears:
   `Hit any key to stop autoboot:`

3. **Set the correct camera overlay (DTBO)** using the U-Boot environment variable:

<Tabs groupId="camera-dtbo">
  <TabItem value="tevs" label="TEVS Cameras">
  ```bash
  u-boot=> setenv dtoverlay tevs
  ```
</TabItem>
  <TabItem value="vls3" label="VLS3 Cameras">
  ```bash
  u-boot=> setenv dtoverlay vls
  ```
</TabItem>
</Tabs>

4. Save and continue the boot process:
  ```bash
  u-boot=> saveenv
  u-boot=> boot
  ```
---

## 🎥 Start Camera Video Stream via GStreamer
### Check Camera Availability

Use the v4l2-ctl tool to list connected video devices.

```bash
$ v4l2-ctl --list-device
```
Example output:

```bash
():
/dev/v4l-subdev0
():
/dev/v4l-subdev1

FSL Capture Media Device (platform:32c00000.bus:camera):
/dev/media0

mxc-isi-cap (platform:32e00000.isi:cap_device):
/dev/video0

mxc-isi-cap (platform:32e02000.isi:cap_device):
/dev/video1
```
In this example, /dev/video0 is the capture device connected via CSI1.

---

### Launch GStreamer Pipeline
Replace `<res_w>`, `<res_h>`, `<x>`, and `<y>` with your desired resolution and screen dimensions:

```shell
$ gst-launch-1.0 v4l2src device=/dev/video0 ! \
  video/x-raw, width=<res_w>, height=<res_h> ! \
  imxvideoconvert_g2d ! \
  waylandsink window-width=<x> window-height=<y> sync=false
```
:::warning
**`imxvideoconvert_g2d`** is optimized for the i.MX8MP platform and leverages ISI hardware for format conversion.
You may optionally use **`videoconvert`**, but it is typically unnecessary.
:::

* * *

## 🚨 Troubleshooting

Ensure camera device tree blob overlay(DTBO) is specified correctly in u-boot.<br/>
<Tabs groupId="u-boot-dtbo">
  <TabItem value="tevs" label="TEVS Cameras">
  ```shell
    u-boot=> printenv dtoverlay
    dtoverlay=tevs
  ```
</TabItem>
  <TabItem value="vls3" label="VLS3 Cameras">
  ```shell
    u-boot=> printenv dtoverlay
    dtoverlay=vls
  ```
</TabItem>
</Tabs>

We can check whether camera have been initialized correctly.<br/>
For **TEVS cameras**:
```shell
  $ dmesg -t | grep tevs
    tevs 1-0048: tevs_probe() device node: tevs@48
    tevs 1-0048: Version:********
    tevs 1-0048: Product:TEVS-AR0144, HeaderVer:3, MIPI_Rate:800
    tevs 1-0048: probe success
    mx8-img-md: Registered sensor subdevice: tevs 1-0048 (1)
    mx8-img-md: created link [tevs 1-0048] => [mxc-mipi-csi2.0]
```


Ensure camera device tree blob(DTB) is specified correctly.
```shell
  $ dmesg -t | grep -i model
    Machine model: TechNexion EDM-G-IMX8MP and WB baseboard
```

