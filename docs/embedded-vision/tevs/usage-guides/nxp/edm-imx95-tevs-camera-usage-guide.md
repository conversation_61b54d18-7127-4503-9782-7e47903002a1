---
title: "EDM-IMX95 TEVS Camera Usage Guide"
description: "EDM-IMX95 TEVS Camera Usage Guide"
---

import ProductList from '@site/src/components/ProductList.jsx';

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## 🚀 Introduction

This article guides you how to get started using TechNexion camera modules on **EDM-IMX95 EVM** board.

:::warning Background knowledge needed
 You must have the background knowledge to modify the kernel configuration, rebuild, and replace the kernel and the device tree source (DTS).
:::

:::info e.g.  IMX95-EVM Baseboard
This article uses the **IMX95-EVM** baseboard as an example,
based on the 🔗 [**Yocto 5.0 (Scarthgap) 2025Q2 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp50-2025q2.md).
The corresponding Linux kernel version is **6.6.52_2.2.0**.
:::
---
## 📸 Supported Camera Modules

<Tabs groupId="supported-cameras">
  <TabItem value="tevs" label="TEVS Series">
|  **Camera Series**  |   **Products**    |
|:---:|-------|
|TEVS| <ProductList series="TEVS"/>|
  </TabItem>
  <TabItem value="vls3" label="VLS3 Series">
|  **Camera Series**  |   **Products**    |
|:---:|-------|
|VLS3| <ProductList series="VLS3"/>|
  </TabItem>
</Tabs>

---
## 🧩 Supported Boards

  **SoM**  | **Board**   |
:---:|-------|
[EDM-IMX95](https://www.technexion.com/products/system-on-modules/edm/edm-imx95/)| IMX95-EVM

* * *
## 🔧 Hardware Setup Instructions

### 1. Power Supply Preparation

- Use a **DC 5V or 12V** power cable to supply power to the board.
- If you're using **FPD-Link III** SerDes, a **12V power adapter (minimum 3A)** is required. Connect it via the **barrel jack connector**.
 <img src="/img/wb-edm-g-12vdc-power.png" width="360" height="300" />

---
### 2. Debug Console Connection

- Prepare a **USB-to-UART cable** if you're connecting to a PC.
- Connect it to the **UART_A_A55** connector.
---
### 3. Display Output Connection

- Prepare a **10.1 inch LVDS panel** for output display.
- Connect the display to the **LVDS connector** on the board.
---
### 4. Camera Interface (CSI) Overview
- The board features **two MIPI-CSI-2 interfaces**, referred to as **CSI1** and **CSI2**.
- These use **70-pin board-to-board connectors** manufactured by **Hirose**.
- The image below shows a **TEVS camera** connected to the **CSI2** interface.

<img src="/img/edm-imx95-evm-top-camera.jpg" width="480" height="360" />
<figcaption><strong>Figure 1.</strong> TEVS camera module plugged into the CSI2 connector using a ribbon cable.</figcaption>
<br/>

:::tip CSI2 Switch
If you are using the **CSI2** connector, please remember to set the **SW2** switch to the **ON** position to enable it.
:::

<img src="/img/edm-imx95-evm-bottom-camera.jpg" width="480" height="360" />
<figcaption><strong>Figure 2.</strong> TEVS camera module plugged into the CSI1 connector using a ribbon cable.</figcaption>
<br/>

* * *

## 💻 Prepare Yocto demo image for testing TechNexion camera

To test TechNexion cameras, you will need a Yocto-based demo image that includes the necessary device tree blobs and camera drivers.

### 🔽 Downloading the Demo Image
Prebuilt demo images are available for download on TechNexion's official server.

#### **Download Link**:
🔗 [Supported Release List](https://ftp.technexion.com/demo_software/EDM/IMX9/)


### 💾 Flashing the Image

You can flash the image to either **e.MMC** or an **SD Card** using one of the following methods:

### 1. Using `uuu` Tool (Universal Update Utility)

TechNexion provides a guide to flash the image using the `uuu` tool:
🔗 [How to Flash with UUU](/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

:::tip  **Boot Mode**
Before flashing, ensure the board is set to **Serial Download Mode** in the boot configuration.
:::

---

### 2. Using `ums` Command in U-Boot (USB Mass Storage)

Alternatively, you can write the image directly to flash storage over **USB-OTG** using U-Boot’s `ums` command:
🔗 [Using UMS in U-Boot](/docs/embedded-software/linux/usage-guides/loading-software/using-u-boots-ums-command-to-write-flash-storage-over-usb-otg)

:::tip  **U-Boot Requirement**
The board must be booted with a version of U-Boot that supports the `ums` command. Typically, this is done from the existing e.MMC.
:::

* * *

## 🛠️ Build Yocto

TechNexion supports building a Yocto-based Linux image tailored for camera modules using the following kernel and branch.

### 📦 Supported Linux Kernel
| Linux Kernel Version | Yocto Branch                            |
|----------------------|------------------------------------------|
|6.6.52               | `tn-imx_6.6.52_2.2.0-stable`              |
---

### 📁 Source and Build Instructions

🔗 [Fetch Yocto Source](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/scarthgap_6.6.y-stable)<br/>
📖 [Build Yocto (Instructions for EDM-IMX95)](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/scarthgap_6.6.y-stable?tab=readme-ov-file#imx95-19x19-lpddr5-evk)

* * *

## 📸 Camera Testing Instructions

### Specify Camera DTBO in U-Boot

  1. Connect the **debug console cable** to carrier board.

  2. **Power on the board** and interrupt the boot process.
   Keep pressing `Enter` when the following message appears:
   `Hit any key to stop autoboot:`

  3. Specify the appropriate device tree for your camera using the `fdtfile` environment variable in U-Boot:

<Tabs groupId="csi-connector">
  <TabItem value="CIS1 " label="CIS1 connector">
  ```bash
  u-boot=> setenv fdtfile imx95-edm-evm-tevs.dtb
  ```
</TabItem>
  <TabItem value="CIS2" label="CIS2 connector">
  ```bash
  u-boot=> setenv fdtfile imx95-edm-evm-tevs-csi1.dtb
  ```
</TabItem>
</Tabs>

4. Continue boot process.
```bash
u-boot=> saveenv
u-boot=> boot
```
:::tip **About device tree Blob**
The provided DTBs include configuration for a 10.1-inch LVDS display panel.
This allows the system to output video, which is useful for verifying camera streaming on screen.
:::

---
### Setup the media controller before camera streaming

To verify that the camera has been properly connected and linked, use the `media-ctl` command:
```bash
$ media-ctl -p
```
This will display media controller information, such as:

```bash
    Media controller API version 6.6.52

    Media device information
    ------------------------
    driver          mxc-isi
    model           FSL Capture Media Device
    serial
    bus info        platform:4ad50000.isi
    hw revision     0x0
    driver version  6.6.52
    Device topology
    - entity 1: crossbar (13 pads, 9 links, 8 routes)
                type V4L2 subdev subtype Unknown flags 0
                device node name /dev/v4l-subdev0
            routes:
                    2/0 -> 5/0 [ACTIVE]
                    2/0 -> 6/0 [ACTIVE]
                    2/0 -> 7/0 [ACTIVE]
                    2/0 -> 8/0 [ACTIVE]
                    2/0 -> 9/0 [ACTIVE]
                    2/0 -> 10/0 [ACTIVE]
                    2/0 -> 11/0 [ACTIVE]
                    2/0 -> 12/0 [ACTIVE]
            pad0: Sink
            pad1: Sink
            pad2: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    <- "4ac10000.syscon:formatter@20":1 [ENABLED,IMMUTABLE]
            pad3: Sink
            pad4: Sink
            pad5: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.0":0 [ENABLED,IMMUTABLE]
            pad6: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.1":0 [ENABLED,IMMUTABLE]
            pad7: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.2":0 [ENABLED,IMMUTABLE]
            pad8: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.3":0 [ENABLED,IMMUTABLE]
            pad9: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.4":0 [ENABLED,IMMUTABLE]
            pad10: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.5":0 [ENABLED,IMMUTABLE]
            pad11: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.6":0 [ENABLED,IMMUTABLE]
            pad12: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:lim-range]
                    -> "mxc_isi.7":0 [ENABLED,IMMUTABLE]

    - entity 15: mxc_isi.0 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev1
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":5 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.0.capture":0 [ENABLED,IMMUTABLE]

    - entity 18: mxc_isi.0.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video0
            pad0: Sink
                    <- "mxc_isi.0":1 [ENABLED,IMMUTABLE]

    - entity 26: mxc_isi.1 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev2
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":6 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.1.capture":0 [ENABLED,IMMUTABLE]

    - entity 29: mxc_isi.1.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video1
            pad0: Sink
                    <- "mxc_isi.1":1 [ENABLED,IMMUTABLE]

    - entity 37: mxc_isi.2 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev3
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":7 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.2.capture":0 [ENABLED,IMMUTABLE]

    - entity 40: mxc_isi.2.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video2
            pad0: Sink
                    <- "mxc_isi.2":1 [ENABLED,IMMUTABLE]

    - entity 48: mxc_isi.3 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev4
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":8 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.3.capture":0 [ENABLED,IMMUTABLE]

    - entity 51: mxc_isi.3.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video3
            pad0: Sink
                    <- "mxc_isi.3":1 [ENABLED,IMMUTABLE]

    - entity 59: mxc_isi.4 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev5
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":9 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.4.capture":0 [ENABLED,IMMUTABLE]

    - entity 62: mxc_isi.4.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video4
            pad0: Sink
                    <- "mxc_isi.4":1 [ENABLED,IMMUTABLE]

    - entity 70: mxc_isi.5 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev6
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":10 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.5.capture":0 [ENABLED,IMMUTABLE]

    - entity 73: mxc_isi.5.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video5
            pad0: Sink
                    <- "mxc_isi.5":1 [ENABLED,IMMUTABLE]

    - entity 81: mxc_isi.6 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev7
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":11 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.6.capture":0 [ENABLED,IMMUTABLE]

    - entity 84: mxc_isi.6.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video6
            pad0: Sink
                    <- "mxc_isi.6":1 [ENABLED,IMMUTABLE]

    - entity 92: mxc_isi.7 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev8
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":12 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.7.capture":0 [ENABLED,IMMUTABLE]

    - entity 95: mxc_isi.7.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video7
            pad0: Sink
                    <- "mxc_isi.7":1 [ENABLED,IMMUTABLE]

    - entity 103: 4ac10000.syscon:formatter@20 (2 pads, 2 links, 1 route)
                  type V4L2 subdev subtype Unknown flags 0
                  device node name /dev/v4l-subdev9
            routes:
                    0/0 -> 1/0 [ACTIVE]
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:smpte170m xfer:709 ycbcr:601 quantization:lim-range]
                    <- "csidev-4ad30000.csi":1 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:smpte170m xfer:709 ycbcr:601 quantization:lim-range]
                    -> "crossbar":2 [ENABLED,IMMUTABLE]

    - entity 108: csidev-4ad30000.csi (2 pads, 2 links, 1 route)
                  type V4L2 subdev subtype Unknown flags 0
                  device node name /dev/v4l-subdev10
            routes:
                    0/0 -> 1/0 [ACTIVE]
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:smpte170m xfer:709 ycbcr:601 quantization:lim-range]
                    <- "tevs 1-0048":0 []
            pad1: Source
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:smpte170m xfer:709 ycbcr:601 quantization:lim-range]
                    -> "4ac10000.syscon:formatter@20":0 [ENABLED,IMMUTABLE]

    - entity 113: tevs 1-0048 (1 pad, 1 link, 0 routes)
                  type V4L2 subdev subtype Sensor flags 0
                  device node name /dev/v4l-subdev11
            pad0: Source
                    [stream:0 fmt:UYVY8_1X16/640x480@1/60 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/640x480
                     crop:(0,0)/640x480]
                    -> "csidev-4ad30000.csi":0 []
```


Showing the output as an image can make it easier to understand.
```shell
$ media-ctl --print-dot > graph.dot
$ dot -Tpng graph.dot > graph.png
# if you don't have tool, you need to install it as follows.
# sudo apt-get install -y graphviz.
```


![](//img/graph.png)

We can observe that the link between ‘**tevs 1-0048** ’ to ‘**csidev-4ad30000** ’ is a dotted line. It’s means not available.

Need to enable the **link** between TEVS and CSI device.
```bash
$ media-ctl -l "'tevs 1-0048':0 -> 'csidev-4ad30000.csi':0 [1]"
```


:::info For CSI 2 connector
When using **CSI2**, the TEVS camera is connected to **I2C bus 3**.
You need to adjust the command accordingly, as shown below:
```bash
media-ctl -l "'tevs 3-0048':0 -> 'csidev-4ad40000.csi':0 [1]"
```
:::

Sets the **resolution** and **format** of all media entities to match the camera's output. It means all components in the media pipeline have to align with the camera's output settings.

Use the `v4l2-ctl` command to view the camera's supported **formats**, **resolutions**, and **frame intervals**.
```bash
# formats
$ v4l2-ctl -d /dev/v4l-subdev11 --list-subdev-mbus-codes
  ioctl: VIDIOC_SUBDEV_ENUM_MBUS_CODE (pad=0)
          0x200f: MEDIA_BUS_FMT_UYVY8_1X16

# resolutions
$ v4l2-ctl -d /dev/v4l-subdev11 --list-subdev-framesize code=0x200f
  ioctl: VIDIOC_SUBDEV_ENUM_FRAME_SIZE (pad=0)
          Size Range: 640x480 - 640x480
          Size Range: 1280x720 - 1280x720
          Size Range: 1280x800 - 1280x800

# frame interval of resolution
$ v4l2-ctl -d /dev/v4l-subdev11 --list-subdev-frameintervals width=640,height=480,code=0x200f
  ioctl: VIDIOC_SUBDEV_ENUM_FRAME_INTERVAL (pad=0)
          Interval: 0.017s (60.000 fps)

$ v4l2-ctl -d /dev/v4l-subdev11 --list-subdev-frameintervals width=1280,height=720,code=0x200f
  ioctl: VIDIOC_SUBDEV_ENUM_FRAME_INTERVAL (pad=0)
          Interval: 0.017s (60.000 fps)

$ v4l2-ctl -d /dev/v4l-subdev11 --list-subdev-frameintervals width=1280,height=800,code=0x200f
  ioctl: VIDIOC_SUBDEV_ENUM_FRAME_INTERVAL (pad=0)
          Interval: 0.017s (60.000 fps)
```


Disable not used sources of crossbar, modify the route through the **“crossbar”** and only keep the channel of video0.
```bash
$ media-ctl -R "'crossbar' [2/0 -> 5/0 [1], 2/0 -> 6/0 [0], 2/0 -> 7/0 [0], 2/0 -> 8/0 [0], 2/0 -> 9/0 [0], 2/0 -> 10/0 [0], 2/0 -> 11/0 [0], 2/0 -> 12/0 [0]]"
```

:::info For CSI 2 connector
 The crossbar link to **pad 3** when using **CSI2**, so the command need to change as below :
```bash
$ media-ctl -R "'crossbar' [3/0 -> 5/0 [1], 3/0 -> 6/0 [0], 3/0 -> 7/0 [0], 3/0 -> 8/0 [0], 3/0 -> 9/0 [0], 3/0 -> 10/0 [0], 3/0 -> 11/0 [0], 3/0 -> 12/0 [0]]"
```
:::

Set resolution to 1280×720 for example.
```bash
$ media-ctl -V "'crossbar':2 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'mxc_isi.0':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'4ac10000.syscon:formatter@20':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'csidev-4ad30000.csi':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'tevs 1-0048':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
```

:::info For CSI 2 connector
```bash
$ media-ctl -V "'crossbar':3 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'mxc_isi.0':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'4ac10000.syscon:formatter@120':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'csidev-4ad40000.csi':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
$ media-ctl -V "'tevs 3-0048':0 [fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]"
```
:::
---

## 🎥 Start Camera Video Stream via GStreamer
### Launch GStreamer Pipeline
Specify the capture device you just get and start gstreamer to get video stream on screen.<br/>
Replace `<res_w>` and `<res_h>` with your selected resolution(1280x720) and screen dimensions:

```shell
$ gst-launch-1.0 v4l2src device=/dev/video0 ! \
  "video/x-raw, format=YUY2, width=<res_w>, height=<res_h>" ! \
  waylandsink sync=false
```
---
### Check Media Controller
We can get media controller as below :
```shell
    $ media-ctl -p
    Media controller API version 6.6.52

    Media device information
    ------------------------
    driver          mxc-isi
    model           FSL Capture Media Device
    serial
    bus info        platform:4ad50000.isi
    hw revision     0x0
    driver version  6.6.52

    Device topology
    - entity 1: crossbar (13 pads, 9 links, 8 routes)
                type V4L2 subdev subtype Unknown flags 0
                device node name /dev/v4l-subdev0
            routes:
                    2/0 -> 5/0 [ACTIVE]
                    2/0 -> 6/0 [INACTIVE]
                    2/0 -> 7/0 [INACTIVE]
                    2/0 -> 8/0 [INACTIVE]
                    2/0 -> 9/0 [INACTIVE]
                    2/0 -> 10/0 [INACTIVE]
                    2/0 -> 11/0 [INACTIVE]
                    2/0 -> 12/0 [INACTIVE]
            pad0: Sink
            pad1: Sink
            pad2: Sink
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]
                    <- "4ac10000.syscon:formatter@20":1 [ENABLED,IMMUTABLE]
            pad3: Sink
            pad4: Sink
            pad5: Source
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]
                    -> "mxc_isi.0":0 [ENABLED,IMMUTABLE]
            pad6: Source
                    -> "mxc_isi.1":0 [ENABLED,IMMUTABLE]
            pad7: Source
                    -> "mxc_isi.2":0 [ENABLED,IMMUTABLE]
            pad8: Source
                    -> "mxc_isi.3":0 [ENABLED,IMMUTABLE]
            pad9: Source
                    -> "mxc_isi.4":0 [ENABLED,IMMUTABLE]
            pad10: Source
                    -> "mxc_isi.5":0 [ENABLED,IMMUTABLE]
            pad11: Source
                    -> "mxc_isi.6":0 [ENABLED,IMMUTABLE]
            pad12: Source
                    -> "mxc_isi.7":0 [ENABLED,IMMUTABLE]

    - entity 15: mxc_isi.0 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev1
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601
                     compose.bounds:(0,0)/1280x720
                     compose:(0,0)/1280x720]
                    <- "crossbar":5 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1280x720 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1280x720
                     crop:(0,0)/1280x720]
                    -> "mxc_isi.0.capture":0 [ENABLED,IMMUTABLE]

    - entity 18: mxc_isi.0.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video0
            pad0: Sink
                    <- "mxc_isi.0":1 [ENABLED,IMMUTABLE]

    - entity 26: mxc_isi.1 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev2
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":6 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.1.capture":0 [ENABLED,IMMUTABLE]

    - entity 29: mxc_isi.1.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video1
            pad0: Sink
                    <- "mxc_isi.1":1 [ENABLED,IMMUTABLE]

    - entity 37: mxc_isi.2 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev3
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":7 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.2.capture":0 [ENABLED,IMMUTABLE]

    - entity 40: mxc_isi.2.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video2
            pad0: Sink
                    <- "mxc_isi.2":1 [ENABLED,IMMUTABLE]

    - entity 48: mxc_isi.3 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev4
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":8 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.3.capture":0 [ENABLED,IMMUTABLE]

    - entity 51: mxc_isi.3.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video3
            pad0: Sink
                    <- "mxc_isi.3":1 [ENABLED,IMMUTABLE]

    - entity 59: mxc_isi.4 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev5
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":9 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.4.capture":0 [ENABLED,IMMUTABLE]

    - entity 62: mxc_isi.4.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video4
            pad0: Sink
                    <- "mxc_isi.4":1 [ENABLED,IMMUTABLE]

    - entity 70: mxc_isi.5 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev6
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":10 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.5.capture":0 [ENABLED,IMMUTABLE]

    - entity 73: mxc_isi.5.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video5
            pad0: Sink
                    <- "mxc_isi.5":1 [ENABLED,IMMUTABLE]

    - entity 81: mxc_isi.6 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev7
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":11 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.6.capture":0 [ENABLED,IMMUTABLE]

    - entity 84: mxc_isi.6.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video6
            pad0: Sink
                    <- "mxc_isi.6":1 [ENABLED,IMMUTABLE]

    - entity 92: mxc_isi.7 (2 pads, 2 links, 0 routes)
                 type V4L2 subdev subtype Unknown flags 0
                 device node name /dev/v4l-subdev8
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     compose.bounds:(0,0)/1920x1080
                     compose:(0,0)/1920x1080]
                    <- "crossbar":12 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:YUV8_1X24/1920x1080 field:none colorspace:jpeg xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1920x1080
                     crop:(0,0)/1920x1080]
                    -> "mxc_isi.7.capture":0 [ENABLED,IMMUTABLE]

    - entity 95: mxc_isi.7.capture (1 pad, 1 link)
                 type Node subtype V4L flags 0
                 device node name /dev/video7
            pad0: Sink
                    <- "mxc_isi.7":1 [ENABLED,IMMUTABLE]

    - entity 103: 4ac10000.syscon:formatter@20 (2 pads, 2 links, 1 route)
                  type V4L2 subdev subtype Unknown flags 0
                  device node name /dev/v4l-subdev9
            routes:
                    0/0 -> 1/0 [ACTIVE]
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]
                    <- "csidev-4ad30000.csi":1 [ENABLED,IMMUTABLE]
            pad1: Source
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]
                    -> "crossbar":2 [ENABLED,IMMUTABLE]

    - entity 108: csidev-4ad30000.csi (2 pads, 2 links, 1 route)
                  type V4L2 subdev subtype Unknown flags 0
                  device node name /dev/v4l-subdev10
            routes:
                    0/0 -> 1/0 [ACTIVE]
            pad0: Sink
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]
                    <- "tevs 1-0048":0 [ENABLED]
            pad1: Source
                    [stream:0 fmt:UYVY8_1X16/1280x720 field:none colorspace:srgb xfer:srgb ycbcr:601]
                    -> "4ac10000.syscon:formatter@20":0 [ENABLED,IMMUTABLE]

    - entity 113: tevs 1-0048 (1 pad, 1 link, 0 routes)
                  type V4L2 subdev subtype Sensor flags 0
                  device node name /dev/v4l-subdev11
            pad0: Source
                    [stream:0 fmt:UYVY8_1X16/1280x720@1/60 field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range
                     crop.bounds:(0,0)/1280x720
                     crop:(0,0)/1280x720]
                    -> "csidev-4ad30000.csi":0 [ENABLED]
```
---

## 🚨 Troubleshooting

#### Check DTB Overlay in U-Boot
Make sure the correct Device Tree Blob (DTB) is specified in U-Boot.
<Tabs groupId="u-boot-dtbo">
  <TabItem value="csi1" label="CSI1 connector">
  ```shell
    u-boot=> printenv fdtfile
    u-boot=> fdtfile=imx95-edm-evm-tevs.dtb
  ```
</TabItem>
  <TabItem value="csi2" label="CSI2 connector">
  ```shell
    u-boot=> printenv fdtfile
    u-boot=> fdtfile=imx95-edm-evm-tevs-csi1.dtb
  ```
</TabItem>
</Tabs>

#### Verify Camera Initialization
Use `dmesg` to check if the TEVS camera module was initialized correctly.
```shell
  $ dmesg -t | grep tevs
    platform 4ad30000.csi: Fixed dependency cycle(s) with /soc/bus@44000000/i2c@44350000/tevs@48
    tevs 1-0048: tevs_probe() device node: tevs@48
    tevs 1-0048: Version:********
    tevs 1-0048: Product:TEVS-AR0144, HeaderVer:3, MIPI_Rate:800
    tevs 1-0048: probe success
```

#### Confirm Board and Camera DTB
Check if the correct DTB is loaded with a matching camera model.
```shell
  $ dmesg -t | grep -i model
    Machine model: TechNexion EDM-IMX95 and EVM baseboard with TechNexion TEVS camera
    mali 4d900000.gpu: Using configured power model mali-tvax-power-model, and fallback mali-simple-power-model
```
#### Check Available Video Devices
Verify that the video devices are available.
```shell
  $ v4l2-ctl --list-device
```
Example output:
```shell
mxc-isi-cap (platform:4ad50000.isi):
    /dev/video0
    /dev/video1
    /dev/video2
    /dev/video3
    /dev/video4
    /dev/video5
    /dev/video6
    /dev/video7
    /dev/media0

mxc-jpeg codec (platform:4c500000.jpegdec):
    /dev/video10

mxc-jpeg codec (platform:4c550000.jpegenc):
    /dev/video11

wave6-dec (platform:wave6-dec):
    /dev/video8

wave6-enc (platform:wave6-enc):
    /dev/video9
```
Typically, `/dev/video0` is your primary camera capture device when using CSI1.

