---
title: "NXP 8MPLUSLPD4-EVK board TEVS Camera Usage Guide"
description: "NXP 8MPLUSLPD4-EVK board TEVS Camera Usage Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

## 🚀 Introduction

This article guides you how to get started using TechNexion camera modules on **NXP 8MPLUSLPD4-EVK** board.

---
## 📸 Supported Camera Modules

|  **Camera Series**  |   **Products**    |
|:---:|-------|
|TEVS| <ProductList series="TEVS"/>|


:::info
**TEVI-OV5640 and TEVI-AR Series Cameras are deprecated starting from Yocto 4.2**<br/>
Support for TEVI-OV5640 and TEVI-AR Series Cameras is available only up to **Yocto 4.0**.
If you need to use these camera modules, please refer to the 🔗 [**Yocto 4.0 (Kirkstone) 2024Q1 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp40-2024q1.md).
 :::

* * *
## 🔧 Hardware Setup Instructions

### Connect the Adapter Board and Camera Cable

To set up TechNexion camera, connect the adapter board and camera cable to the **MIPI CSI1** port on the EVK board.

<img src="/img/tevs-ar0144-nxp-evk-minisas-connected.jpg" width="480" height="360" />

:::tip **Why Use Only MIPI CSI1?**
**MIPI CSI2 is disabled by default.**<br/>
CSI1 and CSI2 **share the same reset pin(CSI_RST)**, so the EVK board **can't run both cameras at the same time**.
:::
:::tip Recommended
**MIPI CSI2** uses the **ISI1 video channel**, which only supports up to **2K resolution**.<br/>
Want to use MIPI CSI2? You’ll need to **manually update the device tree**.
:::

:::info Better Option for Production Use
If you're moving to mass production, we suggest switching to the 🔗 [WB-EDM-G-IMX8M-PLUS](https://www.technexion.com/products/system-on-modules/evk/wb-edm-g-imx8m-plus/) platform.<br/>
It’s more compact and better suited for custom applications.
:::
* * *

## 💻 Prepare Yocto demo image for testing TechNexion camera

To test TechNexion TEVS Series cameras, you need a demo image that includes the required Device Tree Blobs (DTBs) and camera drivers.

### 🔽 Download and Select the Correct Image for Your EVK

There are two kinds of DRAM types for i.mx8mp evk boards: it's equipped with DDR4 or LPDDR4.<br/>
Prebuilt demo images can be available for download via TechNexion's server.

#### **Download Link**:
🔗 [Supported Release List (Instructions for IMX8MP-DDR4)](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-DDR4/archived/)<br/>
🔗 [Supported Release List (Instructions for IMX8MP-LPDDR4)](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-LPDDR4/archived/)

---

### 💾 Flashing the Image
You can flash the image to an SD Card using the following methods:

:::tip  **Boot Mode**
Make sure your EVK board is set to **boot from SD card**.<br/>
Refer to the official NXP guide 🔗 [Getting Started with the i.MX 8M Plus EVK](https://www.nxp.com/document/guide/getting-started-with-the-i-mx-8m-plus-evk:GS-iMX-8M-Plus-EVK)

:::
### 1. For Windows Users

Use **balenaEtcher** to write the image to your SD card:

🔗 [<img src="/img/balenaetcher-vector-logo.png" width="250" height="50" />](https://www.balena.io/etcher/)

### 2. For Linux Users

Use `bmap-tools` for a fast and reliable flashing process to the SD card:

```bash
sudo apt-get update
sudo apt-get install bmap-tools
```

* * *

## 🛠️ Build Yocto

TechNexion supports building a Yocto-based Linux image tailored for camera modules using the following kernel and branch.

If you are using prebuilt demo image, you can skip this section. Please refer to [**📸 Camera Testing Instructions**](#-camera-testing-instructions) section for camera testing steps.

### 📦 Supported Linux Kernel

| Linux Kernel Version | Yocto Branch                            |
|----------------------|------------------------------------------|
| 6.6.52               | `tn-imx_6.6.52_2.2.0-stable`              |

---

### 📁 Source and Build Instructions

🔗 [Fetch Yocto source](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/scarthgap_6.6.y-stable)<br/>
📖 [Build Yocto (Instructions for NXP i.MX 8M Plus EVK)](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/scarthgap_6.6.y-stable?tab=readme-ov-file#for-nxp-evk-with-technexion-tevs-and-vls-camera-support)

---

### 🐧 Develop Linux kernel

Fetch and compile Linux kernel.

:::info Build Enviroment
- Host OS: **Ubuntu 18.04/20.04**
- Toolchain: `gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu`
:::

#### Clone the Kernel Source

```shell
# For Kernel 6.6.52
$ git clone https://github.com/TechNexion/linux-tn-imx.git -b tn-imx_6.6.52_2.2.0-stable
```

#### Set cross-compile environment & the kernel config:
```shell
$ export PATH=$PATH:/opt/gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu/bin
$ export ARCH=arm64
$ export CROSS_COMPILE=/opt/gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-
$ cd linux-tn-imx
~/linux-tn-imx$ make imx_v8_defconfig
```
#### Compile the kernel & module driver:
```shell
# compile kernel
~/linux-tn-imx$ make
# create kernel module folder
~/linux-tn-imx$ mkdir -p ./modules
# compile module driver
~/linux-tn-imx$ rm -rf ./modules/*
~/linux-tn-imx$ make INSTALL_MOD_PATH=./modules modules_install
```
#### Change kernel Image, DTB & modules:
```shell
~/linux-tn-imx/$ cp arch/arm64/boot/Image  <mount_folder>/boot/Image
~/linux-tn-imx/$ cp arch/arm64/boot/dts/freescale/imx8mp-evk-tevs.dtb  <mount_folder>/boot/
~/linux-tn-imx/$ sudo cp -r ./modules/lib/modules/*  <mount_folder>/root/lib/modules/
```
---

## 📸 Camera Testing Instructions

### Specify Camera DTBO in U-Boot
####  Manual DTBO Setup
1. Connect the **debug console cable** to the baseboard (via UART/USB).

2. **Power on the board** and interrupt the boot process.
   Keep pressing `Enter` when the following message appears:
   `Hit any key to stop autoboot:`

3. Specify the appropriate device tree for your camera using the `fdtfile` environment variable in U-Boot:

```shell
u-boot=> setenv fdtfile imx8mp-evk-tevs.dtb
```

4. Save and continue the boot process:

```shell
u-boot=> saveenv
u-boot=> boot
```
---

## 🎥 Start Camera Video Stream via GStreamer
### Check Camera Availability

Use the v4l2-ctl tool to list connected video devices.
```bash
$ v4l2-ctl --list-device
```
Example output:
```shell
mxc-isi-cap (platform:32e00000.isi:cap_devic):

/dev/video{X}
```

The following is example.

 <img src="/img/mxc-isi-cap-snapshot-tevs-nxp-imx8mp-evk.png" width="480" height="320" />

---

### Launch GStreamer Pipeline
Replace `{X}`, `<res_w>` and `<res_h>` with your capture device number and desired resolution:
```shell

$ gst-launch-1.0 v4l2src device=/dev/video{X} ! \
  video/x-raw,width=<x>,height=<y> ! waylandsink
```
* * *

## 🚨 Troubleshooting

Boot up **NXP 8MPLUSLPD4-EVK** and check initialization of **TEVS driver**.
If it shows below messages, the driver is initialized correcly.
```shell
$ dmesg -t | grep tevs

  tevs 1-0048: tevs_probe() device node: tevs@48
  tevs 1-0048: Version:********
  tevs 1-0048: Product:TEVS-AR0234, HeaderVer:3, MIPI_Rate:800
  tevs 1-0048: probe success
```

Ensure camera device tree blob(DTB) is specified correctly.
```shell
$ dmesg -t | grep -i model
  Machine model: NXP i.MX8MPlus EVK board with TechNexion TEVS camera
```