---
title: NEOUSYS NRU-161V-AWP
description: TechNexion Cameras for NEOUSYS NRU-161V-AWP with Jetpack 6.0
---
import ProductList from '@site/src/components/ProductList.jsx';

## Instruction

TechNexion Embedded Vision Solutions provide embedded system developers access to high-performance, industrial-grade camera solutions to accelerate their time to market for embedded vision projects.

## Supported Camera Modules

| **Camera Series** | **Products** |
| :---: | :---: |
| VLS-GM2 | <ProductList series="VLS_GM2"/> |

[More Camera Products Details...](https://www.technexion.com/products/)

---

## Supported NVIDIA Jetson Partner Platforms

  * [NEOUSYS NRU-161V-AWP](https://www.neousys-tech.com/en/product/product-lines/rugged-computer/nru-160-awp)

---

## Install TN Camera on NVIDIA Jetson Partner Platforms

#### Connect to NVIDIA Jetson Partner Platforms

**COAX cable for VLS-GM2 Cameras**

Connect **VLS-GM2 cameras** to **NRU-161V-AWP** directly.

<img
  src='/img/gmsl_neousys_camera.png'
  width="640"
/>
<br />

<img
  src='/img/gmsl_neousys_box.png'
  width="640"
/>

Follow the [video](https://www.youtube.com/watch?v=s3PqXQGCoQc) tutorial below to learn how to connect **VLS-GM2 cameras** to **NRU-161V-AWP**.

<iframe width="640" height="480" src="https://www.youtube.com/embed/s3PqXQGCoQc" title="Partner Showcase : Neousys NRU-161V-AWP NVIDIA Jetson Orin NX System with TechNexion GMSL2 Cameras" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

#### Using TechNexion Pre-built modules

:::tip[Preparation]
We recommend following the user manual from platform vendors to setup system.
After that, you can follow the below method to install TechNexion Cameras Driver.
:::

1. Download pre-built modules.

    ```shell
    $ wget https://download.technexion.com/demo_software/EVK/NVIDIA/NRU-161V-AWP/pre-built-modules/latest/JP60/tn_camera_modules_jp60.tar.gz
    ```

2. Uncompress the modules.

    ```shell
    $ tar -xf tn_camera_modules_jp60.tar.gz
    ```

3. Run installation script.

    ```shell
    $ cd tn_camera_modules_<built_time>/
    $ ./tn_install.sh
    ```

4. After you agree to continue the installation, the module will be installed to the system.

    ```shell
    $ ./tn_install.sh
    ****** TechNexion Camera Driver Installation ******
    This installation is easy to install TechnNexion Camera Drivers for Nvidia
    Jetson Orin NX Platform. Before start to install camera driver,
    You should BACKUP your image to avoid any file you lost while installing process.
    Do you want to continue?[Y/n]y
    Continuing with the installation...
    Install TN-CAM modules: max9296a.ko
    Install TN-CAM modules: max96717.ko
    Install TN-CAM modules: tevs.ko
    Install TN-CAM DTBO file: vls-gm2
    Installed TN-CAM DTB file Done.
    Install TN-CAM DTBO file: vls-gm2-fsync
    Installed TN-CAM DTB file Done.
    Installing configuration: vls-gm2
    Add TN-CAM Configuration for modules: vls-gm2
    Installing configuration: vls-gm2-fsync
    Add TN-CAM Configuration for modules: vls-gm2-fsync
    Set Default Configuration for modules: vls-gm2
    Install TN-CAM service...
    Launch TN-CAM Service...
    Finish Camera Driver Installation. Return Code:[0]
    You should Reboot Device to enable vls-gm2-fsync Cameras.
    Do you want to reboot now?[Y/n]y
    Rebooting....
    ```

    :::note
    You should reboot the device after installation.
    :::

---

## Bring up Camera

#### TechNexion VizionViewer

[VizionViewer&trade;](/docs/vision-software/vizionviewer/vizionviewer-installation#linux-arm64)

#### Gstreamer

If you succeed in initialing the camera, you can follow the steps to open the camera.

1. Check the supported resolutions:

    ```shell
    $ gst-device-monitor-1.0 Video/Source
    Probing devices...

    Device found:

            name  : vi-output, tevs 16-0038
            class : Video/Source
            caps  : video/x-raw, format=UYVY, width=640, height=480, framerate=60/1
                    video/x-raw, format=UYVY, width=1280, height=720, framerate=60/1
                    video/x-raw, format=UYVY, width=1280, height=800, framerate=60/1
                    video/x-raw, format=NV16, width=640, height=480, framerate=60/1
                    video/x-raw, format=NV16, width=1280, height=720, framerate=60/1
                    video/x-raw, format=NV16, width=1280, height=800, framerate=60/1
                    video/x-raw, format=UYVY, width=640, height=480, framerate=60/1
                    video/x-raw, format=UYVY, width=1280, height=720, framerate=60/1
                    video/x-raw, format=UYVY, width=1280, height=800, framerate=60/1
            properties:
                    object.path = v4l2:/dev/video0
                    device.api = v4l2
                    media.class = Video/Source
                    api.v4l2.path = /dev/video0
                    api.v4l2.cap.driver = tegra-video
                    api.v4l2.cap.card = "vi-output\,\ tevs\ 16-0038"
                    api.v4l2.cap.bus_info = platform:tegra-capture-vi:0
                    api.v4l2.cap.version = 5.15.136
                    api.v4l2.cap.capabilities = 84200001
                    api.v4l2.cap.device-caps = 04200001
                    device.id = 34
                    node.name = v4l2_input.platform-tegra-capture-vi
                    node.description = "vi-output\,\ tevs\ 16-0038"
                    factory.name = api.v4l2.source
                    node.pause-on-idle = false
                    factory.id = 10
                    client.id = 32
                    clock.quantum-limit = 8192
                    media.role = Camera
                    node.driver = true
                    object.id = 35
                    object.serial = 35
            gst-launch-1.0 pipewiresrc path=35 ! ...
    ```

2. Bring up the camera (/dev/video0) with 1280x720 by Gstreamer pipeline:

    ```shell
    $ DISPLAY=:0 gst-launch-1.0 nvv4l2camerasrc device=/dev/video0 ! \
    "video/x-raw(memory:NVMM), format=UYVY, width=1280, height=720" ! \
    nvvidconv ! xvimagesink sync=false
    ```

    :::tip
    If you don't have the elemnt "nvv4l2camerasrc", you can follow Troubleshooting 3.
    :::

---

## Troubleshooting

1. **Cannot find cameras**

    If you cannot bring up the cameras, you can check if the video device does exist.

    ```shell
    $ ls /dev/video*  # List all video devices
    /dev/video0  /dev/video1
    ```

    If you cannot see the devices, you should check if the drivers have been probed.

2. **Occur Error: Could not get EGL display connection**

    If you occurred the errors nvbufsurftransform: Could not get EGL display connection by rununing the Gstreamer command, you can modify the parameter `DISPLAY` by the command:

    ```shell
    # Check and Set environment parameter for 'DISPLAY'
    $ export DISPLAY=$(w| tr -s ' '| cut -d ' ' -f 3 | grep :)

    # Run Gstreamer pipeline
    $ gst-launch-1.0 v4l2src device=/dev/video0 ! \
    "video/x-raw, format=UYVY, width=1280, height=720" ! xvimagesink sync=false
    ```

3. **CPU overload when camera streaming**

    If you open the camera stream and find that the CPU load is too large, it may be because Gstreamer does not use the GPU. You have to install some NVIDIA plug-in tools on Gstreamer.

    ```shell
    $ sudo apt update && apt depends nvidia-jetpack | awk '{print $2}' | uniq | xargs -I {} bash -c "sudo apt -o Dpkg::Options::="--force-confold" -y install {} ; sudo apt clean"
    ```
