---
title: "EDM-IMX95 VLS3 Camera Usage Guide"
description: "EDM-IMX95 VLS3 Camera Usage Guide"
---
import ProductList from '@site/src/components/ProductList.jsx';

## 🚀 Introduction

This article guides you how to set up **Media Controller Framework** for using **VLS cameras** on **EDM-IMX95 EVM** board.

For detailed camera setup instructions, please refer to the 🔗 [**EDM-IMX95 TEVS Camera Usage Guide**](/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-5.0-scarthgap-release/edm-imx95-tevs-camera-usage-guide.md). If anything in this article is unclear, that guide covers most of the configuration in depth.

:::info e.g. IMX95-EVM Baseboard
This article uses the **IMX95-EVM** baseboard as an example,
based on the 🔗 [**Yocto 5.0 (Scarthgap) 2025Q2 Release**](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp50-2025q2.md).
The corresponding Linux kernel version is **6.6.52_2.2.0**.
 :::
---
## 📸 Supported Camera Modules

|  **Camera Series**  |   **Products**    |
:---:|-------|
VLS3| <ProductList series="VLS3"/>|

* * *
## 🔧 Hardware Setup Instructions

### 1. Prepare Accessories and Hardware Setup

The TechNexion **IMX95-EVM kit** supports **FPD-Link III SerDes**.<br/>
Each **CSI interface** can connect up to **4 camera channels**, allowing a total of **8 cameras** to stream simultaneously.

:::warning **CSI2 switch**
 If you're using the **CSI2 connector**, make sure to set **SW2** to "**ON**" to enable it.
:::

#### Required Accessory: VizionLink-COAX1 Cable
Supports cable lengths up to **15 meters** for stable camera streaming.
 <img src="/img/vizionlink-coax-1m-cable.png" width="360" height="360" />

* * *

## 💻 Prepare Yocto demo image for testing VLS cameras

Prepare demo image contains the required device tree blobs and camera drivers to probe FPD-Link III SerDes.

### 🔽 Downloading the Demo Image
Prebuilt demo images are available for download on TechNexion's official server.

#### **Download Link**:
🔗 [Supported Release List](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp50-2025q2.md)

#### Included DTB Files

The demo image supports 8-camera configurations with different LVDS panel sizes:

- For 10.1" LVDS panel:

  `imx95-edm-evm-fusion-lvds-vl10112880.dtb`
- For 15.6" LVDS panel:

  `imx95-edm-evm-fusion-lvds-vl156192108.dtb`

:::note
The mainline driver **cannot automatically detect or ignore unused channels**.
The DTB must describe exactly which ports are connected.
You can edit the DTB via the source code here:
[🔗 **GitHub – TechNexion DTB Source**](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.6.52_2.2.0-next/arch/arm64/boot/dts/freescale)
:::

* * *

## 📸 VLS Camera Testing Instructions

### Specify Camera DTB in U-Boot

1. Connect the **debug console cable** to carrier board.

2. **Power on the board** and interrupt the boot process.
   Keep pressing `Enter` when the following message appears:
   `Hit any key to stop autoboot:`

3. Specify the appropriate device tree for your camera using the `fdtfile` environment variable in U-Boot:
```shell
# with 10.1 inch LVDS panel
    u-boot=> setenv fdtfile imx95-edm-evm-fusion-lvds-vl10112880.dtb
# with 15.6 inch LVDS panel
    u-boot=> setenv fdtfile imx95-edm-evm-fusion-lvds-vl156192108.dtb
```

4. Save the settings and boot the system.
```shell
u-boot=> saveenv
u-boot=> boot
```

### Setup the media controller before camera streaming

To verify that the camera has been properly connected and linked, use the `media-ctl` command:
```bash
$ media-ctl -p
```
Get the media controller information and transfer to image, here is 8 VLS3 cameras with FPD-Link III SerDes :

![](//img/8x-vls3-2x-csi.png)

Modify the route through the **“crossbar”**  and enable all the channel :
```shell

$ media-ctl -R "'crossbar' [2/0 -> 5/0 [1], 2/1 -> 6/0 [1], 2/2 -> 7/0 [1], 2/3 -> 8/0 [1], 3/4 -> 9/0 [1], 3/5 -> 10/0 [1], 3/6 -> 11/0 [1], 3/7 -> 12/0 [1]]"
```

Set the route
```shell

$ media-ctl -R "'crossbar' [2/0 -> 5/0 [1], 2/1 -> 6/0 [1], 2/2 -> 7/0 [1], 2/3 -> 8/0 [1], 3/4 -> 9/0 [1], 3/5 -> 10/0 [1], 3/6 -> 11/0 [1], 3/7 -> 12/0 [1]]"
$ media-ctl -R '"ds90ub960 1-0030" [0/0 -> 4/0 [1], 1/0 -> 4/1 [1], 2/0 -> 4/2 [1], 3/0 -> 4/3 [1]]'
$ media-ctl -R '"ds90ub960 2-0030" [0/0 -> 4/4 [1], 1/0 -> 4/5 [1], 2/0 -> 4/6 [1], 3/0 -> 4/7 [1]]'
$ media-ctl -R '"csidev-4ad30000.csi" [0/0 -> 1/0 [1], 0/1 -> 1/1 [1], 0/2 -> 1/$
$ media-ctl -R '"csidev-4ad40000.csi" [0/4 -> 1/4 [1], 0/5 -> 1/5 [1], 0/6 -> 1/6 [1], 0/7 -> 1/7 [1]]'
$ media-ctl -R '"4ac10000.syscon:formatter@20" [0/0 -> 1/0 [1], 0/1 -> 1/1 [1], 0/2 -> 1/2 [1], 0/3 -> 1/3 [1]]'
$ media-ctl -R '"4ac10000.syscon:formatter@120" [0/4 -> 1/4 [1], 0/5 -> 1/5 [1], 0/6 -> 1/6 [1], 0/7 -> 1/7 [1]]'
```

Set the link
```shell
$ media-ctl -l "'ds90ub960 1-0030':4 -> 'csidev-4ad30000.csi':0 [1]"
$ media-ctl -l "'ds90ub960 2-0030':4 -> 'csidev-4ad40000.csi':0 [1]"
```

Here is a script for setting resolution.
```shell
    #!/bin/bash

    # Define the format and resolution as a variable
    MEDIA_FMT="UYVY8_1X16/1280x720"

    # Function to run media-ctl commands with error handling
    run_media_ctl() {
        local cmd="$1"
        echo "Running: $cmd"
        if ! $cmd; then
            echo "Error: Command failed: $cmd"
        fi
    }

    # Configure crossbar devices
    echo "Configuring crossbar devices..."
    for i in {0..3}; do
        run_media_ctl "media-ctl -V 'crossbar':2/$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done
    for i in {4..7}; do
        run_media_ctl "media-ctl -V 'crossbar':3/$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done

    # Configure mxc_isi devices (endpoint 0)
    echo "Configuring mxc_isi devices (endpoint 0)..."
    for dev in {0..7}; do
        run_media_ctl "media-ctl -V 'mxc_isi.$dev':0 [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done

    # Configure formatter@20 endpoints
    echo "Configuring formatter@20 endpoints..."
    for i in {0..3}; do
        run_media_ctl "media-ctl -V '4ac10000.syscon:formatter@20':0/$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done
    # Configure formatter@120 endpoints
    echo "Configuring formatter@120 endpoints..."
    for i in {4..7}; do
        run_media_ctl "media-ctl -V '4ac10000.syscon:formatter@120':0/$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done

    # Configure csidev-4ad30000.csi endpoints 0-3
    echo "Configuring csidev-4ad30000.csi endpoints 0-3..."
    for i in {0..3}; do
        run_media_ctl "media-ctl -V 'csidev-4ad30000.csi':0/$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done
    # Configure csidev-4ad40000.csi endpoints 4-7
    echo "Configuring csidev-4ad40000.csi endpoints 4-7..."
    for i in {4..7}; do
        run_media_ctl "media-ctl -V 'csidev-4ad40000.csi':0/$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done

    # Configure ds90ub960 buses (1 and 2) with endpoints 0-3
    echo "Configuring ds90ub960 buses..."
    for bus in 1 2; do
        for i in {0..3}; do
            run_media_ctl "media-ctl -V 'ds90ub960 $bus-0030':$i [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
        done
    done

    # Configure ds90ub953 buses (1 and 2) with addresses 44-47 (endpoint 0)
    echo "Configuring ds90ub953 buses..."
    for bus in 1 2; do
        for addr in 44 45 46 47; do
            run_media_ctl "media-ctl -V 'ds90ub953 $bus-00$addr':0 [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
        done
    done

    # Configure tevs buses (8-15) with endpoint 0
    echo "Configuring tevs buses..."
    for bus in {8..15}; do
        run_media_ctl "media-ctl -V 'tevs $bus-0048':0 [fmt:$MEDIA_FMT field:none colorspace:srgb xfer:srgb ycbcr:601 quantization:full-range]"
    done
```
---
## 🎥 Start Camera Video Stream via GStreamer
### Launch GStreamer Pipeline
Specify the capture device you just get and start gstreamer to get video stream on screen.<br/>
```shell

    $ gst-launch-1.0 v4l2src device=/dev/videoX ! "video/x-raw, format=YUY2, width=1280, height=720" ! waylandsink sync=false

```

