---
title: Understanding Frame Rate and Exposure Impact on VLS-GM2 Cameras
description: Understanding Frame Rate and Exposure Impact on VLS-GM2 Cameras
---

## Overview ##

When using TechNexion VLS-GM2 cameras, special care must be taken when adjusting exposure time, as it directly affects frame rate and consequently the vertical/horizontal blanking.

In a typical VLS-GM2 camera system, the automatic exposure mode (AE) typically adjusts exposure time as ambient lighting changes, indirectly controlling the frame rate. The camera will automatically extend the exposure time in dark environment, resulting in an increase in the effective data time ratio. In order to maintain the sensor clock configuration, the horizontal blanking will be automatically extended. Or when the user manually reduces the frame rate, in order to maintain the sensor clock configuration, the camera will automatically adjust the vertical blanking time to extend the frame period.

Excessively long blanking may exceed the timing limits supported by GMSL2 transmission and the deserializer’s internal buffer handling. While GMSL2 continues to transmit data, the downstream MIPI CSI-2 output may suffer decoding errors, causing the image to freeze, distort, or disappear.

## Typical Failure Scenario ##

- Black screen or flickering on the MIPI display
- Frame drops or corrupted images
- CSI errors like frame mismatch, CRC errors, or timeout
- GMSL2 status shows LOCK=1, but no valid image is displayed

## Safe AE Range Reference Table ##

To avoid blanking overflow issues, we recommend the following AE range under common configurations:

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0144** | 1280 x  800 | **UYVY** | 16666 | 33333 |
|  | 1280 x 720 |  | 16666 | 33333 |
|  | 640 x 480 |  | 16666 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0145** | 1280 x 800 | **UYVY** | 8695 | 33333 |
|  | 1280 x 720 |  | 8695 | 33333 |
|  | 640 x 480 |  | 8695 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0234** | 1920 x 1200 | **UYVY** | 8333 | 33333 |
|  | 1920 x 1080 |  | 8333 | 33333 |
|  | 1280 x 720 |  | 8333 | 33333 |
|  | 640 x 480 |  | 8333 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0521** | 2592 x 1944 | **UYVY** | 8333 | 66666 |
|  | 2560 x 1440 |  | 8333 | 66666 |
|  | 1920 x 1080 |  | 8333 | 33333 |
|  | 1280 x 960 |  | 8333 | 33333 |
|  | 1280 x 720 |  | 8333 | 33333 |
|  | 640 x 480 |  | 8333 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0522** | 2592 x 1944 | **UYVY** | 8333 | 66666 |
|  | 2560 x 1440 |  | 8333 | 66666 |
|  | 1920 x 1080 |  | 8333 | 33333 |
|  | 1280 x 960 |  | 8333 | 33333 |
|  | 1280 x 720 |  | 8333 | 33333 |
|  | 640 x 480 |  | 8333 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0821** | 3840 x 2160 | **UYVY** | 16666 | 66666 |
|  | 2560 x 1440 |  | 16666 | 66666 |
|  | 1920 x 1080 |  | 16666 | 33333 |
|  | 1280 x 720 |  | 16666 | 33333 |
|  | 640 x 480 |  | 16666 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR0822** | 3840 x 2160 | **UYVY** | 16666 | 66666 |
|  | 2560 x 1440 |  | 16666 | 66666 |
|  | 1920 x 1080 |  | 16666 | 33333 |
|  | 1280 x 720 |  | 16666 | 33333 |
|  | 640 x 480 |  | 16666 | 33333 |

| Sensor | Resolution | Format | AE min | AE max |
| :---: | :---: | :---: | :---: | :---: |
| **AR1335** | 4208 x 3120 | **UYVY** | 8333 | 66666 |
|  | 3840 x 2160 |  | 8333 | 66666 |
|  | 2560 x 1440 |  | 8333 | 66666 |
|  | 1920 x 1080 |  | 8333 | 33333 |
|  | 1280 x 720 |  | 8333 | 33333 |
|  | 640 x 480 |  | 8333 | 33333 |

:::note
Values are approximate and based on internal validation using typical GMSL2 deserializers. Buffer limitations and internal timing tolerance may vary slightly depending on the actual hardware.
:::

## Conclusion ##

We strongly recommend following our verified exposure and blanking limits for reliable video streaming. If you have further questions or are working with special timing configurations, please contact our support team for assistance.

