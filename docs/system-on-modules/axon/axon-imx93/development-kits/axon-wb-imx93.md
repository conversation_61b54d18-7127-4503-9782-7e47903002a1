---
sidebar_position: 1
---

# AXON-WB-IMX93

The AXON-WB-IMX93 development kit comprises a EDM-WB baseboard and an EDM-IMX93 and EDM-IMX91 System-on-Module. The AXON-WB-IMX93 Baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's AXON-IMX93 and AXON-IMX91 System-on-Modules (SoMs). It has dimensions of 85mm x 125mm and supports various external connectors and peripheral devices.

## Overview

| Feature | Description |
|---|---|
| Displays | 24-bit parallel display + single channel LVDS Output |
| USB Type C | 1x USB 2.0 OTG |
| USB Type A | 2x USB 2.0 Host + 1x USB 2.0 Host |
| Network | 2x 10/100/1000 Ethernet RJ45 jack |
| Camera | 1x MIPI-CSI camera connectors (70-pin Hirose) |
| Audio | On-board stereo audio codec, including 3.5mm TRRS headset/mic connector and 2x 8-ohm speaker outputs |
| I/O Expansion | 1x 40-pin header (similar to Raspberry Pi)
| Console | 2x UART connectors (3.3V TTL/CMOS) |
| SD Card | 1x microSD card slot |
| M.2 Connector | 1x M.2 Key B connector with connected SIM card slot |
| Power Supply | 12VDC via DC barrel jack connector *or* USB Type C |

## Board Pictures
| **Top view, with AXON-IMX93 System-on-Module assembled** |
|---|
|![AXON-WB-IMX93 Top View](//img/axon-wb-imx93-top_w500.png)|

| **Bottom view, show M.2 slot and microSD card slot** |
|---|
| ![AXON-WB-IMX93 Back View](//img/axon-wb-imx93-back_w500.png)||

### Kit Contents

| **AXON-WB-IMX93 Kit Contents** |
|---|
| ![AXON-WB-IMX93 Kit](//img/axon-wb-imx93-kit_w500.png)| |

The AXON-WB-IMX93 kits all contain the following:
- AXON-WB baseboard
- AXON-IMX93 Module
- Wireless antenna (PCB type)
- USB-UART cable
- USB Type C to Type A cable
- Standoffs, fasteners, and wireless certification decal

## Getting Started
| AXON-WB-IMX93 top view |
|---|
| [Add top view image with connector locations identified]|

## Board Power

The EDM-WB-IMX93 baseboard can be powered either by the USB type C connector (5V), or by the 12V DCIN jack (12V supply sold separately).

A 12V power supply is necessary for connecting an LVDS display or a VizionLink camera to the board. Additionally, it is recommended when attaching high power-consumption peripherals such as powered USB devices or PCIe NVMe cards to the M.2 slot.

### Install Demo Software Images

When your kit arrives, it will be preprogrammed with an image that will assist you in loading software onto the board for the first time. This image is known as the TechNexion Software Loader (or TSL).

The TSL connects the kit to an internet-connected network via the Ethernet port. It operates as a graphical user interface application and functions optimally when used with an attached display, such as the [LVDS touchscreen kit](https://www.technexion.com/shop/system-on-modules/accessories/vl101-12880yl-c13/).

The TSL will guide you through a series of screens, enabling you to choose the type of image (Yocto, Debian, or Android) and the storage location (e.g. eMMC, SD card).

### Additional Demo Images

If you want to download additional demo images, you can easily do this by navigating your browser to our download server.  We usually update these 2-3 times a year as we release updated BSPs.

[Download Additional EDM-IMX93 Demo Images](https://download.technexion.com/demo_software/EDM/IMX9/edm-imx93/DiskImage/)

### Boot Media Selection Switch
[Add boot media selection info here - this is from AXON-WB: There are two primary boot media selection settings on the Wandboard EDM-G: boot from e.MMC and boot from the baseboard microSD card. These settings are controlled using the DIP switch **SW1**. When **SW1** is OFF, the board will boot from e.MMC. Conversely, when **SW1** is ON, the board will boot from the baseboard microSD card.]

:::info
Note the default boot media is EMMC
:::

### Boot Mode Selection Button
[Add boot mode selection info here -  this is from AXON-WB: There are two main boot modes: Normal boot, where the SOC boots from either e.MMC or microSD card (determined by the SW1 setting), and serial download mode. Serial download mode is useful when reprogramming a blank e.MMC or completely overwriting the e.MMC without needing to run a serial console.

To boot into serial download mode, press and hold BUTTON1 while pressing RESET1 (reset pushbutton switch).]

**Boot Media and Boot Mode election**

| Mode | Setting |
|---|---|
| EMMC | [Add EMMC boot setting picture] |
| microSD (on baseboard) | [Add microSD boot setting picture]|
| Serial download mode | [Add serial download mode picture] |

## Documentation

Refer to the SoM product page’s **Documentation** section for details on the development kit. It includes schematics for all baseboards and PCB files.  

[EDM-WB-IMX93 - technexion.com](https://www.technexion.com/products/system-on-modules/edm/edm-imx93)

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.