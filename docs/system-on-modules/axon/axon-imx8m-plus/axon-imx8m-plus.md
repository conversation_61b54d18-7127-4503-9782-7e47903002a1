---
sidebar_position: 1
---

# AXON-IMX8M-PLUS

| Top View | Back View |
|---|---|
| ![AXON-IMX8M-PLUS-top-cropped-w250](//img/axon-imx8m-plus-b-top_w250.png) | ![AXON-IMX8M-PLUS Back View](//img/axon-imx8m-plus-b-back_w250.png) |

## Overview

The AXON-IMX8M-PLUS is a System on Module (SOM) featuring an NXP i.MX8M Plus quad-core Cortex-A53™ + M7™ processor up to 1.8GHz, suitable for commercial and industrial temperatures (-40 to 85°C). It includes Vivante GC7000Lite graphics supporting full HD HDMI, full-HD MIPI DSI display, dual-channel LVDS, and two MIPI-CSI2 cameras. With an NPU providing up to 2.3 TOPS AI/ML acceleration, it belongs to TechNexion's scalable "AXON pin-to-pin" family, offering interfaces like gigabit Ethernet, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Speed up your market entry with pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 8M Plus: Up to 4 × Cortex™-A53 1.8GHz 1 × Cortex™-M7 |
| Memory | up to 8GB DDR4-2400 |
| GPU | Vivante™ GC7000Lite (3D) and GC520L (2D) |
| NPU | Vivante™ NPU up to 2.3 TOPS |
| Display | HDMI, LVDS, MIPI DSI up to 1080p60 |
| Storage | eMMC up to 64GB, SD card (on baseboard) |
| Network | Ethernet: 10/100/1000 Mbps, PHY on module | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| USB | 2x USB3.0 OTG |
| Audio | Up to 2 I2S (SAI), PDM |
| Commercial Temperature Range | 0 to 60C |
| Industrial Temperature Range (-TI suffix) | -40 to +85 C |
| Dimensions | 58mm x 37mm |

Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/axon/AXON-IMX8M-PLUS/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings

## Pin-to-Pin Comparison

Interested to know how various members of the AXON family have pin-to-pin compatiblity?  Check out our [AXON pin comparison] for more information.

# Getting Started
The AXON-IMX8M-PLUS is part of the AXON SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the AXON-IMX8M-PLUS, it is necessary to select an appropriate development kit for your application. TechNexion suggests considering the following development kits initial development.

### Development Kits

| Kit | Description | Link to part on Technexion.com |
|:---:|:---|:---|
| [Wizard](development-kits/axon-wizard-imx8m-plus) | Full-featured development kit for AXON-IMX8M-PLUS. | [AXON-WIZARD-IMX8M-PLUS](https://www.technexion.com/products/system-on-modules/evk/axon-wizard-imx8m-plus/) |

## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)
* [Android](/docs/embedded-software/android)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.