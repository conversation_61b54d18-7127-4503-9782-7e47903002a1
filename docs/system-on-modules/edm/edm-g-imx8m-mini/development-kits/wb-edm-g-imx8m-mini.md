---
sidebar_position: 1
---

# WB-EDM-G-IMX8M-MINI 

The WB-EDM-G-IMX8M-MINI development kit comprises a WB-EDM-G baseboard and an EDM-G-IMX8M-MINI System-on-Module. The WB-EDM-G Baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's EDM-G family of System-on-Modules (SoMs). It has dimensions of 85mm x 125mm and supports various external connectors and peripheral devices.

## Overview

| Feature | Description |
|---|---|
| Displays | Dual Channel LVDS Output |
| USB Type C | 1x USB 2.0 OTG |
| USB Type A | 2x USB 2.0 Host + 1x USB 2.0 Host |
| Network | 1x 10/100/1000 Ethernet RJ45 jack |
| Camera | 1x MIPI-CSI camera connectors (70-pin Hirose) |
| Audio | On-board stereo audio codec (WM8960), including 3.5mm TRRS headset/mic connector and 2x 8-ohm speaker outputs |
| I/O Expansion | 1x 40-pin header (similar to Raspberry Pi)
| Console | 2x UART connectors (3.3V TTL/CMOS) |
| SD Card | 1x microSD card slot |
| M.2 Connector | 1x M.2 Key B connector with connected SIM card slot |
| Power Supply | 12VDC via DC barrel jack connector |

:::info
The WB-EDM-G baseboard has extensive functions for EDM-G modules. However, some connectors and features are not supported by the WB-EDM-G-IMX8M-MINI due to limitations of the i.MX8M Mini SOC or the EDM-G-IMX8M-MINI SoM. Below is a list of features on the board that the EDM-G-IMX8M-MINI does not support:
| Feature | Reason
|---|---|
| HDMI | The board includes an HDMI connector, but the i.MX8M Mini SOC does not support HDMI, so it is inactive. |
| USB3.0 | The WB-EDM-G baseboard supports USB3.0, but the i.MX8M Mini SOC only supports USB2.0. |
| Camera 2 | The WB-EDM-G baseboard supports 2 MIPI-CSI2 connectors, but the i.MX8M Mini SOC supports a single MIPI-CSI2 port.
:::

## Board Pictures
| **Top view, with EDM-G-IMX8M-PLUS System-on-Module assembled** |
|---|
|![wb-edm-g-imx8m-plus-top-w600](//img/wb-edm-g-imx8m-plus-top-w600.png) |

| **Side view, showing power entry, USB Type C, HDMI, USB Type A, Ethernet, and audio** |
|---|
| ![wb-edm-g-side-connectors-w600](//img/wb-edm-g-side-connectors-w600.png) |

| **Bottom view, show M.2 slot and microSD card slot** |
|---|
| ![wb-edm-g-imx8m-plus-back-w600](//img/wb-edm-g-imx8m-plus-back-w600.png) |

### Kit Contents

| **WB-EDM-G-IMX8M-PLUS Kit Contents** |
|---|
| ![Content_EDM-G_Kit.jpg](//img/Content_EDM-G_Kit.jpg) |

The Wandboard EDM-G kits all contain the following:
- Wandboard EDM-G baseboard (WB-EDM-G)
- EDM-G module (based on selection), preassembled with heatsink:
    - EDM-G-IMX8M-PLUS
    - EDM-G-IMX8M-MINI
    - EDM-G-IMX8M-NANO
- Wireless antenna (PCB type)
- USB-UART cable
- USB Type C to Type A cable
- Standoffs, fasteners, and wireless certification decal

## Getting Started

| WB-EDM-G-IMX8M-MINI top view |
|---|
| ![wb-edm-g-top-callouts.png](//img/wb-edm-g-top-callouts.png)|

### Boot Media Selection Switch
There are two main boot media selection settings: Boot from EMMC, and boot from microSD card. On the Wandboard EDM-G, this is controlled using the DIP switch **SW1**. If **SW1** is OFF, then the board will boot from e.MMC. If **SW1** is ON, the board will boot from the baseboard microSD card.

:::info
Note the default boot media is EMMC
:::

### Boot Mode Selection Button
There are two main boot modes: Normal boot, in which the SOC boots from either e.MMC or microSD card (as determined by the SW1 setting), and serial download mode. Serial download mode is useful when attempting to reprogram blank e.MMC, or to completely overwrite the e.MMC without a requirement to run a serial console.

To boot into serial download mode, press and hold BUTTON1 while while pressing RESET1 (reset pushbutton switch).

**Boot Media and Boot Mode election**

| Mode | Setting |
|---|---|
| EMMC | ![edm-g-imx8mp-wb_emmc.png](//img/edm-g-imx8mp-wb_emmc.png) |
| microSD (on baseboard) | ![edm-g-imx8mp-wb_sd.png](//img/edm-g-imx8mp-wb_sd.png) |
| Serial download mode | ![edm-g-imx8mp-wb_serial_download.png](//img/edm-g-imx8mp-wb_serial_download.png) |

## Documentation

[Add links to detailed documentation]

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.