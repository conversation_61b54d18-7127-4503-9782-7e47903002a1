---
sidebar_position: 5
---

# EDM-IMX91

The EDM-IMX91 is a System on Module (SoM) based on the NXP i.MX91 processor family.

## Overview

The EDM-IMX91 is a System on Module (SOM) featuring an NXP i.MX91 single-core Cortex-A55™ processor up to 1.4GHz, suitable for commercial temperatures (0 to 60°C). It supports parallel 24-bit LCD display, and 16-bit LPDDR4 memory. It belongs to TechNexion's scalable "EDM-G pin-to-pin" family, offering interfaces like gigabit Ethernet, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Speed up your market entry with pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 91: 1 × Cortex™-A55 1.4GHz |
| Memory | up to 2GB LPDDR4-2400 |
| GPU | None |
| Display | 24-bit TTL/CMOS LCD |
| Storage | eMMC up to 64GB, SD card (on baseboard) |
| Network | Ethernet: 10/100/1000 Mbps | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| USB | 1x USB2.0 OTG + 1x USB 2.0 Host |
| Audio | 1 I2S (SAI) |
| Commercial Temperature Range | 0 to 60C |
| Dimensions | 35mm x 69.6mm |

Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/edm/edm-imx91/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings
  
## Pin-to-Pin Comparison

Interesting to know how various members of the EDM family have pin-to-pin compatiblity?  Check out our [TODO EDM comparison page - add article here] for more information.

# Getting Started
The EDM-IMX91 is part of the EDM SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the EDM-IMX91, it is necessary to select an appropriate development kit for your application. TechNexion suggests considering the following development kits initial development.


### Development Kits

| Kit | Description | Link to part on Technexion.com |
|:---:|:---|:---|
| [EDM-WB-IMX93](development-kits/edm-wb-imx93) | Cost-effective development kit for EDM-IMX93/91 and suitable for most applications. Supports 2x CSI, dual-channel LVDS display, HDMI, and M.2 Key B expansion. Some functions are not available with EDM-IMX91 | [EDM-WB-IMX93](https://www.technexion.com/products/system-on-modules/evk/edm-wb-imx93/) |

## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.