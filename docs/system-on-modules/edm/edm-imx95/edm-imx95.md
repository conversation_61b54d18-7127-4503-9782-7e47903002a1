---
sidebar_position: 3
---

# EDM-IMX95

![EDM-IMX95 top view](//img/edm-imx95_top.png)

The EDM-IMX95 is a System on Module (SoM) based on the NXP i.MX95 processor family.

:::tip[**EDM-IMX95 PRERELEASE and EARLY ACCESS**]
EDM-IMX95 is scheduled for mass production in Q1 of 2026. It is currently obtainable through an early access program.  For more information, email [<EMAIL>](mailto:<EMAIL>).
:::

## Overview

The EDM-IMX95, a System on Module (SOM), features an NXP i.MX95 6-core Cortex-A55™ processor with Cortex™-M7 for realtime operations and Cortex M33™ for system management and safety processing. It includes an NXP eIQ neutron N3-1024S, an Image Signal Processor (ISP), and a 4K video processing unit. It supports MIPI-DSI + dual-channel LVDS display and 32-bit LPDDR5 memory (up to 16GB). TechNexion’s scalable “EDM-G pin-to-pin” family offers interfaces like PCIe, 10Gb Ethernet, 2x1Gb Ethernet, CAN-FD, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code are available. It is suitable for commercial temperatures (0 to 60°C) and industrial temperatures (-40 to +85C) applications.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 95: 6 × Cortex™-A55 @ 2.0GHz + Cortex™-M7 @ 800 MHz + Cortex-M33 @ 333 MHz|
| Memory | up to 16GB LPDDR5 32-bit @ 6400 MT/s |
| GPU | Arm Mali-G310 Graphic Processing Unit (GPU) |
| NPU | NXP eIQ neutron N3-1024S at up to 2 TOPS |
| Video Processing Unit | 4Kp 60 H.265 Video Encode and Decode + JPEG Encode/Decode |
| Camera | MIPI-CSI and ISP (2x 4-lane, 2.5 Gbps/lane) with PHY (one mux'd with DSI) |
| Display | 4-lane MIPI-DSI (muxed with 1 MIPI-CSI2 port) + dual-channel LVDS + MIPI-DSI (2x 4-lane, or 1x 8-lane)|
| Storage | eMMC up to 64GB, SD card (on baseboard) |
| Network | Ethernet: 10/100/1000 Mbps PHY on board + 1x 10GbE if PHY on baseboard | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| USB | 1x USB2.0 OTG + 1x USB 2.0 Host |
| CAN-FD | Up to 5 with 2 via assigned IO |
| Audio | 2 I2S (SAI) |
| Commercial Temperature Range | 0 to 60C |
| Industrial Temperature Range | -40 to 85C |
| Dimensions | 35mm x 69.6mm |


Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/edm/EDM-IMX95/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings
  
## Pin-to-Pin Comparison

Interesting to know how various members of the EDM family have pin-to-pin compatiblity?  Check out our [TODO EDM comparison page - add article here] for more information.

# Getting Started
The EDM-IMX95 is part of the EDM SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the EDM-IMX95, it is necessary to select an appropriate development kit for your application. Please check back in the future for more information regarding development kits for EDM-IMX95.

### Development Kits

Kit information will be available soon.

## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)
* [Android](/docs/embedded-software/android)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.