---
title: Pinout Comparison for EDM-G-IMX8M-PLUS/MINI/NANO 
---
## Introduction

The following table lists the pinouts of the various EDM-G modules. This is useful to know which signals and interfaces are present in various module models.

For a high-level overview, describing which functions are available on the various products, please see the [EDM Overview](/docs/system-on-modules/edm).

## Pinout Table
| **Legend** | |
|---|---|
|**PIN**|  EDM-G connector pin number |
| **Name** |  EDM-G Pin Name. This is the same on all modules. | 
|**Level** | Voltage level of the signal. In the case of differential signals (e.g. MIPI, HDMI) please refer to the SOC Datasheet. |
|**EDM-G-IMX8M-(PLUS/MINI/NANO)** | If applicable, the name of the SOC pad to which the signal is connected. Some signals are connected to other components (Ethernet PHY, LVDS converter, etc). **NC** if not connected |

|J1 |EDM-G Pin Definition|Level    |EDM-G-IMX8M-PLUS  |EDM-G-IMX8M-MINI  |EDM-G-IMX8M-NANO  |
|---|--------------------|---------|------------------|------------------|------------------|
|1  |5VIN                |5V       |5VIN              |5VIN              |5VIN              |
|2  |5VIN                |5V       |5VIN              |5VIN              |5VIN              |
|3  |5VIN                |5V       |5VIN              |5VIN              |5VIN              |
|4  |5VIN                |5V       |5VIN              |5VIN              |5VIN              |
|5  |5VIN                |5V       |5VIN              |5VIN              |5VIN              |
|6  |5VIN                |5V       |5VIN              |5VIN              |5VIN              |
|7  |3V3                 |3.3V     |3V3               |3V3               |3V3               |
|8  |3V3_REF             |3.3V     |3V3_REF           |3V3_REF           |3V3_REF           |
|9  |3V3                 |3.3V     |3V3               |3V3               |3V3               |
|10 |GND                 |GND      |GND               |GND               |GND               |
|11 |ETH_ACT_L           |GND      |ETH_ACT_L [5]     |ETH_ACT_L [5]     |ETH_ACT_L [5]     |
|12 |ETH_LINK100_N       |3.3V     |ETH_LINK100_N [5] |ETH_LINK100_N [5] |ETH_LINK100_N [5] |
|13 |ETH_ACT_H           |3.3V     |ETH_ACT_H [5]     |ETH_ACT_H [5]     |ETH_ACT_H [5]     |
|14 |ETH_LINK1000_N      |3.3V     |ETH_LINK1000_N [5]|ETH_LINK1000_N [5]|ETH_LINK1000_N [5]|
|15 |GND                 |GND      |GND               |GND               |GND               |
|16 |GND                 |GND      |GND               |GND               |GND               |
|17 |ETH_D2P             |Ethernet |ETH_D2P [5]       |ETH_D2P [5]       |ETH_D2P [5]       |
|18 |ETH_D0P             |Ethernet |ETH_D0P [5]       |ETH_D0P [5]       |ETH_D0P [5]       |
|19 |ETH_D2N             |Ethernet |ETH_D2N [5]       |ETH_D2N [5]       |ETH_D2N [5]       |
|20 |ETH_D0N             |Ethernet |ETH_D0N [5]       |ETH_D0N [5]       |ETH_D0N [5]       |
|21 |GND                 |GND      |GND               |GND               |GND               |
|22 |GND                 |GND      |GND               |GND               |GND               |
|23 |ETH_D3P             |Ethernet |ETH_D3P [5]       |ETH_D3P [5]       |ETH_D3P [5]       |
|24 |ETH_D1P             |Ethernet |ETH_D1P [5]       |ETH_D1P [5]       |ETH_D1P [5]       |
|25 |ETH_D3N             |Ethernet |ETH_D3N [5]       |ETH_D3N [5]       |ETH_D3N [5]       |
|26 |ETH_D1N             |Ethernet |ETH_D1N [5]       |ETH_D1N [5]       |ETH_D1N [5]       |
|27 |GND                 |GND      |GND               |GND               |GND               |
|28 |GND                 |GND      |GND               |GND               |GND               |
|29 |LVDS0_TX0_N         |LVDS     |LVDS0_D0_N        |LVDS0_TX0_N [1]   |LVDS0_TX0_N [1]   |
|30 |LVDS1_TX0_N         |LVDS     |LVDS1_D0_N        |LVDS1_TX0_N [1]   |LVDS1_TX0_N [1]   |
|31 |LVDS0_TX0_P         |LVDS     |LVDS0_D0_P        |LVDS0_TX0_P [1]   |LVDS0_TX0_P [1]   |
|32 |LVDS1_TX0_P         |LVDS     |LVDS1_D0_P        |LVDS1_TX0_P [1]   |LVDS1_TX0_P [1]   |
|33 |GND                 |GND      |GND               |GND               |GND               |
|34 |GND                 |GND      |GND               |GND               |GND               |
|35 |LVDS0_TX1_N         |LVDS     |LVDS0_D1_N        |LVDS0_TX1_N [1]   |LVDS0_TX1_N [1]   |
|36 |LVDS1_TX1_N         |LVDS     |LVDS1_D1_N        |LVDS1_TX1_N [1]   |LVDS1_TX1_N [1]   |
|37 |LVDS0_TX1_P         |LVDS     |LVDS0_D1_P        |LVDS0_TX1_P [1]   |LVDS0_TX1_P [1]   |
|38 |LVDS1_TX1_P         |LVDS     |LVDS1_D1_P        |LVDS1_TX1_P [1]   |LVDS1_TX1_P [1]   |
|39 |GND                 |GND      |GND               |GND               |GND               |
|40 |GND                 |GND      |GND               |GND               |GND               |
|41 |LVDS0_TX2_N         |LVDS     |LVDS0_D2_N        |LVDS0_TX2_N [1]   |LVDS0_TX2_N [1]   |
|42 |LVDS1_TX2_N         |LVDS     |LVDS1_D2_N        |LVDS1_TX2_N [1]   |LVDS1_TX2_N [1]   |
|43 |LVDS0_TX2_P         |LVDS     |LVDS0_D2_P        |LVDS0_TX2_P [1]   |LVDS0_TX2_P [1]   |
|44 |LVDS1_TX2_P         |LVDS     |LVDS1_D2_P        |LVDS1_TX2_P [1]   |LVDS1_TX2_P [1]   |
|45 |GND                 |GND      |GND               |GND               |GND               |
|46 |GND                 |GND      |GND               |GND               |GND               |
|47 |LVDS0_CLK_N         |LVDS     |LVDS0_CLK_N       |LVDS0_CLK_N [1]   |LVDS0_CLK_N [1]   |
|48 |LVDS1_CLK_N         |LVDS     |LVDS1_CLK_N       |LVDS1_CLK_N [1]   |LVDS1_CLK_N [1]   |
|49 |LVDS0_CLK_P         |LVDS     |LVDS0_CLK_P       |LVDS0_CLK_P [1]   |LVDS0_CLK_P [1]   |
|50 |LVDS1_CLK_P         |LVDS     |LVDS1_CLK_P       |LVDS1_CLK_P [1]   |LVDS1_CLK_P [1]   |
|51 |GND                 |GND      |GND               |GND               |GND               |
|52 |GND                 |GND      |GND               |GND               |GND               |
|53 |LVDS0_TX3_N         |LVDS     |LVDS0_D3_N        |LVDS0_TX3_N [1]   |LVDS0_TX3_N [1]   |
|54 |LVDS1_TX3_N         |LVDS     |LVDS1_D3_N        |LVDS1_TX3_N [1]   |LVDS1_TX3_N [1]   |
|55 |LVDS0_TX3_P         |LVDS     |LVDS0_D3_P        |LVDS0_TX3_P [1]   |LVDS0_TX3_P [1]   |
|56 |LVDS1_TX3_P         |LVDS     |LVDS1_D3_P        |LVDS1_TX3_P [1]   |LVDS1_TX3_P [1]   |
|57 |GND                 |GND      |GND               |GND               |GND               |
|58 |GND                 |GND      |GND               |GND               |GND               |
|59 |LVDS0_VDDEN         |3.3V     |SAI1_TXD0         |GPIO1_IO07        |GPIO1_IO07        |
|60 |LVDS1_VDDEN         |3.3V     |SAI1_TXD1         |SAI5_MCLK         |SAI5_MCLK         |
|61 |LVDS0_BL_EN         |3.3V     |SAI1_TXD2         |GPIO1_IO12        |GPIO1_IO12        |
|62 |LVDS1_BL_EN         |3.3V     |SAI1_TXD3         |GPIO1_IO13        |GPIO1_IO13        |
|63 |LVDS0_BL_PWM        |3.3V     |SAI5_RXD0         |SPDIF_RX          |SPDIF_RX          |
|64 |LVDS1_BL_PWM        |3.3V     |SAI5_RXC          |SPDIF_TX          |SPDIF_TX          |
|65 |DSI_BL_PWM          |3.3V     |SPDIF_EXT_CLK     |SPDIF_EXT_CLK     |SPDIF_EXT_CLK     |
|66 |PWM4                |3.3V     | SAI5_RXFS        |SAI3_MCLK         |SAI3_MCLK         |
|67 |DSI_EN              |3.3V     |SAI1_TXD4         |UART4_RXD         |UART4_RXD         |
|68 |CSI2_RST_N          |3.3V     |SAI1_RXD2         |NC                |NC                |
|69 |DSI_VDDEN           |3.3V     |SAI1_TXD5         |UART4_RXD         |UART4_RXD         |
|70 |CSI2_PWDN           |3.3V     |SAI1_RXD3         |NC                |NC                |
|71 |DSI_RST             |3.3V     |GPIO1_IO06        |GPIO1_IO11        |GPIO1_IO11        |
|72 |CSI2_CLK            |3.3V     |GPIO1_IO15        |NC                |NC                |
|73 |GND                 |GND      |GND               |GND               |GND               |
|74 |GND                 |GND      |GND               |GND               |GND               |
|75 |DSI_D0_N            |MIPI     |MIPI_DSI1_D0_N    |MIPI_DSI_D0_N [1] |MIPI_DSI_D0_N [1] |
|76 |CSI2_CLK_N          |MIPI     |MIPI_CSI2_CLK_N   |NC                |NC                |
|77 |DSI_D0_P            |MIPI     |MIPI_DSI1_D0_P    |MIPI_DSI_D0_P [1] |MIPI_DSI_D0_P [1] |
|78 |CSI2_CLK_P          |MIPI     |MIPI_CSI2_CLK_P   |NC                |NC                |
|79 |GND                 |GND      |GND               |GND               |GND               |
|80 |GND                 |GND      |GND               |GND               |GND               |
|81 |DSI_D1_N            |MIPI     |MIPI_DSI1_D1_N    |MIPI_DSI_D1_N [1] |MIPI_DSI_D1_N [1] |
|82 |CSI2_D0_N           |MIPI     | MIPI_CSI2_D0_N   |NC                |NC                |
|83 |DSI_D1_P            |MIPI     |MIPI_DSI1_D1_P    |MIPI_DSI_D1_P [1] |MIPI_DSI_D1_P [1] |
|84 |CSI2_D0_P           |MIPI     |MIPI_CSI2_D0_P    |NC                |NC                |
|85 |GND                 |GND      |GND               |GND               |GND               |
|86 |GND                 |GND      |GND               |GND               |GND               |
|87 |DSI_D2_N            |MIPI     |MIPI_DSI1_D2_N    |MIPI_DSI_D2_N [1] |MIPI_DSI_D2_N [1] |
|88 |CSI2_D1_N           |MIPI     |MIPI_CSI2_D1_N    |NC                |NC                |
|89 |DSI_D2_P            |MIPI     |MIPI_DSI1_D2_P    |MIPI_DSI_D2_P [1] |MIPI_DSI_D2_P [1] |
|90 |CSI2_D1_P           |MIPI     |MIPI_CSI2_D1_P    |NC                |NC                |
|91 |GND                 |GND      |GND               |GND               |GND               |
|92 |GND                 |GND      |GND               |GND               |GND               |
|93 |DSI_D3_N            |MIPI     |MIPI_DSI1_D3_N    |MIPI_DSI_D3_N [1] |MIPI_DSI_D3_N [1] |
|94 |CSI2_D2_N           |MIPI     |MIPI_CSI2_D2_N    |NC                |NC                |
|95 |DSI_D3_P            |MIPI     |MIPI_DSI1_D3_P    |MIPI_DSI_D3_P [1] |MIPI_DSI_D3_P [1] |
|96 |CSI2_D2_P           |MIPI     |MIPI_CSI2_D2_P    |NC                |NC                |
|97 |GND                 |GND      |GND               |GND               |GND               |
|98 |GND                 |GND      |GND               |GND               |GND               |
|99 |DSI_CLK_N           |MIPI     |MIPI_DSI1_CLK_N   |MIPI_DSI_CLK_N [1]|MIPI_DSI_CLK_N [1]|
|100|CSI2_D3_N           |MIPI     |MIPI_CSI2_D3_N    |NC                |NC                |
|101|DSI_CLK_P           |MIPI     |MIPI_DSI1_CLK_P   |MIPI_DSI_CLK_P [1]|MIPI_DSI_CLK_P [1]|
|102|CSI2_D3_P           |MIPI     |MIPI_CSI2_D3_P    |NC                |NC                |
|103|GND                 |GND      |GND               |GND               |GND               |
|104|GND                 |GND      |GND               |GND               |GND               |
|105|HDMI_CLK_N          |HDMI     |HDMI_TXC_N        |NC                |NC                |
|106|CSI1_CLK_N          |MIPI     |MIPI_CSI1_CLK_N   |MIPI_CSI_CLK_N    |MIPI_CSI_CLK_N    |
|107|HDMI_CLK_P          |HDMI     |HDMI_TXC_P        |NC                |NC                |
|108|CSI1_CLK_P          |MIPI     |MIPI_CSI1_CLK_P   |MIPI_CSI_CLK_P    |MIPI_CSI_CLK_P    |
|109|GND                 |GND      |GND               |GND               |GND               |
|110|GND                 |GND      |GND               |GND               |GND               |
|111|HDMI_TX0_N          |HDMI     |HDMI_TX0_N        |NC                |NC                |
|112|CSI1_D0_N           |MIPI     |MIPI_CSI1_D0_N    |MIPI_CSI_D0_N     |MIPI_CSI_D0_N     |
|113|HDMI_TX0_P          |HDMI     |HDMI_TX0_P        |NC                |NC                |
|114|CSI1_D0_P           |MIPI     |MIPI_CSI1_D0_P    |MIPI_CSI_D0_P     |MIPI_CSI_D0_P     |
|115|GND                 |GND      |GND               |GND               |GND               |
|116|GND                 |GND      |GND               |GND               |GND               |
|117|HDMI_TX1_N          |HDMI     |HDMI_TX1_N        |NC                |NC                |
|118|CSI1_D1_N           |MIPI     |MIPI_CSI1_D1_N    |MIPI_CSI_D1_N     |MIPI_CSI_D1_N     |
|119|HDMI_TX1_P          |HDMI     |HDMI_TX1_P        |NC                |NC                |
|120|CSI1_D1_P           |MIPI     |MIPI_CSI1_D1_P    |MIPI_CSI_D1_P     |MIPI_CSI_D1_P     |
|121|GND                 |GND      |GND               |GND               |GND               |
|122|GND                 |GND      |GND               |GND               |GND               |
|123|HDMI_TX2_N          |HDMI     |HDMI_TX2_N        |NC                |NC                |
|124|CSI1_D2_N           |MIPI     |MIPI_CSI1_D2_N    |MIPI_CSI_D2_N     |MIPI_CSI_D2_N     |
|125|HDMI_TX2_P          |HDMI     |HDMI_TX2_P        |NC                |NC                |
|126|CSI1_D2_P           |MIPI     |MIPI_CSI1_D2_P    |MIPI_CSI_D2_P     |MIPI_CSI_D2_P     |
|127|GND                 |GND      |GND               |GND               |GND               |
|128|GND                 |GND      |GND               |GND               |GND               |
|129|EARC_N_HPD          |1.8V     |EARC_N_HPD        |NC                |NC                |
|130|CSI1_D3_N           |MIPI     |MIPI_CSI1_D3_N    |MIPI_CSI_D3_N     |MIPI_CSI_D3_N     |
|131|EARC_P_UTIL         |1.8V     |EARC_P_UTIL       |NC                |NC                |
|132|CSI1_D3_P           |MIPI     |MIPI_CSI1_D3_P    |MIPI_CSI_D3_P     |MIPI_CSI_D3_P     |
|133|HDMI_HPD            |1.8V     |HDMI_HPD          |NC                |NC                |
|134|GND                 |GND      |GND               |GND               |GND               |
|135|HDMI_CEC            |1.8V     |HDMI_CEC          |NC                |NC                |
|136|CSI1_RST_N          |3.3V     |GPIO1_IO08        |GPIO1_IO05        |GPIO1_IO05        |
|137|HDMI_DDC_SCL        |1.8V     |HDMI_DDC_SCL      |NC                |NC                |
|138|CSI1_PWDN           |3.3V     |GPIO1_IO07        |GPIO1_IO06        |GPIO1_IO06        |
|139|HDMI_DDC_SDA        |1.8V     |HDMI_DDC_SDA      |NC                |NC                |
|140|CSI1_CLK            |3.3V     |GPIO1_IO14        |GPIO1_IO14        |GPIO1_IO14        |
|141|MCORE_SCL           |3.3V     |I2C3_SCL          |I2C3_SCL          |I2C3_SCL          |
|142|CSI_SCL             |3.3V     |I2C2_SCL          |I2C2_SCL          |I2C2_SCL          |
|143|MCORE_SDA           |3.3V     |I2C3_SDA          |I2C3_SDA          |I2C3_SDA          |
|144|CSI_SDA             |3.3V     |I2C2_SDA          |I2C2_SDA          |I2C2_SDA          |
|145|PCIE_WAKE_N         |3.3V     |SAI2_RXFS         |NAND_CE0_B        |NAND_CE0_B        |
|146|USB_VBUS            |3.3V     |USB1_VBUS         |USB1_VBUS         |USB1_VBUS         |
|147|PCIE_CLKREQ_N       |3.3V     |GPIO1_IO13        |SAI1_RXC          |NC                |
|148|USB_OTG_PWR_EN      |3.3V     |SAI1_RXFS         |GPIO1_IO10        |GPIO1_IO10        |
|149|PCIE_RST_N          |3.3V     |GPIO1_IO01        |SAI1_MCLK         |NC                |
|150|USB_SS_SEL          |3.3V     |SAI1_RXC          |NC                |NC                |
|151|GND                 |GND      |GND               |GND               |GND               |
|152|USB1_ID             |3.3V     |USB1_ID           |USB1_ID           |USB1_ID           |
|153|PCIE_CLK_N          |PCIE     |PCIE_REF_PAD_CLK_N|PCIE_CLK_N        |NC                |
|154|USB1_DP             |3.3V     |USB1_DP           |USB1_DP           |USB1_DP           |
|155|PCIE_CLK_P          |PCIE     |PCIE_REF_PAD_CLK_P|PCIE_CLK_P        |NC                |
|156|USB1_DN             |USB      |USB1_D_N          |USB1_DN           |USB1_DN           |
|157|GND                 |GND      |GND               |GND               |GND               |
|158|GND                 |GND      |GND               |GND               |GND               |
|159|PCIE_TX_N           |PCIE     |PCIE_TX_N         |PCIE_TX_N         |NC                |
|160|USB1_RXP            |USB      |USB1_RX_P         |NC                |NC                |
|161|PCIE_TX_P           |PCIE     |PCIE_TX_P         |PCIE_TX_P         |NC                |
|162|USB1_RXN            |USB      |USB1_RX_N         |NC                |NC                |
|163|GND                 |GND      |GND               |GND               |GND               |
|164|GND                 |GND      |GND               |GND               |GND               |
|165|PCIE_RX_N           |PCIE     |PCIE_RX_N         |PCIE_RX_N         |NC                |
|166|USB1_TXP            |USB      |USB1_TX_P         |NC                |NC                |
|167|PCIE_RX_P           |PCIE     |PCIE_RX_P         |PCIE_RX_P         |NC                |
|168|USB1_TXN            |USB      |USB1_TX_N         |NC                |NC                |
|169|GND                 |GND      |GND               |GND               |GND               |
|170|GND                 |GND      |GND               |GND               |GND               |
|171|SD_3V3              |3.3V     |SD_3V3            |SD_3V3            |SD_3V3            |
|172|USB_HUB_RESET       |3.3V     |SAI2_RXC          |NAND_ALE          |NAND_ALE          |
|173|SD_CD               |1.8V/3.3V|SD2_CD_B          |SD2_CD_B          |SD2_CD_B          |
|174|USB2_VBUS           |3.3V     |USB2_VBUS         |USB2_VBUS         |NC                |
|175|SD_CLK              |1.8V/3.3V|SD2_CLK           |SD2_CLK           |SD2_CLK           |
|176|USB2_DP             |USB      |USB2_D_P          |USB2_DP           |NC                |
|177|SD_CMD              |1.8V/3.3V|SD2_CMD           |SD2_CMD           |SD2_CMD           |
|178|USB2_DN             |USB      |USB2_D_N          |USB2_DN           |NC                |
|179|SD_DATA0            |1.8V/3.3V|SD2_DATA0         |SD2_DATA0         |SD2_DATA0         |
|180|GND                 |GND      |GND               |GND               |GND               |
|181|SD_DATA1            |1.8V/3.3V|SD2_DATA1         |SD2_DATA1         |SD2_DATA1         |
|182|USB2_RXP            |USB      |USB2_RX_P         |NC                |NC                |
|183|SD_DATA2            |1.8V/3.3V|SD2_DATA2         |SD2_DATA2         |SD2_DATA2         |
|184|USB2_RXN            |USB      |USB2_RX_N         |NC                |NC                |
|185|SD_DATA3            |1.8V/3.3V|SD2_DATA3         |SD2_DATA3         |SD2_DATA3         |
|186|GND                 |GND      |GND               |GND               |GND               |
|187|GND                 |GND      |GND               |GND               |GND               |
|188|USB2_TXP            |USB      |USB2_TX_P         |NC                |NC                |
|189|CAN1_TX             |3.3V     |SAI5_RXD1         |NC                |NC                |
|190|USB2_TXN            |USB      |USB2_TX_N         |NC                |NC                |
|191|CAN1_RX             |3.3V     |SAI5_RXD2         |NC                |NC                |
|192|GND                 |GND      |GND               |GND               |GND               |
|193|CAN2_TX             |3.3V     |SAI5_RXD3         |NC                |NC                |
|194|UART_B_TXD          |3.3V     |ECSPI2_MOSI       |ECSPI2_MOSI       |ECSPI2_MOSI       |
|195|CAN2_RX             |3.3V     |SAI5_MCLK         |NC                |NC                |
|196|UART_B_RXD          |3.3V     |ECSPI2_SCLK       |ECSPI2_SCLK       |ECSPI2_SCLK       |
|197|GND                 |GND      |GND               |GND               |GND               |
|198|UART_B_RTS          |3.3V     |ECSPI2_SS0        |ECSPI2_SS0        |ECSPI2_SS0        |
|199|UART_A_TXD          |3.3V     |UART2_TXD         |SAI3_TXC          |SAI3_TXC          |
|200|UART_B_CTS          |3.3V     |ECSPI2_MISO       |ECSPI2_MISO       |ECSPI2_MISO       |
|201|UART_A_RXD          |3.3V     |UART2_RXD         |SAI3_TXFS         |SAI3_TXFS         |
|202|UART_C_TXD          |3.3V     |SD1_DATA6 [2]     |SAI2_RXFS         |SAI2_RXFS         |
|203|UART_A_RTS          |3.3V     |UART4_TXD         |SAI3_RXD          |SAI3_RXD          |
|204|UART_C_RXD          |3.3V     |SD1_DATA7 [2]     |SAI2_RXC          |SAI2_RXC          |
|205|UART_A_CTS          |3.3V     |UART4_RXD         |SAI3_RXC          |SAI3_RXC          |
|206|UART_C_RTS          |3.3V     |SD1_RESET_B [2]   |UART3_TXD         |UART3_TXD         |
|207|GND                 |GND      |GND               |GND               |GND               |
|208|UART_C_CTS          |3.3V     |SD1_STROBE [2]    |UART3_RXD         |UART3_RXD         |
|209|I2C_A_SCL           |3.3V     |I2C4_SCL          |I2C4_SCL          |I2C4_SCL          |
|210|GND                 |GND      |GND               |GND               |GND               |
|211|I2C_A_SDA           |3.3V     |I2C4_SDA          |I2C4_SDA          |I2C4_SDA          |
|212|AUD_A_RXD           |3.3V     |SAI3_RXD          |SAI2_RXD0         |SAI2_RXD0         |
|213|I2C_B_SCL           |3.3V     |I2C2_SCL          |I2C2_SCL          |I2C2_SCL          |
|214|AUD_A_TXFS          |3.3V     |SAI3_TXFS         |SAI2_TXFS         |SAI2_TXFS         |
|215|I2C_B_SDA           |3.3V     |I2C2_SDA          |I2C2_SDA          |I2C2_SDA          |
|216|AUD_A_TXD           |3.3V     |SAI3_TXD          |SAI2_TXD0         |SAI2_TXD0         |
|217|I2C_C_SCL           |3.3V     |SPDIF_TX          |I2C3_SCL          |I2C3_SCL          |
|218|AUD_A_TXC           |3.3V     |SAI3_TXC          |SAI2_TXC          |SAI2_TXC          |
|219|I2C_C_SDA           |3.3V     |SPDIF_RX          |I2C3_SDA          |I2C3_SDA          |
|220|AUD_A_MCLK          |3.3V     |SAI3_MCLK         |SAI2_MCLK         |SAI2_MCLK         |
|221|GND                 |GND      |GND               |GND               |GND               |
|222|AUD_B_RXD           |3.3V     |SAI2_RXD0 [3]     |SAI5_RXD0         |SAI5_RXD0         |
|223|SPI_A_MOSI          |3.3V     |ECSPI1_MOSI       |UART1_TXD         |UART1_TXD         |
|224|AUD_B_TXFS          |3.3V     |SAI2_TXFS [3]     |SAI5_RXD1         |SAI5_RXD1         |
|225|SPI_A_MISO          |3.3V     |ECSPI1_MISO       |UART2_RXD         |UART2_RXD         |
|226|AUD_B_TXD           |3.3V     |SAI2_TXD0 [3]     |SAI5_RXD3         |SAI5_RXD3         |
|227|SPI_A_SCLK          |3.3V     |ECSPI1_SCLK       |UART1_RXD         |UART1_RXD         |
|228|AUD_B_TXC           |3.3V     |SAI2_TXC [3]      |SAI5_RXD2         |SAI5_RXD2         |
|229|SPI_A_SS0           |3.3V     |ECSPI1_SS0        |UART2_TXD         |UART2_TXD         |
|230|AUD_B_MCLK          |3.3V     |SAI2_MCLK         |NC                |NC                |
|231|SPI_B_SS0           |3.3V     |GPIO1_IO10        |GPIO1_IO15        |GPIO1_IO15        |
|232|GND                 |GND      |GND               |GND               |GND               |
|233|SPI_B_SCLK          |3.3V     |NC                |NC                |NC                |
|234|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|235|SPI_B_MISO          |3.3V     |NC                |NC                |NC                |
|236|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|237|SPI_B_MOSI          |3.3V     |NC                |NC                |NC                |
|238|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|239|GND                 |         |GND               |GND               |GND               |
|240|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|241|GPIO_P241           |3.3V     |SAI1_RXD0         |GPIO1_IO08        |GPIO1_IO08        |
|242|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|243|GPIO_P243           |3.3V     |SAI1_RXD1         |GPIO1_IO09        |GPIO1_IO09        |
|244|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|245|GPIO_P245           |3.3V     |SAI1_TXFS         |SAI3_TXD          |SAI3_TXD          |
|246|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|247|GPIO_P247           |3.3V     |SAI1_TXC          |SAI3_RXFS         |SAI3_RXFS         |
|248|RESERVED            |         |RESERVED          |RESERVED          |RESERVED          |
|249|GPIO_P249           |3.3V     |SAI1_RXD4         |SD1_DATA6         |SD1_DATA6         |
|250|BOOT_CFG1           |3.3V     |BOOT_CFG1         |BOOT_MODE1        |BOOT_MODE1        |
|251|GPIO_P251           |3.3V     |SAI1_RXD5         |SD1_DATA7         |SD1_DATA7         |
|252|BOOT_CFG0           |3.3V     |BOOT_CFG0         |BOOT_MODE0        |BOOT_MODE0        |
|253|GPIO_P253           |3.3V     |SAI1_RXD6         |SD1_RESET_B       |SD1_RESET_B       |
|254|BRD_CONF_SCL        |3.3V     |SAI3_RXFS [4]     |NC                |NC                |
|255|GPIO_P255           |3.3V     |SAI1_RXD7         |SD1_STROBE        |SD1_STROBE        |
|256|BRD_CONF_SDA        |3.3V     |SAI3_RXC [4]      |NC                |NC                |
|257|SYS_nRST            |3.3V     |SYS_nRST          |SYS_nRST          |SYS_nRST          |
|258|ONOFF               |1.8V     |ONOFF             |ONOFF             |ONOFF             |
|259|WDOG_B              |3.3V     |GPIO1_IO02        |GPIO1_IO02        |GPIO1_IO02        |
|260|RTC                 |3.3V     |RTC               |RTC               |RTC               |
:::note
[1] LVDS and MIPI-DSI Signals: On EDM-G-IMX8M-PLUS, these are both fully connected to the SOC. On EDM-G-IMX8M-MINI/NANO, the LVDS signals are only connected on modules ordered with the 'LVDS' option. Otherwise, the MIPI-DSI signals are connected. Note - when LVDS signals are connected, DSI signals are not connected.
[2] Signals connect to SOC through level-shifter (TXB series from TI)                                           
[3] On EDM-G-IMX8M-PLUS, AUD_B signals are shared with PCM interface of on-board BT radio.
[4] On EDM-G-IMX8M-PLUS, BRD_CONF_SCL/SDA connect to on-board I2C EEPROM               
[5] Signals connect to onboard AR8035 Gigabit Ethernet PHY (Rev A and Rev B modules) or Realtek RTL8211 PHY (Rev D modules)   
:::
                                                                                                                                                 
        


