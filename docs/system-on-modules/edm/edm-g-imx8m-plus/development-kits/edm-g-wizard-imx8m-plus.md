---
sidebar_position: 2
---

# EDM-G-WIZARD-IMX8M-PLUS

The EDM-G-WIZARD-IMX8M-PLUS development kit comprises a EDM-G-WIZARD baseboard and an EDM-G-IMX8M-PLUS System-on-Module. The EDM-G-WIZARD baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's EDM-G family of System-on-Modules (SoMs). It is a more larger (170mm x 170mm), more full-featured development kit for the EDM-G-IMX8M-PLUS System on Module.

This kit is the best option when working with the MIPI-DSI interface on applicable EDM-G SoMs.

## Overview

| Feature | Description |
|---|---|
| Displays | HDMI + Dual Channel LVDS Output + MIPI-DSI |
| USB Type C | 1x USB 3.0 OTG |
| USB Type A | 2x USB 3.0 Host + 1x USB 2.0 Host |
| Network | 1x 10/100/1000 Ethernet RJ45 jack |
| Camera | 2x MIPI-CSI camera connectors (70-pin Hirose) |
| Audio | On-board stereo audio codec (WM8960), including 3.5mm TRRS headset/mic connector and 2x 8-ohm speaker outputs |
| I/O Expansion | 1x 40-pin header (similar to Raspberry Pi)
| Console | 2x UART connectors (3.3V TTL/CMOS) |
| SD Card | 1x microSD card slot |
| M.2 Connector | 2x M.2 connectors with connected SIM card slots |
| Power Supply | 12VDC via DC barrel jack connector |

## Board Pictures
| **Top view, with EDM-G-IMX8M-PLUS System-on-Module assembled** |
|---|
|![EDM-G Wizard Top](//img/edm-g-wizard-imx8m-plus-top-hs_500x500.webp) |

| **Bottom view** |
|---|
| ![EDM-G Wizard Bottom View](//img/edm-g-wizard-imx8m-plus-back_500x500.webp) |


### Kit Contents

| **Kit View, showing cables and accessories** |
|---|
| ![EDM-G Wizard Kit](//img/edm-g-wizard-imx8m-plus-kit_600x600.webp) |


The Wizard EDM-G kits all contain the following:
- Wizard EDM-G baseboard (EDM-G-WIZARD)
- EDM-G module (based on selection), preassembled with heatsink:
    - EDM-G-IMX8M-PLUS
    - EDM-G-IMX8M-MINI
    - EDM-G-IMX8M-NANO
- Wireless antenna (PCB type)
- USB-UART cable
- USB Type C to Type A cable
- Standoffs, fasteners, and wireless certification decal

:::info
A [12V power supply](https://www.technexion.com/shop/system-on-modules/accessories/dsa-60pfe-12/), sold separately, is required to power the EDM-G-WIZARD kit.
:::

## Board Power

The EDM-G-WIZARD baseboard is powered by the 12V DCIN jack (12V supply sold separately).

A 12V power supply is necessary for connecting an LVDS display or a VizionLink camera to the board. Additionally, it is important when attaching high power-consumption peripherals such as powered USB devices or PCIe NVMe cards to the M.2 slot.

### Install Demo Software Images

When your kit arrives, it will be preprogrammed with an image that will assist you in loading software onto the board for the first time. This image is known as the TechNexion Software Loader (or TSL).

The TSL connects the kit to an internet-connected network via the Ethernet port. It operates as a graphical user interface application and functions optimally when used with an attached display, such as the [LVDS touchscreen kit](https://www.technexion.com/shop/system-on-modules/accessories/vl101-12880yl-c13/). Alternatively, an HDMI display can be used, but a USB mouse must be connected to one of the USB ports.

The TSL will guide you through a series of screens, enabling you to choose the type of image (Yocto, Debian, or Android) and the storage location (e.g. eMMC, SD card).

### Additional Demo Images

If you want to download additional demo images, you can easily do this by navigating your browser to our download server.  We usually update these 2-3 times a year as we release updated BSPs.

[Download Additional EDM-G-IMX8MP Demo Images](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/DiskImage/)

### Boot Media Selection Switch
There are two primary boot media selection settings on the Wandboard EDM-G: boot from e.MMC and boot from the baseboard microSD card. These settings are controlled using the DIP switch **SW1**. When **SW1** is OFF, the board will boot from e.MMC. Conversely, when **SW1** is ON, the board will boot from the baseboard microSD card.

:::info
Note the default boot media is EMMC
:::

### Boot Mode Selection Button
There are two main boot modes: Normal boot, where the SOC boots from either e.MMC or microSD card (determined by the SW1 setting), and serial download mode. Serial download mode is useful when reprogramming a blank e.MMC or completely overwriting the e.MMC without needing to run a serial console.

To boot into serial download mode, press and hold BUTTON1 while pressing RESET1 (reset pushbutton switch).

**Boot Media and Boot Mode election**

| Mode | Setting |
|---|---|
| EMMC | ![EDM-G Wizard EMMC boot setting](//img/edm-g-imx8mp-wizard_emmc.png) |
| microSD (on baseboard) | ![EDM-G-Wizard SD card boot setting](//img/edm-g-imx8mp-wizard_sd.png) |
| Serial download mode | ![EDM-G-Wizard Serial Download setting](//img/edm-g-imx8mp-wizard_serial_download.png) |

## Documentation

Refer to the SoM product page’s **Documentation** section for details on the development kit. It includes schematics for all baseboards and PCB files.  

[EDM-G-IMX8M-PLUS - technexion.com](https://www.technexion.com/products/system-on-modules/edm/edm-g-imx8m-plus/)

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.