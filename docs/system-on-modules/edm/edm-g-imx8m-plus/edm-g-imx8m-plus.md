---
sidebar_position: 1
---

# EDM-G-IMX8M-PLUS

![edm-g-imx8m-plus-top-cropped-w250](//img/edm-g-imx8m-plus-top-cropped-w250.png)

## Overview

The EDM-G-IMX8M-PLUS is a System on Module (SOM) featuring an NXP i.MX8M Plus quad-core Cortex-A53™ + M7™ processor up to 1.8GHz, suitable for commercial and industrial temperatures (-40 to 85°C). It includes Vivante GC7000Lite graphics supporting full HD HDMI, full-HD MIPI DSI display, dual-channel LVDS, and two MIPI-CSI2 cameras. With an NPU providing up to 2.3 TOPS AI/ML acceleration, it belongs to TechNexion's scalable "EDM-G pin-to-pin" family, offering interfaces like gigabit Ethernet, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Speed up your market entry with pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 8M Plus: Up to 4 × Cortex™-A53 1.8GHz 1 × Cortex™-M7 |
| Memory | up to 8GB DDR4-2400 |
| GPU | Vivante™ GC7000Lite (3D) and GC520L (2D) |
| NPU | Vivante™ NPU up to 2.3 TOPS |
| Display | HDMI, LVDS, MIPI DSI up to 1080p60 |
| Storage | eMMC up to 64GB, SD card (on baseboard) |
| Network | Ethernet: 10/100/1000 Mbps | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| USB | 2x USB3.0 OTG |
| Audio | Up to 2 I2S (SAI), PDM |
| Commercial Temperature Range | 0 to 60C |
| Industrial Temperature Range (-TI suffix) | -40 to +85 C |
| Dimensions | 35mm x 69.6mm |

Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/edm/edm-g-imx8m-plus/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings

## Pin-to-Pin Comparison

Interesting to know how various members of the EDM-G family have pin-to-pin compatiblity?  Check out our [EDM-G comparison page](edm-g-pinout-comparison) for more information.

# Getting Started
The EDM-G-IMX8M-PLUS is part of the EDM SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the EDM-G-IMX8M-PLUS, it is necessary to select an appropriate development kit for your application. TechNexion suggests considering the following development kits initial development.

### Development Kits

| Kit | Description | Link to part on Technexion.com |
|:---:|:---|:---|
| [Wandboard](development-kits/wb-edm-g-imx8m-plus) | Cost-effective development kit for EDM-G-IMX8M-PLUS and suitable for most applications. Supports 2x CSI, dual-channel LVDS display, HDMI, and M.2 Key B expansion.  | [WB-EDM-G-IMX8M-PLUS](https://www.technexion.com/products/system-on-modules/evk/wb-edm-g-imx8m-plus/) |
| [Wizard](development-kits/edm-g-wizard-imx8m-plus) | Full-featured development kit for EDM-G-IMX8M-PLUS, best if you require support for MIPI-DSI displays. | [EDM-G-WIZARD-IMX8M-PLUS](https://www.technexion.com/products/system-on-modules/evk/edm-g-wizard-imx8m-plus/) |

## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)
* [Android](/docs/embedded-software/android)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.