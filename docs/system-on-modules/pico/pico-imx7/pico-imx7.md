---
sidebar_position: 3
---

# PICO-IMX7

| Top View | Back View |
|---|---|
| ![PICO-IMX7 Top View](//img/pico-imx7-top_w250.png) | ![PICO-IMX7 Back View](//img/pico-imx7-back_w250.png) |

## Overview

The PICO-IMX7 is a System on Module (SOM) featuring an NXP i.MX7 dual-core Cortex-A7™ + Cortex-M4 processor up to 1.0 GHz, suitable for commercial and extended temperatures (-20 to 70°C). It includes 2D graphics acceleration supporting 24-bit parallel LCD and 2-lane MIPI DSI display.  It supports a single MIPI-CSI2 interface with up to 2-lanes.  It belongs to TechNexion's scalable "PICO pin-to-pin" family, offering interfaces like gigabit Ethernet, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Speed up your market entry with pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 7 Dual: Up to 2 × Cortex™-A7 @1.0 GHz |
| Memory | up to 2GB DDR3L |
| GPU | Image re-sizing / rotation / overlay and CSC Pixel Processing Pipeline |
| Display | parallel LCD (24-bit), 2-lane MIPI DSI |
| Storage | eMMC up to 64GB or SD card, depending on module selection |
| Network | Ethernet: 10/100/1000 Mbps, PHY on baseboard | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| USB | 1x USB2.0 OTG + 1x USB2.0 Host |
| Audio | Up to 2 I2S (SAI) |
| PCIe | 1x 1-lane PCIe 2.0 |
| Commercial Temperature Range | 0 to 60C |
| Extended Temperature Range (-TE suffix) | -20 to +70 C |
| Dimensions | 37mm x 40mm |

Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/pico/PICO-IMX7/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings

## Pin-to-Pin Comparison

Interested to know how various members of the PICO family have pin-to-pin compatiblity?  Check out our [PICO pin comparison] for more information.

# Getting Started
The PICO-IMX7 is part of the PICO SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the PICO-IMX7, it is necessary to select an appropriate development kit for your application. TechNexion suggests considering the following development kits initial development.

### Development Kits

| Kit | Description | Link to part on Technexion.com |
|:---:|:---|:---|
| [Nymph](development-kits/pico-pi-imx7) | Low-cost development kit for PICO-IMX7. | [PICO-PI-IMX7](https://www.technexion.com/products/system-on-modules/evk/pico-pi-imx7/) |

## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)
* [Android](/docs/embedded-software/android)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.