---
sidebar_position: 3
---

# PICO-PI-IMX7

The PICO-PI-IMX7 development kit comprises a PICO-PI-GL baseboard and an PICO-IMX7 System-on-Module, and comes assembled within an enclosure. The PICO-PI-GL baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's PICO-IMX7 and PICO-IMX6 family of System-on-Modules (SoMs). 

## Overview

| Feature | Description |
|---|---|
| Displays | 24-bit parallel LCD |
| USB Type C | 1x USB 2.0 OTG |
| USB Type A | 1x USB 2.0 Host |
| Network | 1x 10/100/1000 Ethernet RJ45 jack |
| Camera | 33-pin MIPI-CSI FPC connector |
| Audio | On-board stereo audio codec (SGTL5000), including 3.5mm TRRS headset + microphone connector |
| Console | 1x UART via onboard USB-UART translator via microUSB connector |
| Expansion | 40-pin expansion IO connector, 1x Mikrobus slot |
| Power Supply | 5V DC, via USB Type C connector |

## Board Pictures
| **Top view, with PICO-IMX7 System-on-Module assembled** |
|---|
|![PICO PI Top View](//img/pico-pi-imx7-top_w500.png) |

| **With 5" parellel LCD touchpanel** |
|---|
| ![PICO PI Bottom View](//img/pico-pi-imx7-panel_w500.png) |

### Kit Contents

The PICO kits all contain the following:
- PICO baseboard (PICO-PI-GL)
- PICO-IMX7 module preassembled with heatsink
- Wireless antenna (PCB-type)
- USB Type C to Type A cable
- micro-USB cable
- Wireless certification decal

## Board Power

The PICO-PI-GL baseboard (and the entire kit) is powered by the USB-C connector.

### Install Demo Software Images

When your kit arrives, it will be preprogrammed with an image that will assist you in loading software onto the board for the first time. This image is known as the TechNexion Software Loader (or TSL).

The TSL connects the kit to an internet-connected network via the Ethernet port. It operates as a graphical user interface application and functions optimally when used with an attached display, such as an LCD touchscreen.

The TSL will guide you through a series of screens, enabling you to choose the type of image (Yocto, Debian, or Android) and the storage location (e.g. eMMC, SD card).

### Additional Demo Images

If you want to download additional demo images, you can easily do this by navigating your browser to our download server.  We usually update these 2-3 times a year as we release updated BSPs.

[Download Additional PICO-IMX7 Demo Images](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/DiskImage/)

### Boot Media Selection and Boot Mode
There are two primary boot media selection settings on the PICO-PI-GL: boot from e.MMC or the module's microSD card slot (depending on the module). These settings are controlled using 4 3-position jumpers.  These jumper settings are also used to configure the boot mode (normal boot, or serial download mode).

:::info
Note the default boot media is EMMC
:::

**Boot Media and Boot Mode election**

| | PICO-PI-IMX7 |
|---|---|
| EMMC (For modules with EMMC on the SOM) | ![pico-imx6-pi-emmc](//img/pico-imx6-pi-emmc.png) |
| SOM micro SD (For modules with micro SD slots on the SOM) | ![pico-imx6-pi-sd-module](//img/pico-imx6-pi-sd-module.png) | 
| Serial Download Mode | ![pico-imx7-pi-serial-download-mode](//img/pico-imx7-pi-serial-download-mode.png) | 

## Documentation

Refer to the SoM product page’s **Documentation** section for details on the development kit. It includes schematics for all baseboards and PCB files.  

[PICO-IMX7 - technexion.com](https://www.technexion.com/products/system-on-modules/PICO/PICO-IMX7/)

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.