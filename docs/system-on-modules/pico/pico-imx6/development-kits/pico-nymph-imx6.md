---
sidebar_position: 2
---

# PICO-NYMPH-IMX6

The PICO-NYMPH-IMX6 development kit comprises a PICO-NYMPH baseboard and an PICO-IMX6 System-on-Module, and comes assembled within an enclosure. The PICO-NYMPH baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's PICO family of System-on-Modules (SoMs). 

This kit is the best option when working with the HDMI or LVDS interfaces with PICO-IMX6 modules.

## Overview

| Feature | Description |
|---|---|
| Displays | HDMI + Dual Channel LVDS Output + VGA |
| USB Type C | 1x USB 2.0 OTG |
| USB Type A | 2x USB 2.0 Host |
| Network | 1x 10/100/1000 Ethernet RJ45 jack |
| Camera | 33-pin MIPI-CSI FPC connector |
| Audio | On-board stereo audio codec (SGTL5000), including 3.5mm TRRS headset + Line out connector |
| Console | 1x UART connectors (3.3V TTL/CMOS) |
| PCIe | 1x M.2 slot |
| Mini PCIe | 1x MIPI-PCIe slot with USB2.0 connection + SIM card slot |
| SD Card | 1x microSD card slot |
| M.2 Connector | 2x M.2 connectors with connected SIM card slots |
| Power Supply | 12VDC via molex connector|

## Board Pictures
| **Kit view, in enclosure** |
|---|
|![PICO Nymph Kit](//img/pico-nymph-imx6-34left-top_w500.png) |

| **Top view, with PICO-IMX6 System-on-Module assembled** |
|---|
|![PICO Nymph Top View](//img/pico-nymph-imx6-top_w500.png) |

| **Bottom view** |
|---|
| ![PICO Nymph Bottom View](//img/pico-nymph-imx6-back_w500.png) |

### Kit Contents

The PICO kits all contain the following:
- PICO baseboard (PICO-NYMPH)
- PICO-IMX6 module preassembled with heatsink
- Enclosure
- Wireless antenna (Dipole type)
- USB Type C to Type A cable
- Wireless certification decal

## Board Power

The PICO-NYMPH baseboard is powered by the 12V DCIN molex connector.

### Install Demo Software Images

When your kit arrives, it will be preprogrammed with an image that will assist you in loading software onto the board for the first time. This image is known as the TechNexion Software Loader (or TSL).

The TSL connects the kit to an internet-connected network via the Ethernet port. It operates as a graphical user interface application and functions optimally when used with an attached display, such as an HDMI display. A USB mouse must be connected to one of the USB ports.

The TSL will guide you through a series of screens, enabling you to choose the type of image (Yocto, Debian, or Android) and the storage location (e.g. eMMC, SD card).

### Additional Demo Images

If you want to download additional demo images, you can easily do this by navigating your browser to our download server.  We usually update these 2-3 times a year as we release updated BSPs.

[Download Additional PICO-IMX6 Demo Images](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/DiskImage/)

### Boot Media Selection and Boot Mode
There are two primary boot media selection settings on the PICO-NYMPH: boot from e.MMC and boot from the baseboard microSD card. These settings are controlled using 4 3-position jumpers.  These jumper settings are also used to configure the boot mode (normal boot, or serial download mode).

:::info
Note the default boot media is EMMC
:::

**Boot Media and Boot Mode election**

| | NYMPH |
|---|---|
| EMMC (For modules with EMMC on the SOM) | ![pico-imx6-nymph-emmc](//img/pico-imx6-nymph-emmc_w150.png) |
| SOM micro SD (For modules with micro SD slots on the SOM) | ![pico-imx6-nymph-sd-module](//img/pico-imx6-nymph-sd-module_w150.png) | 
| Serial Download Mode | ![pico-imx6-nymph-serial-download-mode](//img/pico-imx6-nymph-serial-download-mode_w150.png) | 

## Documentation

Refer to the SoM product page’s **Documentation** section for details on the development kit. It includes schematics for all baseboards and PCB files.  

[PICO-IMX6 - technexion.com](https://www.technexion.com/products/system-on-modules/PICO/PICO-IMX6/)

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.