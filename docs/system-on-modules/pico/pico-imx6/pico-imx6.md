---
sidebar_position: 4
---

# PICO-IMX6

| Top View | Back View |
|---|---|
| ![PICO-IMX6Q Top View](//img/pico-imx6q-top_w250.png) | ![PICO-IMX6Q Back View](//img/pico-imx6q-back_w250.png) |

## Overview

The PICO-IMX6 is a System on Module (SOM) featuring an NXP i.MX6 quad-core Cortex-A9™ processor up to 1.0 GHz, suitable for commercial and industrial temperatures (-40 to 85°C). It includes Vivante GC2000 graphics supporting full HD HDMI, dual-channel LVDS, 24-bit parallel LCD, and 2-lane MIPI DSI display.  It supports a single MIPI-CSI2 interface with up to 4-lanes.  It belongs to TechNexion's scalable "PICO pin-to-pin" family, offering interfaces like gigabit Ethernet, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Speed up your market entry with pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 6 Solo/Dual-Lite/Quad: Up to 4 × Cortex™-A9 @1.2 GHz |
| Memory | up to 2GB DDR3L (Dual-Lite/Quad), Up to 1GB (Solo) |
| GPU | Vivante™ GC2000 (3D), GC355 (Vector graphics), and GC320 (2D composition) |
| Display | HDMI, LVDS, parallel LCD (24-bit), MIPI DSI |
| Storage | eMMC up to 64GB, SD card (on baseboard) |
| Network | Ethernet: 10/100/1000 Mbps, PHY on baseboard | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| USB | 1x USB2.0 OTG + 1x USB2.0 Host |
| Audio | Up to 2 I2S (SAI) |
| PCIe | 1x 1-lane PCIe 2.0 |
| Commercial Temperature Range | 0 to 60C |
| Industrial Temperature Range (-TI suffix) | -40 to +85 C |
| Dimensions | 37mm x 40mm |

Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/pico/PICO-IMX6/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings

## Pin-to-Pin Comparison

Interested to know how various members of the PICO family have pin-to-pin compatiblity?  Check out our [PICO pin comparison] for more information.

# Getting Started
The PICO-IMX6 is part of the PICO SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the PICO-IMX6, it is necessary to select an appropriate development kit for your application. TechNexion suggests considering the following development kits initial development.

### Development Kits

| Kit | Description | Link to part on Technexion.com |
|:---:|:---|:---|
| [Nymph](development-kits/pico-nymph-imx6) | Full-featured development kit for PICO-IMX6. | [PICO-HYMPH-IMX6](https://www.technexion.com/products/system-on-modules/evk/pico-nymph-imx6/) |

## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)
* [Android](/docs/embedded-software/android)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.