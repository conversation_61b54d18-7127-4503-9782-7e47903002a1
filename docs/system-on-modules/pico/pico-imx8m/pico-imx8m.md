---
sidebar_position: 6
---

# PICO-IMX8M
| Top View |
|---|
|![PICO-IMX8M Top](//img/pico-imx8m-top-cropped-w250.png) |

## Overview

The PICO-IMX8M is a System on Module (SOM) featuring an NXP i.MX8M quad-core Cortex-A53™ + M4™ processor up to 1.5GHz, suitable for commercial and industrial temperatures (-40 to 85°C). It includes Vivante GC7000Lite graphics supporting 4K HDMI + full-HD MIPI DSI display and 2x MIPI-CSI2 cameras. It belongs to TechNexion's scalable "PICO pin-to-pin" family, offering interfaces like gigabit Ethernet, PCIe, USB, I2S, UART, SPI, I2C, PWM, GPIO for IIoT edge devices. Speed up your market entry with pre-certified dual-band Wi-Fi, Bluetooth, and available Linux, Yocto, Android images and source code.

## Technical Specifications

| Feature | Description |
| --- | --- |
| CPU | NXP i.MX 8M: Up to 4 × Cortex™-A53 at up to 1.5GHz (1.3 GHz standard product) + 1 × Cortex™-M4 |
| Memory | up to 8GB LPDDR4 |
| GPU | Vivante™ GC7000Lite |
| Display | HDMI up to 4K + MIPI DSI 4-lanes up to 1080p60 |
| Storage | eMMC up to 64GB |
| Network | RGMII Ethernet: 10/100/1000 Mbps (PHY on baseboard) | 
| Wireless | WiFi: Certified 802.11 a/b/g/n/ac, Bluetooth: 5.0, BLE |
| RTC | on baseboard |
| I2C | 3 |
| SPI | 2 |
| UARTs | Up to 3 |
| PCIe | 1x 1-lane |
| USB | 1x USB2.0 OTG, 1x USB2.0 Host |
| Audio | Up to 2 I2S (SAI) |
| Commercial Temperature Range | 0 to 60C |
| Industrial Temperature Range (-TI suffix) | -40 to +85 C |
| Dimensions | 37 x 40mm |

Visit [product page on TechNexion website](https://www.technexion.com/products/system-on-modules/pico/pico-imx8m/) for:
* Reference Manuals
* Schematic and gerbers of our evaluation kits
* Mechanical drawings
  
## Pin-to-Pin Comparison

Interesting to know how various members of the PICO family have pin-to-pin compatiblity?  Check out our [PICO comparison page] for more information.

## Getting Started
The PICO-IMX8M is part of the PICO SoM product line. In this documentation, you will find comprehensive information about product features, development kits, and software.

## Recommendations for Initial Development

WWhen beginning your work with the PICO-IMX8M, it is necessary to select an appropriate development kit for your application. TechNexion suggests considering the following development kits initial development.

### Development Kits

| Kit | Description | Link to part on Technexion.com |
|:---:|:---|:---|
| [PICO-PI-IMX8M](development-kits/pico-pi-imx8m) | Cost-effective development kit for PICO-IMX8M and suitable for most applications. Supports 1x CSI, MIPI-DSI, and Ethernet  | [PICO-PI-IMX8M](https://www.technexion.com/products/system-on-modules/evk/pico-pi-imx8m/) |
| [PICO-WIZARD-IMX8M](development-kits/pico-wizard-imx8m) | Full-featured development kit for PICO-IMX8M. Supports 1x CSI, MIPI-DSI, PCIe, and Ethernet  | [PICO-WIZARD-IMX8M-MINI](https://www.technexion.com/products/system-on-modules/evk/pico-wizard-imx8m/) |


## Software Development

This product is supported with the following operating systems.
* [Yocto Linux](/docs/embedded-software/linux/yocto)
* [Debian Linux](/docs/embedded-software/linux/debian)
* [Android](/docs/embedded-software/android)

Visit our [embedded software](/docs/embedded-software) page for instructions on developing software for this product.

## Support Options

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.