---
sidebar_position: 1
---

# PICO-WIZARD-IMX8M

The PICO-WIZARD-IMX8M development kit comprises a PICO-WIZARD baseboard and an PICO-IMX8M System-on-Module. The PICO-WIZARD Baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's PICO family of System-on-Modules (SoMs). It supports various external connectors and peripheral devices.

## Overview

| Feature | Description |
|---|---|
| Displays | HDMI + MIPI-DSI (4x-lanes) + touchscreen |
| USB Type C | 1x USB 2.0 OTG |
| USB Type A | 2x USB 2.0 Host |
| Network | 1x 10/100/1000 Ethernet RJ45 jack |
| Camera | 2x MIPI-CSI camera connectors (33-pin FPC) |
| Audio | On-board stereo audio codec (WM8960), including 3.5mm TRRS headset/mic connector + stereo speaker output |
| I/O Expansion | 1x 40-pin header (similar to Raspberry Pi)
| Console | UART interface by through-hole |
| SD Card | 1x microSD card slot |
| Expansion Slots | 1x M.2 (USB2.0 only by default), 1x Mini-PCIe |
| Power Supply | 12VDC via microFit3 Connector |

## Board Pictures
| **Top view, with 720x1280 MIPI-DSI touchpanel kit (Panel sold separately)** |
|---|
| ![PICO-WIZARD-side-connectors-w600](//img/pico-wizard-imx8mm-panel_w500.png) |


### Kit Contents

The PICO-WIZARD-IMX8M kit contains the following:
- PICO-WIZARD baseboard
- PICO-IMX8M module, preassembled with heatsink
- Wireless antenna (PCB type)
- USB to UART cable (for serial console)
- USB Type A to Type C cable (for power and programming)

## Getting Started

### Connecting the Serial Console Cable

On PICO-WIZARD, the main console port connecto reference designator is **M1**.
![pico-wizard-cable-m1.png](//img/pico-wizard-cable-m1.png)

### Board Power

The PICO-WIZARD baseboard is powered by 12V DC, via the micro-Fit3.0 connector.

### Install Demo Software Images

When your kit arrives, it will be preprogrammed with an image that will assist you in loading software onto the board for the first time. This image is known as the TechNexion Software Loader (or TSL).

The TSL connects the kit to an internet-connected network via the Ethernet port. It operates as a graphical user interface application and functions optimally when used with an attached display, such as the 5" MIPI-DSI touchpanel kit, which is available for purchase when you order the PICO-WIZARD-IMX8M.

The TSL will guide you through a series of screens, enabling you to choose the type of image (Yocto, Debian, or Android) and the storage location (e.g. eMMC, SD card).

### Additional Demo Images

If you want to download additional demo images, you can easily do this by navigating your browser to our download server.  We usually update these 2-3 times a year as we release updated BSPs.

[Download Additional PICO-IMX8M Demo Images](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8m/DiskImage/)

### Flashing e.MMC

During development or initial manufacturing, you may want to flash the entire e.MMC.  Or, you way want to recover a 'bricked' unit (one that will not boot).  To do this, you can follow our step-by-step tutorial showing how to use the `uuu` tool here:

[Using 'uuu' to flash e.MMC](/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

If the board boots, and you have access to a serial console, you may want to use U-boot's `ums` command to load software to the e.MMC:

[Using U-boot's 'ums' command to flash e.MMC](/docs/embedded-software/linux/usage-guides/loading-software/using-u-boots-ums-command-to-write-flash-storage-over-usb-otg)

### Recovery to Factory Settings

It may be necessary or desireable to recover the unit to factory settings.  For example, you may want to use the Technexion Software Loader to download and install a different demo image to the board.

The process for doing this is identical to flashing a regular Linux image to your board, except that you would need to download the TechNexion Software Loader (TSL) image instead.

| Download Software Loader Image|
|---|
| [PICO-WIZARD-IMX8M - HDMI display](https://download.technexion.com/rescue/pico-imx8mq/wizard-hdmi1920x1080/rescue-22.04.2.bz2) |

### Boot Media and Boot Mode Selection

There are two primary options on the PICO-WIZARD-IMX8M: Boot from e.MMC, or boot into serial download mode, allowing images to be loaded and configuration of the module over USB. These settings are controlled using boot configuration jumpers.  See the table below.

:::info
Note the default boot media is EMMC. On the module, we configure boot configuration signals to boot from the default media without needing to be pulled high or low on the baseboard. You can either leave the jumpers as set here, or remove them.
:::

## boot from e.mmc
| mode | jumper configuration |
| --- | --- |
| boot from e.mmc | ![pico-imx8m-wizard_emmc](//img/pico-imx8m-wizard_emmc.png) |

## boot into serial download mode
| mode | jumper configuration |
| --- | --- |
| serial download mode | ![pico-imx8m-wizard_serial_download](//img/pico-imx8m-wizard_serial_download.png) |

## Documentation

Refer to the SoM product page’s **Documentation** section for details on the development kit. It includes schematics for all baseboards and PCB files.  

[PICO-IMX8M - technexion.com](https://www.technexion.com/products/system-on-modules/pico/PICO-IMX8M/)

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.