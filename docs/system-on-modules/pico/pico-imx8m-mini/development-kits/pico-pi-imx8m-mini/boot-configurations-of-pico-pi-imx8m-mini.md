---
title: boot configurations of pico-pi-imx8m-mini
description: add document description here
---
below is a picture representation of the boot configuration jumper settings for pico-imx8m-mini on our pico-pi baseboard. configure the jumpers according to the boot selection you need.

## boot configurations
there are two main boot configuration settings: boot from emmc, and serial download mode. there is also an option to boot from microsd card on pico-pi, via the included clix sd card adapter.

:::info
pico-imx8m-mini, the default boot media is emmc. on the module, we configure boot configuration signals to boot from the default media without needing to be pulled high or low on the baseboard. you can either leave the jumpers as set here, or remove them.
:::
### boot from e.mmc
| mode | jumper configuration |
| --- | --- |
| boot from e.mmc | ![pico-imx8m-pi-emmc](//img/pico-imx8m-pi-emmc.png) |

### boot from sd card (on clix module)
| mode | jumper configuration |
| --- | --- |
| boot from sd card (on clix module) | ![pico-imx8m-pi_sd](//img/pico-imx8m-pi_sd.png) |

### boot into serial download mode
| mode | jumper configuration |
| --- | --- |
| serial download mode | ![pico-imx8m-pi-serial-download-mode](//img/pico-imx8m-pi-serial-download-mode.png) |





