---
sidebar_position: 1
---

# PICO-PI-IMX8M-MINI

The PICO-PI-IMX8M-MINI development kit comprises a PICO-PI-8M baseboard and an PICO-IMX8M-MINI System-on-Module. The PICO-PI-8M Baseboard is a printed circuit board assembly (PCBA) designed to be compatible with TechNexion's PICO family of System-on-Modules (SoMs). It supports various external connectors and peripheral devices.

## Overview

| Feature | Description |
|---|---|
| Displays | MIPI-DSI (4x-lanes) + touchscreen |
| USB Type C | 1x USB 2.0 OTG |
| USB Type A | 1x USB 2.0 Host |
| Network | 1x 10/100/1000 Ethernet RJ45 jack |
| Camera | 2x MIPI-CSI camera connectors (33-pin FPC), with CSI1 active only  |
| Audio | On-board stereo audio codec (SGTL5000), including 3.5mm TRRS headset/mic connector and 2x 8-ohm speaker outputs |
| I/O Expansion | 1x 40-pin header (similar to Raspberry Pi)
| Console | USB-UART console device on-board |
| SD Card | 1x microSD card slot |
| Power Supply | 5 VDC via USB Type C |

## Board Pictures
| **Top view, with PICO-IMX8M-MINI System-on-Module assembled** |
|---|
|![PICO-PI-IMX8M-MINI Top](//img/pico-pi-imx8m-mini-top_w500.png) |

| **Bottom view** |
|---|
| ![PICO-PI-IMX8M-MINI Bottom](//img/pico-pi-imx8m-mini-back_w500.png) |

| **With 720x1280 MIPI-DSI touchpanel kit (Panel sold separately)** |
|---|
| ![PICO-PI-8M-side-connectors-w600](//img/pico-pi-imx8m-mini-panel_w500.png) |

:::warning HDMI connector unused
The PICO-PI-8M integrated HDMI connector is not-functional on PICO-PI-IMX8M-MINI, as the i.MX8M Mini SOC does not have an integrated HDMI interface.
:::

### Kit Contents

The PICO-PI-IMX8M-MINI kit contains the following:
- PICO-PI-8M baseboard
- PICO-IMX8M-MINI module, preassembled with heatsink
- Wireless antenna (PCB type)
- MicroUSB cable (for serial console)
- USB Type A to Type C cable (for power and programming)

## Getting Started

### Connecting the Serial Console Cable

The PICO-PI-8M-IMINI kits are supplied with a microUSB cable.  To interact with the main Cortex-A series processor console port, this cable must be connected to microUSB connector on the PICO-PI-8M baseboard.

:::warning Onboard USB-UART adapter IC power
On the PICO-PI-8M baseboard, the USB-UART bridge IS is powered by the board, not by the USB host interface.  When the board power is cycled, or if the SOM is reset, the USB serial port will disconnect and then reconnect.
:::

### Board Power

The PICO-PI-8M baseboard can be powered by the USB type C connector (5V).

### Install Demo Software Images

When your kit arrives, it will be preprogrammed with an image that will assist you in loading software onto the board for the first time. This image is known as the TechNexion Software Loader (or TSL).

The TSL connects the kit to an internet-connected network via the Ethernet port. It operates as a graphical user interface application and functions optimally when used with an attached display, such as the 5" MIPI-DSI touchpanel kit, which is available for purchase when you order the PICO-PI-IMX8M-MINI.

The TSL will guide you through a series of screens, enabling you to choose the type of image (Yocto, Debian, or Android) and the storage location (e.g. eMMC, SD card).

### Additional Demo Images

If you want to download additional demo images, you can easily do this by navigating your browser to our download server.  We usually update these 2-3 times a year as we release updated BSPs.

[Download Additional PICO-IMX8M-MINI Demo Images](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/DiskImage/)

### Flashing e.MMC

During development or initial manufacturing, you may want to flash the entire e.MMC.  Or, you way want to recover a 'bricked' unit (one that will not boot).  To do this, you can follow our step-by-step tutorial showing how to use the `uuu` tool here:

[Using 'uuu' to flash e.MMC](/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

If the board boots, and you have access to a serial console, you may want to use U-boot's `ums` command to load software to the e.MMC:

[Using U-boot's 'ums' command to flash e.MMC](/docs/embedded-software/linux/usage-guides/loading-software/using-u-boots-ums-command-to-write-flash-storage-over-usb-otg)

### Recovery to Factory Settings

It may be necessary or desireable to recover the unit to factory settings.  For example, you may want to use the Technexion Software Loader to download and install a different demo image to the board.

The process for doing this is identical to flashing a regular Linux image to your board, except that you would need to download the TechNexion Software Loader (TSL) image instead.

| Download Software Loader Image|
|---|
| [PICO-PI-IMX8M-MINI - MIPI display](https://download.technexion.com/factory_default_reset/pico-imx8mm/pi-dsi1280x720/rescue.xz) |

### Boot Media and Boot Mode Selection

There are two primary boot media selection options on the PICO-PI-IMX8M-MINI: Boot from e.MMC, or boot from the baseboard microSD card, supplied on the attached CLIX module.  Additionally, the module can be configured to boot into serial download mode, allowing images to be loaded and configuration of the module over USB. These settings are controlled using boot configuration jumpers.  See the table below.

:::info
Note the default boot media is EMMC. On the module, we configure boot configuration signals to boot from the default media without needing to be pulled high or low on the baseboard. You can either leave the jumpers as set here, or remove them.
:::

### boot from e.mmc
| mode | jumper configuration |
| --- | --- |
| boot from e.mmc | ![pico-imx8m-pi-emmc](//img/pico-imx8m-pi-emmc.png) |

### boot from sd card (on clix module)
| mode | jumper configuration |
| --- | --- |
| boot from sd card (on clix module) | ![pico-imx8m-pi_sd](//img/pico-imx8m-pi_sd.png) |

### boot into serial download mode
| mode | jumper configuration |
| --- | --- |
| serial download mode | ![pico-imx8m-pi-serial-download-mode](//img/pico-imx8m-pi-serial-download-mode.png) |


## Documentation

Refer to the SoM product page’s **Documentation** section for details on the development kit. It includes schematics for all baseboards and PCB files.  

[PICO-IMX8M-MINI - technexion.com](https://www.technexion.com/products/system-on-modules/pico/PICO-IMX8M-MINI/)

## Support

Having trouble?  Please check out our [support page](https://www.technexion.com/support/) for available support options.