---
title: PICO Evaluation Kit Boot Mode Settings
description: Add document description here
---
## Introduction
Below is a picture representation of the boot configuration jumper settings for our PICO baseboards. Configure the jumpers according to the boot selection you need.

### PICO-IMX6
|  | PI | NYMPH | DWARF* (see note) | HOBBIT* (see note) |
|---|---|---|---|---|
| EMMC (For modules with EMMC on the SOM) | ![pico-imx6-pi-emmc](//img/pico-imx6-pi-emmc.png) | ![pico-imx6-nymph-emmc](//img/pico-imx6-nymph-emmc.png) | ![pico-imx6-dwarf-emmc-e1541696045653](//img/pico-imx6-dwarf-emmc-e1541696045653.png) | ![pico-imx7-hobbit-emmc](//img/pico-imx7-hobbit-emmc.png) |
| Baseboard micro SD | Not applicable as PICO-PI does not have a microSD card slot | ![pico-imx7-nymph-sd-baseboard](//img/pico-imx7-nymph-sd-baseboard.png) | ![pico-imx6-dwarf-sd-module-e1541696091946](//img/pico-imx6-dwarf-sd-module-e1541696091946.png) | ![pico-imx7-hobbit-sd-module](//img/pico-imx7-hobbit-sd-module.png) |
| SOM micro SD (For modules with micro SD slots on the SOM) | ![pico-imx6-pi-sd-module](//img/pico-imx6-pi-sd-module.png) | ![pico-imx6-nymph-sd-module](//img/pico-imx6-nymph-sd-module.png) | ![pico-imx6-dwarf-sd-module-e1541696091946](//img/pico-imx6-dwarf-sd-module-e1541696091946.png) | ![pico-imx7-hobbit-sd-module](//img/pico-imx7-hobbit-sd-module.png) |
| Serial Download Mode | ![pico-imx7-pi-serial-download-mode](//img/pico-imx7-pi-serial-download-mode.png) | ![pico-imx6-nymph-serial-download-mode](//img/pico-imx6-nymph-serial-download-mode.png) | ![pico-imx6-dwarf-serial-download-mode-e1541696142551](//img/pico-imx6-dwarf-serial-download-mode-e1541696142551.png) | ![pico-imx7-hobbit-serial-download-mode](//img/pico-imx7-hobbit-serial-download-mode.png) |

### PICO-IMX7
|  | PI | NYMPH | DWARF* (see note) | HOBBIT* (see note) |
|---|---|---|---|---|
| EMMC (For modules with EMMC on the SOM) | ![pico-imx6-pi-emmc](//img/pico-imx6-pi-emmc.png) | ![pico-imx6-nymph-emmc](//img/pico-imx6-nymph-emmc.png) | ![pico-imx6-dwarf-emmc-e1541696045653](//img/pico-imx6-dwarf-emmc-e1541696045653.png) | ![pico-imx7-hobbit-emmc](//img/pico-imx7-hobbit-emmc.png) |
| Baseboard micro SD | Not applicable as PICO-PI does not have a microSD card slot | ![pico-imx7-nymph-sd-baseboard](//img/pico-imx7-nymph-sd-baseboard.png) | ![pico-imx6-dwarf-sd-module-e1541696091946](//img/pico-imx6-dwarf-sd-module-e1541696091946.png) | ![pico-imx7-hobbit-sd-module](//img/pico-imx7-hobbit-sd-module.png) |
| SOM micro SD (For modules with micro SD slots on the SOM) | ![pico-imx6-pi-sd-module](//img/pico-imx6-pi-sd-module.png) | ![pico-imx6-nymph-sd-module](//img/pico-imx6-nymph-sd-module.png) | ![pico-imx7-dwarf-sd-module](//img/pico-imx7-dwarf-sd-module.png)  | ![pico-imx7-hobbit-sd-module](//img/pico-imx7-hobbit-sd-module.png) |
| Serial Download Mode | ![pico-imx7-pi-serial-download-mode](//img/pico-imx7-pi-serial-download-mode.png) | ![pico-imx6-nymph-serial-download-mode](//img/pico-imx6-nymph-serial-download-mode.png) | ![pico-imx6-dwarf-serial-download-mode-e1541696142551](//img/pico-imx6-dwarf-serial-download-mode-e1541696142551.png) | ![pico-imx7-hobbit-serial-download-mode](//img/pico-imx7-hobbit-serial-download-mode.png) |

### PICO-IMX6UL/6ULL
Because the i.MX6UL/ULL SOCs have a maximum of 2 MMC/SDIO ports, booting from the baseboard microSD card slot is not possible as both ports are used on the SOM itself. One of them is used for the onboard EMMC, the other is used to communicate with the wifi module.
|  | PI | DWARF* (see note) | HOBBIT* (see note)|
|---|---|---|---|
| Serial Download Mode | ![PICO-IMX6UL-SERIAL-PI](//img/PICO-IMX6UL-SERIAL-PI.png) | ![PICO-IMX6UL-SERIAL-DWARF](//img/PICO-IMX6UL-SERIAL-DWARF.png) | ![PICO-IMX6UL-SERIAL-HOBBIT](//img/PICO-IMX6UL-SERIAL-HOBBIT.png) |

### PICO-IMX8M and PICO-IMX8M-MINI
|  | PI | WIZARD |
|---|---|---|
| EMMC | ![pico-imx8m-pi-emmc](//img/pico-imx8m-pi-emmc.png) | ![pico-imx8m-wizard_emmc](//img/pico-imx8m-wizard_emmc.png) |
| Baseboard microSD (Not applicable for PICO-IMX8M) | ![Pico-imx8m-pi_sd](//img/pico-imx8m-pi_sd.png)| ![pico-imx8m-wizard_sd](//img/pico-imx8m-wizard_sd.png) |
| Serial Download Mode | ![pico-imx8m-pi-serial-download-mode](//img/pico-imx8m-pi-serial-download-mode.png) | ![pico-imx8m-wizard_serial_download](//img/pico-imx8m-wizard_serial_download.png) |

:::warning
These boards are no longer sold in sample quantities. We include this information to support our customers that have development kits based on these boards.

It is still possible to purchase these boards, but minimum quantities apply. Please contact [TechNexion Sales ](http://mailto:<EMAIL>).
:::
