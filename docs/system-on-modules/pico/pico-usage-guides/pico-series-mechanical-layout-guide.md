---
title: Mechanical Layout Guide
description: Mechanical design of baseboards to accept PICO System on Modules
---

This article provides information relevant to mechanical design of baseboards to accept PICO System on Modules. PCB designers laying out a baseboard for PicoSOM can use the information here to be able to adequate place the components and mounting holes.

## PICO SoM Mechanical Drawing

Below is a mechanical drawing of PICO-IMX6-SD. This is the PICO SoM with the NXP i.MX6 SOC and a microSD card slot instead of an eMMC memory. This is useful because it shows the locations of the three board-to-board connectors in the context of the SoM. It also shows the location of the microSD card slot on the SoM itself.

![PICO-IMX6-SDCARD-mechanical-748x355.png](//img/PICO-IMX6-SDCARD-mechanical-748x355.png)

## Baseboard Drawing

In order to layout the SoM, one has to know the dimensions of the module, the locations and orientations, as well as pin 1 locations of the board-to-board connectors, as well as the locations and sizes of the mounting holes.In the drawing below, we show the layout of a baseboard with the locations of the connectors and mounting holes, with respect to the corner of the PICO SoM.The locations of pin 1 on each connector are indicated by a small triangle mark.For the mounting holes, we recommend a 2.0mm dia. plated hole with a 3.4mm dia. annular ring.

![PICO-IMX6-SD-baseboard-layout-768x626.jpg](//img/PICO-IMX6-SD-baseboard-layout-768x626.jpg)

If you are making use of the PICO SoM with built-in microSD card slots, you will need to set aside a removal area on your baseboard for card, which is why we have annoted this on the drawing. If you are using the version of the SoM with built-in eMMC, and never intend to use the microSD version, then you need not concern yourself with the microSD removal area.

## Board-to-Board Component Clearance

At TechNexion, we avoid placing components on their baseboard within the board outline of the PicoSOM. In order to avoid component interference and thus problems mating the modules. We design our baseboards to accept a range of Pico modules, each has a different bottom component layout.

## Baseboard Mating Connectors

![Hirose-df40c-connector.jpg](//img/Hirose-df40c-connector.jpg)

We use Hirose DF40 series connectors on the SoM. On our baseboards, the mating connector we use is DF40HC(3.0)-70DS-0.4V(51). This is a 3.0mm stacking height connector. Other stacking heights are available from Hirose, but we recommend the 3.0mm stacking height or greater in order to provide adequate component clearance between the SOM and the baseboard.\[HIROSE mating connectors\]For prototype quantities, we sell packages of 30 connectors including standoffs and nuts on our web shop [here (TechNexion part number: PICO-CONNECTOR-KIT).](https://www.technexion.com/shop/system-on-modules/accessories/pico-connector-kit) In production, Hirose connectors are available from their franchised distributors.
