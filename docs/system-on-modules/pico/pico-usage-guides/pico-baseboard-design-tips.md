---
title: Baseboard Design Tips
description: Add document description here
---

This article provides some tips on how to succesfully design a baseboard for PICO SoMs.

## Recommendation checklist
These are some points to consider before manufacturing a custom PICO baseboard:
* Debug console for the main processor cores
* Debug console for the on-die microctonroller (such as cortex M4), if needed/applicable
* Boot pins, if needed
* Validate voltage level on 5V rails
* Validate load on 3.3V rail
Further details and explanation follows.

### Debug consoles
The debug console allows a software engineer access to the board during board development.It strongly recommended that the initial board prototypes have some mechanism making the debug uart available. A pinheader or even pinholes with TX and RX pins suffices (flow control is optional).Without a debug console the hardware validation process is much more difficult without.Some TechNexion SoMs feature a microcontroller core. For instance the PICO-IMX7D and PICO-IMX8M contain a cortex M4 core. Development for the cortex M4 simplified if a debug console is made available for it as well.

### Boot pins
The PICO X2 connector has four pins set aside for providing boot mode function. Most PICO SoMs boot by default from their built-in storage, but for development purposes, it is often very useful to be able to be able to boot from different boot devices.Consult TechNexion baseboard schematics for the available strapping options.In the worst case, a board without an X2 connector can be installed using a TechNexion baseboard. Install the SoM on the TechNexion board, validate the onboard storage can boot and then move the PICO SoM from the TechNexion board to the custom baseboard.NB! Use the right pull-up level for your CPU module!NB2: On some products the boot pins double as LCD pins. On these products, the pull-up / down resistances might need some hand-tweaking.

## Electrical considerations
There are a few common pitfalls when designing the electrical part for the PICO product line. For instance: the Intel Edison baseboard has a slightly low SYS voltage (below 5V). For instance PICO-IMX7D can have problems booting when the 5V rail is notably less than 5V.
:::info
Make sure the SYS voltage is 5V
:::
Another quirk of the PICO-IMX7D PMIC is that unless there is some load on some lines, it will not power up. For this reason very basic baseboards can have problems booting the PICO-IMX7D.

:::info
Add a dummy load on the 3.3V (pins 8 and 10) of about 10mA.
:::