---
sidebar_position: 1
---

# System on Modules

TechNexion System-on-Modules built around NXP i.MX6 / i.MX7 / i.MX8M / i.MX9 families provide embedded system developers access to high-performance, industrial-grade building block to integrate in their end-product.

Proud to be an NXP Gold Partner, our products based on 32-bit and 64-bit ARM™ i.MX processors. These are powerful multimedia computing platforms that include the latest connectivity options such as USB 3.0, PCI-Express, Gigabit Ethernet, video encoding and decoding, high-definition audio interfaces, and high performance 3D graphics. Most products are available with custom-built pre-integrated wireless connectivity for demanding IoT applications. We accelerate your time to market with a open source Linux and Android operating system support.

## Our System on Module Families
- [EDM](/docs/system-on-modules/edm)
- [AXON](/docs/system-on-modules/axon)
- [PICO](/docs/system-on-modules/pico)
