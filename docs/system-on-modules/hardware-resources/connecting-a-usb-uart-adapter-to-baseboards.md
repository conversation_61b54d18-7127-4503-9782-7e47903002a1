---
title: Connecting a USB-UART Adapter to TechNexion Baseboards
---

## Introduction
This tutorial provides instructions for how to connect our USB-UART adapter cable (TechNexion Part Number: USBUART330TTLFTDI100) to certain baseboards (see applicable list below).

### Cable Part Number
| TechNexion FTDI-based USB Type A to UART (3.3V TTL) cable
|---
|Part Number (TechNexion Shop): [USBUART330TTLFTDI100](https://www.technexion.com/shop/system-on-modules/accessories/usbuart330ttlftdi100/)

### Applicable Baseboards
| Baseboard
|---
| PICO-NYMPH
| PICO-HOBBIT*
| PICO-DWARF*
| PICO-WIZARD
| WB-EDM-G (Wandboard EDM-G

:::(Warning) (Legacy Baseboards)
\* PICO-DWARF and PICO-HOBBIT have been deprecated and are no longer available for purchase as standalone products in sample quantities or as part of development kits. The information here is for completeness and to assist developers that may be working with those products. We can build them on a special order basis. Please email [<EMAIL>](mailto:<EMAIL>) for further information and to request a quote.
:::

The USB-UART cable is primarily used to provide a serial console for the purposes of programming, configuration, and debugging. It provides a simple connection to UART signal pins at 3.3V levels and makes the serial port available via a USB Type A connection to a host computer. The host computer provides power to the cable’s built-in USB-UART chip.

### Connecting the cable to the board
On one end of the cable is a pin header that is connects directly into the correct connector site of the baseboard using compression-fitting pins.

#### PICO-NYMPH
![nymph-usbserial-connected.jpg](//img/nymph-usbserial-connected.jpg)

#### PICO-DWARF*
![dwarf-usbserial-connected-748x409.jpg](//img/dwarf-usbserial-connected-748x409.jpg)

#### PICO-HOBBIT*
![hobbit-usbserial-connected.jpg](//img/hobbit-usbserial-connected.jpg)

#### PICO-WIZARD
On PICO-WIZARD, the main console port connecto reference designator is **M1**.
![pico-wizard-cable-m1.png](//img/pico-wizard-cable-m1.png)

#### WB-EDM-G (Wandboard EDM-G)
On Wandboard EDM-G, the main console port connector reference designator is **CONSOLE1**.
![wb-edm-g-cable-console1.png](//img/wb-edm-g-cable-console1.png)

### Cable Insertion Notes
Note the black wire (GND) connects to pin 1. Making sure that the board is powered off, insert the pins carefully to ensure that all of the pins seat into the holes, and press firmly to fully push the pins through the holes. This may take more force the first time. It will stay plugged in until forcibly removed.

After the cable is plugged into the board, connect the other end of the cable (the USB Type A connector) into a port on your host computer and use your preferred serial terminal emulator application such as screen, minicom, and PuTTY on Linux and MacOS, and Hyperterm or Teraterm on Windows.

### BAUD Rate
The default baud rate for the serial console on our products is 115200 baud, 8 bits, no parity, and 1 stop bit, with no hardware flow control (115200,8,N,1).

### Connector Schematic
The following picture is a schematic showing the connector pinout.  The important connections are GND (pin 1), UART transmit (pin 2), and UART receive (pin 4).
![DWARF-debug-connection-schematic.png](//img/DWARF-debug-connection-schematic.png)

### USB-UART Cable-to-Board Connector
USB-UART cable-to-board connector and signals.
| Pin | Signal | Color
|---|---|---
| 1 | GND | Black
| 2 | RX (Device --> Host) | Yellow
| 3 | 3.3V (not used) | *Empty*
| 4 | TX (Host --> Device) | Orange
| 5 | Not used | Brown
| 6 | Not used | Green