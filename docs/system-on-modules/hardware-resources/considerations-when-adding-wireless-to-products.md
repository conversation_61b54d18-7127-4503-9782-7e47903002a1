---
title: Considerations when Adding Wireless to Products
---

## Introduction
Adding wireless LAN functionality to your product provides some key capabilities, especially for Internet of Things applications. Wireless LAN provides a (relatively) short-distance, high-bandwidth data communications link between your device and the local network. All mobile devices such as tablets and smartphones are equipped with WLAN radios and operating systems such as Android and iOS can connect to the local network or to any access point (AP), and when combined with either a web browser or custom application, turns them into very handy device management platforms. However, adding wireless to your product also comes with some key considerations.

### Security
How can I keep this device from being tampered with and not a key fault injection point for the next ransomware attack?

### Operating Modes
What operating modes do I need to use?
- Do I need to connect to the device as an access point? How many devices do I need to connect?
- Do I need to connect this device to a local network (as a client or in WLAN terms - a "Station")
- Do I need to do both? For example, does the device need to have an AP active for provisioning purposes?

### Engineering Development Time
Engineering development time is also a major considering when developing a product with wireless functionality. You can separate this into two main areas: *Hardware Integration* and *Software Integration*.

#### Hardware Integration
When selection WLAN hardware for a design, there are two main options. Pluggable Modules (e.g. M.2 modules or mini PCIe modules) can be inserted into a standard socket on the PCB, and solderable SIP (System In Package) modules.
**Pluggable Modules Integration:** Integrating a pluggable module such as an M.2 or miniPCIe module is straight forward.
| **CLIX-9377**| **STIX-9377-M2** | **STIX-9377-MPCIE** |
|---|---|---|
|TechNexion CLIX form-factor, 25x20mm, 40-pos Hirose DF40 board to board connector | M.2 form-factor, Key B or Key A/E available | Mini PCIe form-factor |
| ![clix-9377-top-cropped.png](//img/clix-9377-top-cropped.png)| ![stix-9377-m2-b-top-cropped.png](//img/stix-9377-m2-b-top-cropped.png) | ![stix-9377-mpcie-top-cropped.png](//img/stix-9377-mpcie-top-cropped.png) |

**SIP Module Integration:** Short for System In Package, SIP module integration can be challenging as the footprint is dense (fine pitch). In addition, ground pad attachment underneath the module is very important for good RF performance and thermal conduction of heat away from the module. The PCB layout as well as the solder reflow profile can require a lot of tuning and iteration in order to attain acceptable results and minimize manufacturing fallout.

| **PIXI-9377-S, PIXI-9377-P** | ![pixi-9377-s-top-cropped.png](//img/pixi-9377-s-top-cropped.png)|
|---|---|
| Form-factor: | M.2 1216 (12mm X 16mm) form-factor SIP module |
| Interface: | SDIO/UART (PIXI-9377-S), PCIE (PIXI-9377-P)

TechNexion SOMs are pre-integrated with wireless modules. If you select a TechNexion SOM for your design, 95% of hardware integration is complete. The main question after this is antenna selection. Our modules have been tested and certified with two different types of antennas:

A **Dipole antenna** allows ease antenna integration in use cases involving metal enclosures. As conductive materials tend to shield radio frequency emissions, a dipole antenna is a viable option as it can be mounted easily outside of the enclosure.

We also test with a flex **PIFA** (Planar Inverted F Antenna) PCB antenna. This type of antenna can be used within an enclosure when the enclosure's RF permeability isn't an issue (e.g. plastic enclosures). This antenna has an omnidirectional beam pattern, but a lower overall gain than the dipole antenna.

The following images are examples of PCB (PIFA) and dipole antennas available from TechNexion:
| PCB (PIFA) Antenna (2.5dBi) | Dipole Antenna (7dBi) |
|---|---|
| Part Number: [ANTP150P232525D2450MHF4](https://www.technexion.com/shop/system-on-modules/accessories/antp150p232525d2450mhf4/) | Part Number: [ANTP180A207070D2450MHF4](https://www.technexion.com/shop/system-on-modules/accessories/antp180a207070d2450mhf4/)| 
|  ![antp150p232525d2450mhf4-cropped.png](//img/antp150p232525d2450mhf4-cropped.png) | ![antp180a207070d2450mhf4-cropped.png](//img/antp180a207070d2450mhf4-cropped.png)|

#### Software Integration
When selecting a wireless module, software integration is not to be neglected. Every wireless module usually requires a specific driver, firmware (which is downloaded to the module on boot), and multiple configuration files. In some cases these files are in the public domain, but in many cases these files are specific to the module vendor and are proprietary.

We have performed all of the required driver integration, as well as firmware integration, for Linux and Android Operating Systems (OS). Also validated are three different modes of operation:
- Client, or Station (STA) mode.
- Access Point (or AP) mode.
- Concurrent mode, where the system acts as both an access point and a station
Here are some tutorials and videos to get you started working with our modules in Client (Station), Access Point, and Concurrent Modes:
- [Testing Wireless LAN (WiFi)](testing-wireless-lan-wifi)
