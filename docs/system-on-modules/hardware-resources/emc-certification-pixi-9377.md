---
title: EMC Compliance Certification
---
## 	EMC Compliance certification
The PIXI-9377 module has been tested and approved as a Modular Radio in accordance with the appropriate FCC, IC, ETSI, RED, TELEC and RCM standards. If you need supporting test data and test report, please contact your TechNexion Account Manager, distributor, or contact us directly at [<EMAIL>](mailto://<EMAIL>).

Since this module and its associated set of approved antennas have been certified as a Modular Radio, this allows the end user to integrate this module into an end-product without the requirement of recertifying the radio module. The module integrator is responsible for the unintentional conducted and radiated emissions and must verify that the integrated product is compliant with the rules associated with unintentional radiators. The module integrator is also required to maintain an engineering record of the verification testing and declare on the product through proper labeling and marking that the device is compliant with these particular rules.

The installed module’s FCC ID (US), IC (Canada), and TELEC (Japan) numbers need to be clearly marked on the product with the following verbiage:
- “Contains FCC ID: 2AKZA-QCA9377”
- "Contains IC: 22364-QCA9377"
- "Contains TELEC: 201-180629"

In addition, the modules have been tested to be RCM and ETSI compliant and to the following CE standards:

* EN 300 328 v2.2.2
* EN 301 893 v2.1.1
* EN 55032 / EN 55024

### FCC Testing Requirements for End-Product
Once the module is integrated and the end-product is realized, the end-product must be tested and follow the verification process for Unintentional Conducted and Radiated Emissions in accordance to the FCC and IC guidelines. The module needs to be powered and placed in the receive mode for this test. The receiver must be tuned to its lowest frequency channel, mid-frequency channel, and highest frequency channel. The supporting test data does not need to be submitted to the FCC or IC. The implementation of the module in a specific end-product should also be reviewed to ensure compliance with the FCC and IC requirements for SAR and MPE if applicable.

### FCC Wireless certification: New Filing vs. Permissive Change
Products are continually under revision due to obsolete or unavailable parts, cost cutting, updates for a product release, and so on. The dilemma for most companies is determining the process and requirements for altered products. This section outlines the options available and highlights a few examples for guidance.

Here we focus on changes for unlicensed transmitters that require an FCC Certification or Equipment Authorization. Products require “Document of Compliance” testing if the change could adversely affect the radiating characteristics of the equipment. This procedure is outlined in Title 47 Part 2.1073.

To allow products to be modified without requiring a new filing, the FCC has defined three Permissive Change options listed in Title 47 Part 2.1043.

#### Class I Permissive Change
A Class I Permissive Change includes modifications which do not degrade the characteristics reported by the manufacturer and accepted by the Commission when certification is granted. No filing with the Commission is required for a Class I Permissive Change. [FCC source: 2009 Title 47 CFR 2.1043 (b)(1)](https://docs.fcc.gov/public/attachments/FCC-15-92A1.pdf)

#### Class II Permissive Change
A Class II Permissive Change includes modifications which degrade the performance characteristics as reported to the Commission at initial certification. Such degraded performance must still meet the minimum requirements of the applicable rules. When a Class II permissive change is made, the grantee must supply the Commission with complete information and results of tests of characteristics affected by such change. Modified equipment cannot be marketed under the existing grant of certification prior to acknowledgment by the Commission that the change is acceptable.
 [FCC source: 2009 Title 47 CFR 2.1043 (b)(2)](https://docs.fcc.gov/public/attachments/FCC-15-92A1.pdf)

#### Class III Permissive Change
A Class III Permissive Change includes software modifications of a software-defined radio transmitter that change the frequency range, modulation type or maximum output power (either radiated or conducted) outside the parameters previously approved, or that change the circumstances under which the transmitter operates in accordance with Commission rules.

When a Class III Permissive Change is made, the grantee must supply the Commission with a description of the changes and test results showing that the equipment complies with applicable rules with the new software loaded, including compliance with applicable RF exposure requirements. The modified software must not be loaded into the equipment, and the equipment must not be marketed with modified software under the existing grant of certification, prior to acknowledgment by the Commission that the change is acceptable.

Class III changes are permitted only for equipment on which no Class II changes have been made from the originally approved device. [FCC source: 2009 Title 47 CFR 2.1043 (b)(3)](https://docs.fcc.gov/public/attachments/FCC-15-92A1.pdf)

### Examples of Common Changes
Following are examples of three common changes to certified radios:

#### Example 1: Change of Antenna for a Part 15 Subpart C Unlicensed Radio
Both products and modules are tested and certified with specific antennas. In many cases the end user deviates from the specified antenna due to cost, style or availability. To maintain a Class I Permissive Change, the new antenna must be an equivalent antenna, defined as one of the same type (e.g., PIFA or dipole) and must be of equal or less gain than an antenna previously authorized under the same FCC ID, and must have similar in-band and out-of-band characteristics (consult specification sheet for cutoff frequencies). [FCC Source: 178919 D01 Permissive Change Policy](https://apps.fcc.gov/kdb/GetAttachment.html?id=N0FeGuIZalHwpzYoaFJpjA%3D%3D&desc=178919%20D01%20Permissive%20Change%20Policy%20v06&tracking_number=33013)

Therefore, if the antenna is of a different type or higher gain, the product or radio module requires a Class II Permissive Change, and all FCC provisions of Title 47 CFR 15.203 for antenna requirements must be met.

#### Example 2: Electrical Hardware Changes
The main deciding factor for hardware changes is whether the device is “electrically equivalent.” If the device is electrically equivalent, a Class I or Class II Permissive Change is acceptable, depending on the test result evaluation. Again, it is the manufacturer’s responsibility to ensure that the devices are electrically equivalent and to perform analysis that the device’s performance has not degraded. Some form of testing is generally required to support the claim that the device’s performance has not degraded.

Changes in frequency or output power, or removal or addition of components related to the RF section of the device automatically require a new filing and new FCC ID number.

#### Example 3: Enclosure Change
Obviously, enclosure changes do not apply to modular radios, but for other products, slight changes are allowed to the enclosure under a permissive change. However, major reorientation of the device inside the enclosure is not acceptable. For example, if the device was originally tested and certified lying flat and the new enclosure has the device mounted vertically, this would require retesting and a new FCC filing.

### Summary
Changes to a modular radio or product will result in either a Permissive Change or a new FCC filing and ID number. The degree of change is the determining factor for both the process and the amount of supporting data required to illustrate compliance. TechNexion takes pride in helping customers evaluate and develop the most beneficial test plan to properly address these changes.