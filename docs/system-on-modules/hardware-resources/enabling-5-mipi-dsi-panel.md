---
title: Enabling 5" MIPI-DSI Panel on PICO Development Kits
---
## Introduction
This article shows you how to enable the 5" MIPI-DSI display panel on certain i.MX8M Mini Development kits.

This is really just a matter of selecting the correct device tree blob (.dtb) file to be loaded by U-boot during the boot-up sequence.

This is done in U-boot. In order to do this, reboot the board with a serial console attached, then halt the boot process using any key. The following instructions assume that you are doing this on the U-boot command line.

### PICO-IMX8M-MINI:
On PICO-PI:
```bash
setenv fdt_file imx8mm-pico-pi-ili9881c.dtb
saveenv
```
