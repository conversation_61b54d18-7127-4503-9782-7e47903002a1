---
sidebar_position: 1
---

# Embedded Software

Embedded software refers to specialized computer programs designed to control dedicated hardware systems with specific functions. Unlike general-purpose software that runs on computers or mobile devices, embedded software is tightly integrated with the hardware it controls, operating within strict constraints of memory, processing power, and real-time requirements.

Embedded systems are found everywhere in modern life, from automotive control units and medical devices to IoT sensors and industrial automation equipment. The software that powers these systems must be highly reliable, efficient, and often operate continuously for years without interruption.

Key characteristics of embedded software include:
- **Resource constraints**: Limited memory, storage, and processing power
- **Real-time requirements**: Deterministic response times for critical operations
- **Hardware integration**: Direct interaction with sensors, actuators, and specialized peripherals
- **Reliability**: High availability and fault tolerance requirements
- **Power efficiency**: Optimized for battery-powered and low-power applications
- **Security**: Protection against unauthorized access and tampering

Common embedded software platforms covered in this documentation include Linux-based systems for more complex applications, Android for consumer devices, and Real-Time Operating Systems (RTOS) for time-critical applications.

## Getting Started with Embedded Software

### Linux Embedded Development

**Yocto**
- [Linux Kernel Development Guide](/docs/category/kernel-and-bootloader) - Building and customizing Linux kernels for embedded systems
- [Cross-Compilation Setup](/docs/embedded-software/linux/usage-guides/kernel-bootloader/preparing-a-toolchain-for-building-arm-binaries-on-x86-hosts) - Toolchain configuration for target architectures
- [Device Tree Configuration](/docs/embedded-software/linux/usage-guides/kernel-bootloader/device-tree-blob-overlaydtbo-table) - Hardware description and driver binding
- [Yocto Project Guide](/docs/embedded-software/linux/yocto/) - Advanced embedded Linux build system
- [General Linux Usage Guides](/docs/category/general-linux-usage-guides) - Covering the basic skills of the TN development board

**Debian**
- [Debian Features](/docs/embedded-software/linux/debian/usages-guides/debian-12-images-for-technexion-boards) - Debian 12 features with TechNexion flexbuild

**Android**
- [Android Project Guide](/docs/embedded-software/android/) - Android build steps

**FreeRTOS**
- [FreeRTOS Porting Guide](/docs/embedded-software/rtos/freertos/getting-started/freertos-porting-guide-for-imx93) - Quick verification to FreeRTOS

### Release Notes & Updates

- [Release Notes] - Latest version information and changes
- [Migration Guides] - Upgrading between major versions
- [Known Issues] - Limitations and workarounds
