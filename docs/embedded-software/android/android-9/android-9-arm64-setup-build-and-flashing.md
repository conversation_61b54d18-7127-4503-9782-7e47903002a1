---
title:  Setup, Build, and Flashing for ARM64
description: Android 9 (Pie) Setup, Build, and Flashing Instructions for ARM64 Systems
sidebar_position: 0
---
### Download The Source Code

Github way (Prepare repo command first is recommended)

Install repo first:

How to download repo manually:
```shell
$ curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
$ chmod a+x repo
$ sudo mv repo /usr/bin/repo
```

Download latest stable release (LTS branch):

    Latest update (20201006):
    1. support ubuntu 20.04 host compile environment
    2. add PICO-WIZARD baseboard to PICO-IMX8M and PICO-IMX8MM SOM list (20200826)
    3. [EDM-IMX8M] enable g080uan01 and g101uan02 panels support (20200405)
    4. [PICO-IMX8MM] enable NFC support (20200330)
    5. [PICO-IMX8M] tweak GPU performance for 1GiB memory (20200225)
    6. support 44.1KHz sample rate on HDMI audio interface (20200224)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-p9.0.0_2.0.1_8m-ga
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```
<br/>

Donload earlier release: TN2.0 (20191216):

    Changelog:
    1. GPU performance improvement
    2. Add LIBGPIOD JNI app
    3. Add openssh/ethtool libraries

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-p9.0.0_2.0.1_8m-ga_tn2.0
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```
<br/>

Download earlier release: TN1.0 (20191020):

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-p9.0.0_2.0.1_8m-ga_tn1.0
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```


### Compiling Environment Setup

There are two different methods you can use to set up the build environment. One is to install the required packages onto your host filesystem.<br/>
Another is to use a docker container, where the installation of the required packages is automated for you.

General Packages Installation ( Ubuntu 16.04 or above)

```shell
$ sudo apt-get install uuid uuid-dev zlib1g-dev liblz-dev liblzo2-2 liblzo2-dev lzop \
git-core curl u-boot-tools mtd-utils android-tools-fsutils device-tree-compiler gdisk \
gnupg flex bison gperf build-essential zip curl zlib1g-dev gcc-multilib g++-multilib \
libc6-dev-i386 lib32ncurses5-dev x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev \
libxml2-utils xsltproc unzip sshpass ssh-askpass zip xz-utils kpartx vim screen sudo wget \
bc locales openjdk-8-jdk rsync docker.io python3 kmod cgpt bsdmainutils lzip hdparm
```

Or adapt Docker Ubuntu 16.04 Container based compile environment (Optional)

```shell
$ cd cookers
$ docker build -t build_droid9 .
$ sudo docker run --privileged=true --name mx8_build  -v /home/<USER>/<source folder>:/home/<USER>
(first time)

$ sudo docker ps -a
$ sudo docker start <your container id>
$ sudo docker exec -it mx8_build bash
(after first time)
```

### Starting Compile the Source Code

* Source the compile relative commands:

    **For PICO-IMX8M + PICO-PI-IMX8M HDMI (1080p)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m.pi.hdmi
    ```
    <br/>

    **For PICO-IMX8M + PICO-PI-IMX8M 5-inch LCD (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m.pi.mipi-dsi_ili9881c
    ```
    <br/>

    **For PICO-IMX8M + PICO-PI-IMX8M HDMI with Voice-HAT**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m.pi.hdmi-voicehat
    ```
    <br/>

    **For PICO-IMX8M-Mini + PICO-PI-IMX8M 5-inch LCD (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m-mini.pi.mipi-dsi_ili9881c
    ```
    <br/>

    **For PICO-IMX8M-Mini + PICO-PI-IMX8M 5-inch LCD with Voice-HAT (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m-mini.pi.mipi-dsi_ili9881c-voicehat
    ```
    <br/>

    **For FLEX-IMX8M-Mini + PICO-PI-IMX8M 5-inch LCD (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.flex-imx8m-mini.pi.mipi-dsi_ili9881c
    ```
    <br/>

    **For FLEX-IMX8M-Mini + PICO-PI-IMX8M 5-inch LCD with Voice-HAT (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.flex-imx8m-mini.pi.mipi-dsi_ili9881c-voicehat
    ```
    <br/>

    **For EDM-IMX8M + EDM-WIZARD HDMI (1080p)**
    ```shell
    $ source cookers/env.bash.imx8.edm-imx8m.wizard.hdmi
    ```
    <br/>

    **For EDM-IMX8M + EDM-WIZARD HDMI with wm8960 audio codec (1080p)**
    ```shell
    $ source cookers/env.bash.imx8.edm-imx8m.wizard.hdmi-wm8960
    ```
    <br/>

    **For EDM-IMX8M + EDM-WIZARD HDMI with Voice-HAT (1080p)**
    ```shell
    $ source cookers/env.bash.imx8.edm-imx8m.wizard.hdmi-voicehat
    ```
    <br/>

    **For EDM-IMX8M + EDM-WIZARD 5-inch LCD (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.edm-imx8m.wizard.mipi-dsi_ili9881c
    ```
    <br/>

    **For EDM-IMX8M + EDM-WIZARD 5-inch LCD with Voice-HAT (ili9881c 720p via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.edm-imx8m.wizard.mipi-dsi_ili9881c-voicehat
    ```
    <br/>

    **For EDM-IMX8M + EDM-WIZARD Dual HDMI (1080p for primary screen and 720p for secondary screen)**
    ```shell
    $ source cookers/env.bash.imx8.edm-imx8m.wizard.dual-hdmi
    ```
    <br/>

    **For PICO-IMX8M-Mini + PICO-WIZARD 5-inch LCD (ili9881c 720p via MIPI-DSI interface) (LTS branch only)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m-mini.wizard.mipi-dsi_ili9881c
    ```
    <br/>

    **For PICO-IMX8M-Mini + PICO-WIZARD 10-inch LCD (g101uan02 1920x1200 via MIPI-DSI interface) (LTS branch only)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m-mini.wizard.mipi-dsi_g101uan02
    ```
    <br/>

    **For PICO-IMX8M + PICO-WIZARD HDMI (1080p) (LTS branch only)**
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m.wizard.hdmi
    ```
    <br/>

* Get the NXP restricted extra packages (recommended):
    ```shell
    $ merge_restricted_extras
    (sometimes could be stocking on the waiting github response, please try again)
    ```

* For a full clean build:
    ```shell
    $ cook -j<N> (N is up to cors numbers on your host PC)
    ```

* For an incremental build:
    ```shell
    $ heat -j<N> (N is up to cors numbers on your host PC)
    ```

* For clean the all build files:
    ```shell
    $ throw
    ```

* To Configuration in Linux Kernel part:
    ```shell
    $ cd vendor/nxp-opensource/kernel_imx/
    $ recipe (or make menuconfig)
    ```

***
### Flashing The Output Images

Output relative image files of path:

```shell
$ ls <source>/out/target/product/<target board>/ (pico-imx8m or others)
```

Download official uuu tool first:
* [NXP uuu release](https://github.com/NXPmicro/mfgtools/releases)

Then install uuu to different environment:

* [Refer Q&A item 3 of Chapter 5 on User Manual](https://github.com/technexion-android/Documents/blob/android-9/pdf/Android-Pie_User-Manual_20191220.pdf)

Quick way for flashing to board (use uuu tool):

```shell
$ uuu_flashcard x (x is up to your eMMC size, 8GB: x=7, 16GB: x=13, 32GB: x=28)
```

Standard way using uuu based script:

Issue command to generate a mass production folder:

```shell
$ gen_mp_images
```

then issue uuu command to flash the images to eMMC

```shell
$ cd <source>/auto_test/
$ Ubuntu host: sudo ./uuu_imx_android_flash.sh -c <partition table size> -f <cpu type> -e -D .
$ Windows host: uuu_imx_android_flash.bat -c <partition table size> -f <cpu type> -e -D .
(cpu type is imx8mq, imx8mm, etc.)
(partition table size is up to the eMMC size of target board: 8GB: x=7, 16GB: x=13, 32GB: x=28)
```

About Technexion uuu Detail:
* [Using 'uuu' to flash e.MMC](/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

Firstly, the user should change the boot mode to serial download mode and connect a OTG cable from board to host PC. Then, running the uuu commands as above post. In the end, change back the boot mode to eMMC boot mode, that's it.

***
### Enabling WiFi/BT function

**Prepare WiFi/BT firmware:**

This SDK is supporting Qualcomm(QCA) WLAN module - QCA9377 as default configuration, Because of the license restriction, please contact TechNexion FAE or Sales to get licensed firmware files, default is disabled.

    Contact Window: <EMAIL>

After getting the firmware binary: .. Decompress the tarball and put all the firmware files into

```shell
<source folder>/device/fsl/pico_8m/wifi-firmware/
```

Then take the QCA9377 folder as target path such as:

```shell
<source folder>/device/fsl/pico_8m/wifi-firmware/QCA9377
```

Issue the command cook/heat again as previous Chapter "[Compiling Environment Setup](#compiling-environment-setup)", WiFi/BT function will be working! Enjoy!