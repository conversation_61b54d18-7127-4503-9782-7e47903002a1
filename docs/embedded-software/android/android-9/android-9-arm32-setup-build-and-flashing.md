---
title: Setup, Build, and Flashing for ARM32
description: Android 9 (Pie) Setup, Build, and Flashing Instructions for ARM32 Systems
sidebar_position: 1
---
### Download The Source Code

Github way (Prepare repo command first is recommended)

Install repo first:

How to download repo manually:
```shell
$ curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
$ chmod a+x repo
$ sudo mv repo /usr/bin/repo
```

Download latest stable release (LTS branch):

    Latest update (20201006):
    1. support ubuntu 20.04 host compile environment
    2. enalbe screen rotation function (20200916)
    3. enable WiFi P2P HAL layer support (20200826)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-p9.0.0_2.2.0-ga
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```
<br/>

Download earlier release: TN2.0 (20191216)

    Changelog:
    1. Add new platforms: EDM-IMX6, TC series, TEP series
    2. Add LIBGPIOD JNI app
    3. Tweak Bluetooth HAL driver
    4. Add Bluetooth A2DP source function
    5. Add M.2 PIXI-9377-P support to TEP-IMX7

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-p9.0.0_2.2.0-ga_tn2.0
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```
<br/>

Download earlier release: TN1.0 (20191020)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-p9.0.0_2.2.0-ga_tn1.0
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```

### Compiling Environment Setup

There are two different methods you can use to set up the build environment. One is to install the required packages onto your host filesystem.<br/>
Another is to use a docker container, where the installation of the required packages is automated for you.

General Packages Installation ( Ubuntu 16.04 or Ubuntu 18.04)

```shell
$ sudo apt-get install uuid uuid-dev zlib1g-dev liblz-dev liblzo2-2 liblzo2-dev lzop \
git-core curl u-boot-tools mtd-utils android-tools-fsutils device-tree-compiler gdisk \
gnupg flex bison gperf build-essential zip curl zlib1g-dev gcc-multilib g++-multilib \
libc6-dev-i386 lib32ncurses5-dev x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev \
libxml2-utils xsltproc unzip sshpass ssh-askpass zip xz-utils kpartx vim screen sudo wget \
bc locales openjdk-8-jdk rsync docker.io python3 kmod cgpt bsdmainutils lzip hdparm
```

Or adapt Docker Container based compile environment (Optional)

```shell
$ cd cookers
$ docker build -t build_droid9 .
$ sudo docker run --privileged=true --name mx9_build  -v /home/<USER>/<source folder>:/home/<USER>
(first time)

$ sudo docker ps -a
$ sudo docker start <your container id>
$ sudo docker exec -it mx8_build bash
(after first time)
```

### Starting Compile The Source Code

* Source the compile relative commands

    **For PICO-IMX6Q with PI REV.B1: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6q.pico-imx6.pi.lcd-5-inch
    ```
    <br/>

    **For PICO-IMX6DL with PI REV.B1: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.pico-imx6.pi.lcd-5-inch
    ```
    <br/>

    **For PICO-IMX6Q with NYMPH: HDMI (720p)**
    ```shell
    $ source cookers/env.bash.imx6q.pico-imx6.nymph.hdmi
    ```
    <br/>

    **For PICO-IMX6DL with NYMPH: HDMI (720p)**
    ```shell
    $ source cookers/env.bash.imx6dl.pico-imx6.nymph.hdmi
    ```
    <br/>

    **For PICO-IMX6Q with NYMPH: VL070-10260NL-C01 7-inch LCD (1024x600 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6q.pico-imx6.nymph.lvds-7-inch
    ```
    <br/>

    **For PICO-IMX6DL with NYMPH: VL070-10260NL-C01 7-inch LCD (1024x600 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.pico-imx6.nymph.lvds-7-inch
    ```
    <br/>

    **For EDM-IMX6Q with FAIRY: HDMI (720p)**
    ```shell
    $ source cookers/env.bash.imx6q.edm1-imx6.fairy.hdmi
    ```
    <br/>

    **For EDM-IMX6DL with FAIRY: HDMI (720p)**
    ```shell
    $ source cookers/env.bash.imx6dl.edm1-imx6.fairy.hdmi
    ```
    <br/>

    **For EDM-IMX6Q with FAIRY: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6q.edm1-imx6.fairy.lcd-5-inch
    ```
    <br/>

    **For EDM-IMX6DL with FAIRY: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.edm1-imx6.fairy.lcd-5-inch
    ```
    <br/>

    **For TC0700 HMI: 7-inch LCD (1024x600 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.edm1-imx6.fairy.tc0700
    ```
    <br/>

    **For TC1000 HMI: 10-inch LCD (1280x800 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.edm1-imx6.fairy.tc1000
    ```
    <br/>

    **For TEP-1010 HMI: 10.1-inch LCD (1280x800 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.hmi.tep5-imx6.lvds-10-inch
    ```
    <br/>

    **For TEP-1560 HMI: 15.6-inch LCD (1366x768 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6dl.hmi.tep5-imx6.lvds-15-inch
    ```
    <br/>

    **For PICO-IMX7D with PI REV.B1: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx7d.pico-imx7.pi.lcd-5-inch
    ```
    <br/>

    **For PICO-IMX7D with PI REV.B1 + VOICEHAT: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx7d.pico-imx7.pi.lcd-5-inch-voicehat
    ```
    <br/>

    **For PICO-IMX7D with NYMPH: VGA (1024x768 resolution via VGA interface)**
    ```shell
    $ source cookers/env.bash.imx7d.pico-imx7.nymph.vga
    ```
    <br/>

    **For PICO-IMX7D with NYMPH: VGA (1024x768 resolution via VGA interface)**
    ```shell
    $ source cookers/env.bash.imx7d.pico-imx7.nymph.vga
    ```
    <br/>

    **For TEP1-IMX7 HMI REV.A2/D1: VL050-8048NT-C01 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx7d.hmi.tep1-imx7.lcd-5-inch
    ```
    <br/>


* Get the NXP restricted extra packages (recommended):
    ```shell
    $ merge_restricted_extras
    (sometimes could be stocking on the waiting github response, please try again)
    ```

* For a full clean build:
    ```shell
    $ cook -j<N> (N is up to cors numbers on your host PC)
    ```

* For an incremental build:
    ```shell
    $ heat -j<N> (N is up to cors numbers on your host PC)
    ```

* For clean the all build files:
    ```shell
    $ throw
    ```

* To Configuration in Linux Kernel part:
    ```shell
    $ cd vendor/nxp-opensource/kernel_imx/
    $ recipe (or make menuconfig)
    ```

***
### Flashing The Output Images

Output relative image files of path:

```shell
$ ls <source>/out/target/product/<target board>/ (pico-imx6q or others)
```

Quick way for flashing to board (legacy way, adapt mfgtool or ums command):

```shell
$ flashcard /dev/sdx y (x is your device node, y is up to your eMMC size, 4GB: y=3, 8GB: y=7, 16GB: y=14, 32GB: y=28)
```

About mfgtool detial (recommended):

Before flash your images to the eMMC, please change the boot mode to serial download mode first, then mount eMMC as a storage on host PC using mfgtool, every boot mode configuration of Technexion boards as following document:

* [mfgtool user guide](https://github.com/technexion-android/Documents/blob/android-9_imx6_imx7/pdf/PreBuilt_OS_Image_Installation_Guide_v3.5.pdf)

About ums detial:

Another modular way is use ums command on your currect u-boot inside the eMMC, connect the USB OTG cable to host PC, then ums will auto mounting a storage on host PC, ums command as following:

```shell
# ums 0 mmc 0 (mmc device number is up to your board, you can choose to 1 if 0 is not work)
```

Remember the ums is no need change the boot mode, but the disadvangage is the flash speed is slower than mfgtool way, so we recommend the mfgtool is better.


About uuu detail (modular way):

* [Generic instructions](https://github.com/TechNexion/u-boot-tn-imx/wiki/Use-mfgtool-%22uuu%22-to-flash-eMMC)

* [Download link](https://download.technexion.com/development_resources/development_tools/installer/imx-mfg-uuu-tool_20200629.zip)
<br/>

Please issue command to generate a binary image:

```shell
$  gen_virtual_images
```

Then output a full image named 'test.img' after run this command, put this image to the root folder of uuu tool(download from above download link), then issue uuu commands:

Windows host:
```shell
$ E:\imx-mfg-uuu-tool>uuu\windows64\uuu.exe -b emmc_imx6_img imx6\imx6-SPL imx6\imx6-u-boot.img test.img
```

Linux host:
```shell 
$ sudo  sudo uuu/linux64/uuu -b emmc_imx6_img imx6/imx6-SPL imx6/imx6-u-boot.img test.img
```

It will start flash user's own custom build image, enjoy.
<br/>

About Mass Production images flashing (Linux Host PC only):

```shell
$ gen_mp_images
$ cd auto_test
$ sudo ./fsl-sdcard-partition.sh -f imx6dl -c 14 /dev/sdx (-f is cpu type, -c is eMMC size, 4GB=3, 8GB=7, 16GB=14, 32GB=28)
$ sudo ./gpt_partition_move -d /dev/sdx -s 4096
$ sudo dd if=u-boot-pico-imx6_android_spl_defconfig.SPL of=/dev/sdx bs=1k seek=1 conv=sync
```

IMX6 series
```shell
$ sudo dd if=u-boot-pico-imx6_android_spl_defconfig.img of=/dev/sdx bs=512 seek=92 oflag=dsync
```

IMX7 series
```shell
$ sudo dd if=u-boot-pico-imx7_android_spl_defconfig.img of=/dev/sdx bs=512 seek=120 oflag=dsync
```

***
### Enabling WiFi/BT function

**Prepare WiFi/BT firmware**

This SDK is supporting Qualcomm(QCA) WLAN module - QCA9377 as default configuration, Because of the license restriction, please contact TechNexion FAE or Sales to get licensed firmware files, default is disabled.

    Contact Window: <EMAIL>

After getting the firmware binary: .. Decompress the tarball and put all the firmware files into 

```shell
<source folder>/device/fsl/imx8m/pico_imx8m/wifi-firmware/
```

Then take the QCA9377 folder as target path such as:

```shell
<source folder>/device/fsl/imx8m/pico_imx8m/wifi-firmware/QCA9377
```

Issue the command cook/heat again as previous Chapter "[Compiling Environment Setup](#compiling-environment-setup)", WiFi/BT function will be working! Enjoy!
<br/>

**NOTE:** 11/26 Update: Add QCA9377-5 M.2 WiFi/BT supporting:

Please take the WiFi via mPCIE firmware files to

```shell
<source folder>/device/fsl/imx7d/tep1_imx7/qca9377-5_wifi_firmware/ath10k/QCA9377/hw1.0/
```

Please take the Bluetooth via USB firmware files to

```shell
<source folder>/device/fsl/imx7d/tep1_imx7/bluetooth/
```

Issue the command cook/heat again as previous Chapter "[Compiling Environment Setup](#compiling-environment-setup)", WiFi/BT function will be working! Enjoy!