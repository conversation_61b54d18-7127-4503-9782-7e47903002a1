---
title: Setup, Build, and Flashing
description: Android 13 Setup, Build, and Flashing
sidebar_position: 0
---

## Build environment setup
 
:::info
* 64-bit environment
* 400GB of free disk space (more is better)
* 64 GB of RAM (more is better)

Please see Android document '[Hardware requirements](https://source.android.com/docs/setup/start/requirements#hardware-requirements)'  for the detail.
:::
There are two different methods you can use to set up the build environment. One is to install the required packages onto your host filesystem. 

Another is to use a docker container, where the installation of the required packages is automated for you.

### Host Build
#### Required Packages Installation
* Tested with Ubuntu 20.04 and 22.04
* Update Ubuntu
    ```shell
    sudo apt update
    sudo apt full-upgrade
    sudo apt autoclean
    sudo apt autoremove
    ```
* Install required packages
    * Ubuntu 20.04
        ```shell
        sudo apt-get install tzdata git-core gnupg flex bison apt-utils build-essential zip curl \
        zlib1g-dev liblz-dev gcc-multilib g++-multilib libc6-dev-i386 libncurses5 lib32ncurses5-dev \
        x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev libxml2-utils xsltproc unzip \
        fontconfig uuid uuid-dev liblzo2-2 liblzo2-dev lzop u-boot-tools mtd-utils \
        android-sdk-libsparse-utils android-sdk-ext4-utils device-tree-compiler gdisk m4 make \
        libssl-dev libghc-gnutls-dev swig libdw-dev dwarves python bc cpio tar lz4 zstd rsync \
        ninja-build clang android-tools-adb gperf software-properties-common sshpass \
        ssh-askpass xz-utils kpartx vim screen sudo wget locales openjdk-8-jdk python3 kmod cgpt \
        bsdmainutils lzip hdparm cmake python3-protobuf
        ```
    * Ubuntu 22.04
        ```shell
        sudo apt-get install tzdata git gnupg flex bison apt-utils build-essential zip curl \
        zlib1g-dev liblz-dev gcc-multilib g++-multilib libc6-dev-i386 libncurses5 lib32ncurses-dev \
        x11proto-core-dev libx11-dev lib32z1-dev libgl1-mesa-dev libxml2-utils xsltproc unzip \
        fontconfig uuid uuid-dev liblzo2-2 liblzo2-dev lzop u-boot-tools mtd-utils \
        android-sdk-libsparse-utils device-tree-compiler gdisk m4 make libssl-dev swig libdw-dev \
        dwarves python3 python-is-python3 bc cpio tar lz4 zstd rsync ninja-build clang adb gperf \
        software-properties-common sshpass ssh-askpass xz-utils kpartx vim screen sudo wget locales \
        kmod cgpt bsdmainutils lzip hdparm cmake python3-protobuf
        ```


### > Install Repo Tool
Install repo
``` shell
curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
chmod a+x repo
sudo mv repo /usr/bin/repo
```
### > Download The Source Code

Initialize repo using the latest LTS release (LTS branch):
```shell
repo init -u https://github.com/technexion-android/manifest -b tn-android-13.0.0_1.2.0_8m-next -m tn-android-13.0.0_1.2.0.xml
```
Download the source code:
```shell
repo sync -j<N>
```
* \<N\> is up to cors numbers on your host PC

### > Docker Container Build (Optional)
#### Adapt docker dontainer based compile environment
* Install docker
    * Please follow the instruction of [Install Docker Engine on Ubuntu](https://docs.docker.com/engine/install/ubuntu/)
* Create docker image (Just first time) 
    ```shell
    cd cookers
    docker build -t build_droid13 .
    ```
* Create and run a new docker container
    ```shell
    sudo docker run --privileged=true --name mx8_build  -v <your source folder>:/home/<USER>
    ```
* Run a existed docker container    
    ```shell
    sudo docker container start mx8_build
    sudo docker container attach mx8_build
    ```
 * After entering the container, change directory to /home/<USER>
 <br/>
---

## Start Compiling The Source Code

### Setup build environment

#### EDM-G-IMX8MP + WANDBOARD (HDMI)
For example using display HDMI
```shell
source cookers/env.bash.imx8.edm-g-imx8mp.wandboard.hdmi
```

#### EDM-G-IMX8MP + WIZARD (HDMI)
For example using display HDMI
```shell
source cookers/env.bash.imx8.edm-g-imx8mp.wizard.hdmi
```

#### EDM-G-IMX8MM + WANDBOARD (LVDS)
For example using display LVDS panel VL10112880
```shell
source cookers/env.bash.imx8.edm-g-imx8mm.wandboard.lvds_vl10112880
```

#### EDM-G-IMX8MM + WIZARD (LVDS)
For example using display LVDS panel VL10112880
```shell
source cookers/env.bash.imx8.edm-g-imx8mp.wizard.lvds_vl10112880
```

### Get the NXP restricted extra packages
```shell
merge_restricted_extras
```
:::info
* sometimes could be stocking on the waiting github response, please try again.
* Note that it will showing up a EULA message before merge packages, please type '**yes**' to continue the process as follows:
   > Could you agree this EULA and keep install packages?(yes/no) yes
* **You only need to install it once, it will take a lot of time to install these packages**
:::

### Get the Technexion firmware for WiFi/Bluetooth functions (recommended)
 ```shell
get_tn_firmware
 ```
:::info (**NOTES**)
* sometimes could be stocking on the waiting git server response, please try again.
* It will install to directory vendor/technexion automatically.
:::

### Full build OS image
```shell
cook -j<N>
```
* \<N\> is up to cors numbers on your host PC

### Clean the all compiled files
```shell
throw
```

### Partial build
After modifying the source code, you can directly build U-Boot, kernel or Android.
<br/>
Before building
* Goto the top directory of your Android project
* Remember to setup the build environment (see '[Setup build environment](#setup-build-environment)')

#### U-Boot
```shell
./imx-make.sh bootloader -j<N>
```
* \<N\> is up to cors numbers on your host PC

#### Kernel
```shell
./imx-make.sh kernel -j<N>
```
* \<N\> is up to cors numbers on your host PC

#### Android
Usually build U-Boot and kernel at the same time
```shell
./imx-make.sh -j<N>
```
or 
```shell
cook -j<N>
```
* \<N\> is up to cors numbers on your host PC
<br/>
* If you want build single Android package, see the Android document '[Building the code](https://source.android.com/docs/setup/build/building#build-the-code)' for details
<br/>

---
## Flashing The Output Images

Output relative image files of path:
```shell
ls <source>/out/target/product/<target board>/
```
* \<source\>: top directory of your project
* \<target board\>: edm_g_imx8mp or others

### uuu way (recommended)
:::warning
**Android 13 need version 1.4.182 or above**
:::
1. TechNexion Android sources already include uuu in vendor/technexion/utils/mfgtools
2. You can download uuu tool from from TechNexion's [download site](https://download.technexion.com/development_resources/development_tools/installer/imx-mfg-uuu-tool.zip)
    * Linux
        * Confirm where the previous uuu was installed
            ```shell
            which uuu
            ```
            The result may be /usr/bin, /usr/local/bin or other
        * For example, the previous uuu is located in /usr/bin, move the uuu to /usr/bin/ and grant available permissions
            ```shell
            sudo cp imx-mfg-uuu-tool/uuu/linux64/uuu /usr/bin/
            sudo chmod a+x /usr/bin/uuu
            ```
            If located in /usr/local/bin, move the uuu to /usr/local/bin/ and grant available permissions
            ```shell
            sudo cp imx-mfg-uuu-tool/uuu/linux64/uuu /usr/local/bin/
            sudo chmod a+x /usr/local/bin/uuu
            ```
     * Windows
       * The prebuilt uuu is located in  imx-mfg-uuu-tool/uuu/windows64/uuu.exe
     * For usage, please refer to [Technexion mfgtools Wiki](https://github.com/TechNexion/imx-mfgtools-tn)

3. Quick way for flashing to board (adapt uuu based flash script):
    For Ubuntu host:
    ```shell
    cd <source>/out/target/product/<target board>/
    sudo ./uuu_imx_android_flash.sh -f <soc_name> -a -e -c <card_size> -D .
    ```
    Options:
    * `<soc_name>` is up to your SoC platform of device, such as imx8mp, imx8mm, imx8mq, imx8mn
    * `<card_size>` is up to your eMMC size
        * default support eMMC 16GB.
        * eMMC 32GB: card_size=28
    * If device changed to QSPI mode, add "-u fspi" args.
       ```shell
       sudo ./uuu_imx_android_flash.sh -f <soc_name> -a -e -c <card_size> -D . -u fspi
       ```
    <br/>
    
    Example for imx8mp with eMMC 32GB:
    * Linux 
        ```shell
        sudo ./uuu_imx_android_flash.sh -f imx8mp -a -e -c 28 -D .
        ```
    * Windows
        ```shell
        uuu_imx_android_flash.bat -f imx8mp -a -e -c 28
        ```
    <br/>
    Example for imx8mq with eMMC 16GB, no need -c paramater:
    * Linux 
        ```shell
        sudo ./uuu_imx_android_flash.sh -f imx8mq -a -e -D .
        ```
    * Windows
        ```shell
        uuu_imx_android_flash.bat -f imx8mq -a -e
        ```
    
:::info
1. Firstly, the user must be change the boot mode to serial download mode and connect a OTG cable from board to host PC. 
2. Then, running the uuu commands as above post. In the end, 
change back the boot mode to eMMC boot mode, that's it.

**Notes**<br/>
users need change to serial download mode if adapt uuu tool, and ums just keep eMMC boot mode is enough.
:::

<!-- :::(Internal) (Not Ready)
 ### big image way (easier but spend much time for image flashing)
 1. Source the compile relative commands first.
 2. issue command to auto generate a local image for flashing
     ```shell
     ./imx-sdcard-partition-gen_image.sh -f <soc_name>  -c <card_size> image_name
    ```
    Options:
    * <soc_name> is up to your SoC platform of device, such as imx8mp, imx8mm, imx8mq, imx8mn
     * <card_size> is up to your eMMC size
         * eMMC 32GB: card_size=28
     <br/>
     
     Example for imx8mp with eMMC 32GB:
     ```shell
     ./imx-sdcard-partition-gen_image.sh -f imx8mm -c 28 test.img
     ```
 3. You'll see a test.img, test.img.bmap and test.img.xz in \<source>/out/target/product/\<target board>/
 4. You can flash this image to eMMC using utilities like dd, bmaptool or balena etcher.
 ::: -->
### Generate mass production image folder
Another situation is when you need move your image flash tool to factory side for MP, you can do these steps:

1. issue command to generate a protable MP image folder
    ```shell
    gen_mp_images
    ```
    the output folder name is 'auto_test'
2. compress auto_test folder to zip file, then you can take it to factory.
    ```shell
    zip -r auto_test.zip auto_test/*
    ```
5. prepare computers for MP, install Ubuntu host PC with required packages as [Host Build](#host-build) chapter.
6. extract auto_test.zip
    ```shell
    unzip auto_test.zip
    ```
    then issue flash command as [uuu way](#uuu-way-recommended) commands to flash image to target boards.

<!-- :::(Internal) (Need to check if this method is available)
**Technexion also provide a quicker way for MP stage, welcome to send <NAME_EMAIL> if you have interest.**
::: -->