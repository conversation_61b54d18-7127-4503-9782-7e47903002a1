---
title: Multi-Display guide
description: Add document description here
sidebar_position: 2
---

## Supported product
- TEP-IMX8MP

## Precondition
1. Plug in secondary display on product
2. Set device tree overlay table on u-boot prompt
    We have auto panel detection on technexion's panel  ( LVDS and vizionpanel). 
    During u-boot, system will set device tree overlay setting automatically.
    Please make sure there are two panel on dtoverlay setting.
    Example:
    ```
    u-boot=> setenv dtoverlay lvds-vl156192108 vizionpanel-vl10112880
    ```
<br/>
<!--
Only visible to team accounts
- Enable marco to apply multi display patch
   src path: device/nxp/imx8m/\<product>/BoardConfig.mk
    ```makefile
    SOONG_CONFIG_IMXPLUGIN += TN_MD_TOUCH
    SOONG_CONFIG_IMXPLUGIN_TN_MD_TOUCH = true
    ```
-->
    
## Multi-Display application
:::info Multi Display mode
- Default is sync mode. All display viewer are sync with primary display.
- Switch to async mode by multi-display application.
:::

1. open MD Launcher application
   
   ![MD-Launcher.png](/img/MD-Launcher-app.png)

2. Select which display that you want to open other application
   ![MD-launcher2.png](/img/MD-Launcher-usage.png)

   Note: If didn't select any display, displays will keep sync mode.
   <br/>
   Example: play video on secondary display
   ![TEP-Multi-Display-demo.jpg](/img/TEP-Multi-Display.jpg)
    


## Async touch
Refer input-port-associations.xml to binding display and touch.<br/>
Example: device/nxp/imx8m/\<product>/input-port-associations.xml
```xml
<ports>
	<port display="0" input="usb-xhci-hcd.1.auto-1.3.1/input0" />
	<port display="1" input="3-002a/input0" />
   ...
</ports>
```
<br/>

## Set primary display
Default primary display is LVDS panel on TEP-IMX8MP product.
Specific your display connect type into <b>persist.vendor.hwc.device.primary</b> property.
```shell
setprop persist.vendor.hwc.device.primary LVDS
```
List connect type:
```shell
ls /sys/class/drm
```
