---
title: Custom Configuration
description: Android 13 custom configuration
sidebar_position: 1
---
## Device Tree Overlay Configuration

Technexion implement a device-tree overlay method using AVB 3.0.

### Overlay Table
<table border={0} overflow-x="auto" height={600} style={{ fontSize: 14 }}>
  <tbody >
    <tr>
      <td style={{ backgroundColor: "#D2D2D2" }} width={180}>
        <b>platform</b>
      </td>
      <td style={{ backgroundColor: "#E6E6E6" }}>
        <b>overlay function</b>
      </td>
      <td style={{ backgroundColor: "#C8C8C8" }}>
        <b>dt overlay</b>
      </td>
    </tr>
    {/* edm-g-8mp */}
    <tr>
      <td style={{ backgroundColor: "#E6E6E6" }} rowSpan={6}>
        EDM-G-IMX8M-PLUS
      </td>
      <td>10" LVDS panel<br />VL10112880</td>
      <td>lvds-vl10112880</td>
    </tr>
    <tr>
      <td>15.6" LVDS panel<br />VL15613676</td>
      <td>lvds-vl15613676</td>
    </tr>
    <tr>
      <td>21" LVDS panel<br />Vl215192108</td>
      <td>lvds-vl215192108</td>
    </tr>
    <tr>
      <td>TEVI-OV5640</td>
      <td>tevi-ov5640</td>
    </tr>
    <tr>
      <td>TEVI-AR series</td>
      <td>tevi-ap1302</td>
    </tr>
    <tr>
      <td>VIZIONLINK with AR series</td>
      <td>vizionlink-tevi-ap1302</td>
    </tr>
    {/* edm-g-8mm */}
    <tr>
      <td style={{ backgroundColor: "#C8C8C8" }} rowSpan={6}>
        EDM-G-IMX8M-MINI
      </td>
      <td>10" LVDS panel<br />VL10112880</td>
      <td>sn65dsi84-vl10112880</td>
    </tr>
    <tr>
      <td>15.6" LVDS panel<br />VL15613676</td>
      <td>sn65dsi84-vl15613676</td>
    </tr>
    <tr>
      <td>21" LVDS panel<br />Vl215192108</td>
      <td>sn65dsi84-vl215192108</td>
    </tr>
    <tr>
      <td>TEVI-OV5640</td>
      <td>tevi-ov5640</td>
    </tr>
    <tr>
      <td>TEVI-AR series</td>
      <td>tevi-ap1302</td>
    </tr>
    <tr>
      <td>VIZIONLINK with AR series</td>
      <td>vizionlink-tevi-ap1302</td>
    </tr>
  </tbody>
</table>

### Example
#### Runtime stage
Please boot up into u-boot prompt, and issue commands as follows example:

* Overlay vl10112880 lvds panel
    * EDM-G-IMX8M-PLUS
        ```
        u-boot=> setenv dtoverlay lvds-vl10112880
        ```
    * EDM-G-IMX8M-MINI
        ```
        u-boot=> setenv dtoverlay sn65dsi84-vl10112880
        ```
* Overlay vl215192108 lvds panel
    * EDM-G-IMX8M-PLUS
        ```
        u-boot=> setenv dtoverlay lvds-vl215192108
        ```
    * EDM-G-IMX8M-MINI
        ```
        u-boot=> setenv dtoverlay sn65dsi84-vl215192108
        ```
* Overlay tevi-ov5640
    * Setup dt overlay
        ```
        u-boot=> setenv dtoverlay tevi-ov5640
        ```
* Overlay tevi-ap1302
    * Setup dt overlay
        ```
        u-boot=> setenv dtoverlay tevi-ap1302
        ```
    * **NOTE!**<br/>
      For TEVI-AR serials camera, in addition to dt overlay, also need to set the camera configuration.<br/>
      Such as TEVI-AR0144
        * Set camera configuration by each camera module
           add **androidboot.camera.layout=ar0144** to bootargs
            ```
            u-boot=> env edit bootargs
            edit: stack_depot_disable=on kasan.stacktrace=off console=ttymxc1,115200 earlycon=ec_imx6q,0x30890000,115200 init=/init firmware_class.path=/vendor/firmware loop.max_part=7 transparent_hugepage=never swiotlb=65536 pci=nomsi cma=800M@0x400M-0x1000M buildvariant=userdebug androidboot.hwrotation=0 androidboot.usb.debugging=1 bootconfig androidboot.camera.layout=ar0144
            ```
        * Camera module and configuration mapping table
            | Camera Module | Camera Configuration |
            | :---: | :---: |
            | TEVI-AR0144 | ar0144 |
            | TEVI-AR0234 | ar0234 |
            | TEVI-AR0521 | ar0521 |
            | TEVI-AR0522 | ar0522 |
            | TEVI-AR0821 | ar0821 |
            | TEVI-AR0822 | ar0822 |
            | TEVI-AR1335 | ar1335 |

* Save your overlay configuration for every boot later.
If you just want to boot current setting once, you can ignore this step.
    ```
    u-boot=> saveenv
    ```
* Boot into Android system
    ```
    u-boot=> boot
    ```
#### Compile stage
You also can fixed your overlay setting in u-boot header file according your target board
For example, to enable TEVI-AR0144 of EDM-G-IMX8M-PLUS, add overlay and camera configuration to vendor/nxp-opensource/uboot-imx/include/configs/edm-g-imx8mp_android.h
```c
#define CONFIG_EXTRA_ENV_SETTINGS	\
    ......
	"dtoverlay=tevi-ap1302\0"\
	"bootargs="\
    ......
	"androidboot.camera.layout=ar0144 "\
	"bootconfig "\
	"\0"
```
* **Note the setting in bootargs without '\0'**

---

## Change Boot Arguments
According to your device, you might need to change properties let your system performance better, list common properties as follows:

About CMA size, it's up to your DRAM size for each platforms, please modify CMA size as follows table:
<table border={0} overflow-x="auto" height={600} align="left" style={{ fontSize: 14, verticalAlign: "center" }}>
  <tbody>
    <tr>
      <td style={{ backgroundColor: "#D2D2D2" }}  width={180}>
        <b>platform</b>
      </td>
      <td style={{ backgroundColor: "#E6E6E6" }}  width={150}>
        <b>DRAM size</b>
      </td>
      <td style={{ backgroundColor: "#C8C8C8" }} >
        <b>recommend properties</b>
      </td>
      <td style={{ backgroundColor: "#E6E6E6" }} >
        <b>default properties</b>
      </td>
    </tr>
    {/* edm-g-8mp */}
    <tr>
      <td style={{ backgroundColor: "#E6E6E6" }} rowSpan={2}>
        EDM-G-IMX8M-PLUS
      </td>
      <td>1GB</td>
      <td>
        cma=320M@0x400M-0xb80M galcore.contiguousSize=33554432
        androidboot.displaymode=720p
      </td>
      <td rowSpan={2}>
        cma=800M@0x400M-0xb80M
      </td>
    </tr>
    <tr>
      <td>2GB and above</td>
      <td>cma=800M@0x400M-0xb80M</td>
    </tr>
    {/* edm-g-8mm */}
    <tr>
      <td style={{ backgroundColor: "#C8C8C8" }} rowSpan={2}>
        EDM-G-IMX8M-MINI
      </td>
      <td>1GB</td>
      <td>
        cma=320M@0x400M-0xb80M galcore.contiguousSize=33554432
        androidboot.displaymode=720p
      </td>
      <td rowSpan={2}>
        cma=800M@0x400M-0xb80M
      </td>
    </tr>
    <tr>
      <td>2GB and above</td>
      <td>cma=800M@0x400M-0xb80M</td>
    </tr>
  </tbody>
</table>

### Example
#### Runtime stage

EDM-G-IMX8M-PLUS
```
u-boot=> setenv bootargs stack_depot_disable=on kasan.stacktrace=off console=ttymxc1,115200 earlycon=ec_imx6q,0x30890000,115200 init=/init firmware_class.path=/vendor/firmware loop.max_part=7 transparent_hugepage=never swiotlb=65536 pci=nomsi cma=800M@0x400M-0x1000M bootconfig buildvariant=userdebug loglevel=0
u-boot=> saveenv
u-boot=> boot
```

#### Compile stage
You also can fixed your overlay setting in u-boot header file according your target board.<br/>
For example, set the default properties of EDM-G-IMX8M-PLUS 
```c
#define CONFIG_EXTRA_ENV_SETTINGS	\
    ......
    "bootargs="\
    ......
    "cma=800M@0x400M-0x1000M "\
    ......
    "\0"
```
---

## LIBGPIOD JNI demo and APIs
 
Technexion provide a demo app about libgpiod JNI Test, specific source code as [kotlin_gpiod_demo](https://github.com/technexion-android/kotlin_gpiod_demo)

* Users can implement own GUI using our Input/Output APIs
    * Get total number of GPIO bank:
        * Java
            ```java
            public native Int getGpioTotalBank();
            ```
        * Kotlin
            ``` kotlin
            external fun getGpioTotalBank(): Int
            ```
    * Setting GPIO as output with specific value:
        * Java
            ```java
            public native String  setGpioInfo(int bank,int line, int value);
            ```
        * Kotlin
            ```kotlin
            external fun setGpioInfo(bank: Int, line: Int, value: Int): String
            ```
    * Setting GPIO as input and get a value:
        * Java
            ```java
            public native String  getGpioInfo(int bank,int line);
            ```
        * Kotlin
            ```kotlin
            external fun getGpioInfo(bank: Int, line: Int): String
            ```
* Users also can import JNI library to your own app using Androd Studio, detail instruction [here](https://github.com/technexion-android/kotlin_gpiod_demo#kotlin_gpiod_demo)

<!--:::(Internal) (Private notes)
Only visible to team accounts


## New OTA system: systemupgrade

Our Android 13 does support full update and incremental update.
**please contact our window to get detail instruction:
<EMAIL>**
-->