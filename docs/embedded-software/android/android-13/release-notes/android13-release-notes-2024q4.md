---
title: Release Notes 2024Q4
description: Release notes for Android 13, release 2024Q4
sidebar_position: 6
---
## Android 13 2024 Release Notes

### Links for source repositories
AOSP (android-13.0.0) + U-boot (2022.04) + <PERSON><PERSON> (5.15.74)
Tag: tn-android-13.0.0-2024Q4
| |Branch|Version|CoimmitID |
|:---|:---|:---|:---|
| Device | [tn-android-13.0.0_1.2.0_8m-next](https://github.com/technexion-android/platform_device_fsl/tree/tn-android-13.0.0_1.2.0_8m-next)|13.0.0_r30|266d1ebf2939d1f57ba405aefd9531a7727d16e1|
| U-boot | [tn-android13_1.2.0-2022.04](https://github.com/technexion-android/uboot-imx/tree/tn-android13_1.2.0-2022.04)|2022.04|b38f94984ae12c6aa3b184b245bf0e5128da7557|
| Kernel | [tn-android-13.0.0_1.2.0_8m-next](https://github.com/technexion-android/kernel_imx/tree/tn-android-13.0.0_1.2.0_8m-next)|5.15.74|5a0945e807e188625012d0dd1fef28c1e1bb2337|

---

### Supported Platforms in this Release
<!-- :::(Internal) (About support list)
Please refer to Yocto release notes
Example [Release Notes: YP4.0 2024Q1](https://developer.technexion.com/docs/release-notes-yp40-2024q1)
:::
 -->

import CopyableText from '@site/src/components/CopyableText.jsx';

### SOM
<table
  style={{
    fontFamily: "Arial",
    fontSize: 14,
    height: 600,
    overflowX: "auto",
    overflow: "auto",
    display: "flexbox",
    minWidth: "100%"
  }}
>
    <thead style={{ position: "sticky", top: 0, zIndex: 2 }}>
      <tr style={{ height: 26, backgroundColor: "#ffce93" }}>
      <td style={{ backgroundColor: "#f9f801", position: "sticky", left: 0 }}>
        <b> SOM </b>
      </td>
      <td
        style={{ backgroundColor: "#96f8c8", textAlign: "left" }}
        nowrap="nowrap"
      >
        <b> AXON-IMX8MP </b>
      </td>
      <td
        style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      >
        <b> EDM-G-IMX8MP </b>
      </td>
      <td
        style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      >
        <b> EDM-G-IMX8MM </b>
      </td>
      <td
        style={{ backgroundColor: "#96f8c8", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      >
        <b> PICO-IMX8MM <span style={{ color: "#ff0000" }}> (new) </span></b>
      </td>
      <td
        style={{ backgroundColor: "#96f8c8", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      >
        <b> PICP-IMX8MQ <span style={{ color: "#ff0000" }}> (new) </span></b>
      </td>
    </tr>
    <tr style={{ height: 26, backgroundColor: "#fae197", position: "sticky", top: 0, zIndex: 2 }}>
      <td style={{ position: "sticky", left: 0 }}>
        <b>Baseband</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>AXON-WIZARD</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WB</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDB-G-WIZARD</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WB</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WIZARD</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>PICO-PI</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>PICO-WIZARD</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>PICO-PI</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>PICO-WIZARD</b>
      </td>
    </tr>
  </thead>
  <tbody>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > Wifi driver </td>
      <td>QCA9377-3</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3</td>
      <td>QCA9377-3</td>
      <td>QCA9377-3</td>
      <td>QCA9377-3</td>
    </tr>
    <tr style={{ height: 26, backgroundColor: "#fae197" }}>
      <td style={{ position: "sticky", left: 0, zIndex: 1 }} nowrap="nowrap">
        <b> Panel support </b>
      </td>
      <td style={{ height: 26 }} colSpan={9}>&nbsp;</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > HDMI </td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
        nowrap="nowrap"
      > MIPI to HDMI <span style={{ color: "#ff0000" }}> (new) </span></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes</td>
      <td>NA</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
        nowrap="nowrap"
      > MIPI panel 5 inch<span style={{ color: "#ff0000" }}> (new) </span></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > LVDS 10.1 inch </td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > LVDS 15.6 inch </td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > LVDS 21.5 inch </td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl215192108"/></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 10.1 inch panel</td>
      <td>Yes <span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vizionpanel-vl10112880"/></td>
      <td>NA</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 15.0 inch panel</td>
      <td>Yes<span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vizionpanel-vl15010276"/></td>
      <td>NA</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 15.6 inch panel</td>
      <td>Yes<span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vizionpanel-vl15613676"/></td>
      <td>NA</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26, backgroundColor: "#fae197" }}>
      <td style={{ position: "sticky", left: 0 }}>
        <b> Camera </b>
      </td>
      <td style={{ height: 26 }} colSpan={9}>&nbsp;</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > TEVI-OV5640 </td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > TEVI-AR series </td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > VLI-OV5640 </td>
      <td>NA</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640-dual"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640-dual"/></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > VLI-AR Camera </td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >TEVS-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
      <td>Yes</td>
      <td>Yes</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
      <td>Yes<span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >VLS3-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
  </tbody>
</table>
<br/>


### HMI
-  More information for [Multi-Display feature](docs/embedded-software/android/android-13/android13-multi-display-guide.md).

<table
  border={0}
  style={{ fontFamily: "Arial", fontSize: 14, tableLayout: "fixed", textAlign: "left" }}
>
  <colgroup>
    <col style={{ width: 250 }} />
    <col style={{ width: 250 }} />
  </colgroup>
  <thead>
    <tr>
      <th style={{ backgroundColor: "#f9f801" }}>Product</th>
      <th style={{ backgroundColor: "#96f8c8" }}>TEP-IMX8MP</th>
    </tr>
  </thead>
  <tbody>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td><b>SOC variants</b></td>
      <td><b>i.mx8m Plus</b></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>DRAM Variants</td>
      <td>2/4/8GB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Wireless(Optional)</td>
      <td>QCA9377-5(PCIE)</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Atheros/Realtek phy</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Panel support</b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>HDMI</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>LVDS 15.6 inch</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>LVDS 21.5 inch</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VIZIONPANEL with 10.1 inch panel</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VIZIONPANEL with 15.0 inch panel</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VIZIONPANEL with 15.6 inch panel</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Camera </b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-OV5640</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLS3-AR Camera </td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
    </tr>
  </tbody>
</table>
<br/>

### Box PC
<table
  border={0}
  style={{ fontFamily: "Arial", fontSize: 14, tableLayout: "fixed", textAlign: "left" }}
>
  <colgroup>
    <col style={{ width: 250 }} />
    <col style={{ width: 250 }} />
  </colgroup>
  <thead>
    <tr>
      <th style={{ backgroundColor: "#f9f801" }}>Product</th>
      <th style={{ backgroundColor: "#96f8c8" }}>TEK-IMX8MP</th>
    </tr>
  </thead>
  <tbody>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td><b>SOC variants</b></td>
      <td><b>i.mx8m Plus</b></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>DRAM Variants</td>
      <td>2/4/8GB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Wireless(Optional)</td>
      <td>QCA9377-5(PCIE)</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Realtek phy</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Panel support</b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>HDMI</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Camera</b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-OV5640</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLS3-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
    </tr>
  </tbody>
</table>

:::info notes
#### Device Tree Overlay Configuration
Technexion implement a device-tree overlay method using AVB 3.0.

For panel and camera detection, manual setup via the U-Boot prompt is required as indicated by 'Yes' in the support table, along with the corresponding U-Boot commands. Get U-Boot commands with expanding toggle symbol `▶`. Proceed to boot into the U-Boot prompt and execute these commands.

Certain peripherals feature auto-detection, denoted solely by 'Yes' in the support table. No manual U-Boot intervention is necessary for these devices.

**Example**
- Using Vizionlink with TEVS-AR (VLS3-AR) camera on edm-g-imx8mp, please type command in u-boot:
```
u-boot=> setenv dtoverlay vls
```
- Using TEVS-AR camera on edm-g-imx8mp, no need to set `dtoverlay` env in u-boot.
:::
<br/>

* * *

### Software Specifications

| Features | |
|:---|:---|
| Framework | 1. NEW KOTLIN based gpiod demo app <br/> 2. GPIOD JNI library for Kotlin/Java App |
| Security | 1. Fully SELinux enabled <br/> 2. Kernel Control Flow Integrity (kCFI) <br/> 3. File-Based Encryption (FBE)
| System | 1. A/B partitions <br/> 2. System Updater (New OTA) <br/> 3. Factory reset default <br/> 4. Device Tree Overlay using AVB 3.0 |
| Wireless | 1. WiFi STA/AP mode <br/> 2. WiFi Direct <br/> 3. Bluetooth classic <br/> 4. Bluetooth LE|
| Vision | VIZIONLINK camera support - AR series|
| Nerual Network | 1. TensorFlow-Lite (CPU, NPU) <br/> 2. OpenCV (CPU) |
|Video support format | MP4, AVI, MKV, WEBM, 3gp |
|Audio support format | WAV, MP3, AAC (EAC3 need install plug-in apk) |

* * *

### Latest Update log:
**tn-android-13.0.0_1.2.0_8m-next**
<br/>2024/12/26
 - fix QCA9377-5 crash when connect WPA3 wifi issue
 
<br/>2024/12/15
- fix bugs
    - gpiod app crash issue
    - system crash when HDMI plug in
    - camera app crash when capture
    - EDM-G-IMX8MP
        - abnormal frame with vizionlink tevi ov5640 camera
        - can't recognize vizionpanel
    - EDM-G-IMX8MM
        - system crash when using remote monitor tool (vysor, scrcpy)
    - TEP-IMX8MP / TEK-IMX8MP
        - sometimes can't boot up into system
        - when select Factory Reset mode, system can't compelet and enter recovery mode

<br/>2024/10/15
- PICO-IMX8MM (new)
    - MIPI panel support
- PICO-IMX8MQ (new)
    - MIPI panel support
    - TEVS camera support
- EDM-G-IMX8MM
    - Add TEVS camera support on wizard board
- AXON-IMX8MP
    - Add vizionpanel support

<br/>2024/08/15
- TEP-IMX8MP (new)
    - multi-display support with associate touch
    - QSPI flash support
    - fw_env tool support
-  TEK-IMX8MP (new)
    -  QSPI flash support
    -  fw_env tool support
-  EDM-G-IMX8MP
    - add tevs and vls support on wizard board
    - add vizionpanel support on wizard board
-  uuu tool update to 20240628
-  new support hardware
    - ath10k wifi
    - wm8904 audio
-  camera and panel auto detect

<br/>2024/05/15
* Add Vizion TEVS and VLS support
* Add NXP IW416 wifi module driver
* Fix hotsopt (AP mode) issue
* Fix CFI issue on wifi

<br/>2023/08/15
* Fix LVDS brightness control
* Fix EDM-G-IMX8M-PLUS phone jack output issue
* Fix EDM-G-IMX8M-MINI TEVI-AR serials camera issue
* BT work
* OTA ready

<br/>2023/07/21
* First release

#### Known issue
1. TEP-IMX8MP
    * BT not ready
2. TEK-IMX8MP
    * BT not ready
  
#### Limitation
1. EDM-G-IMX8M-PLUS
    * NFC not supported
    * HDMI brightness control not supported
    * HDMI audio output not supported
2. EDM-G-IMX8M-MINI
    * NFC not supported
    * VIZIONLINK-TEVI-AR series screen will appear upside down
3. TEP-IMX8MP
    * wifi hotspot not supported
4. TEK-IMX8MP
    * wifi hotspot not supported
* * *

### Prebuilt Images
Prebuilt images are avialable for download via TechNexion's server.
|SOM|Set|
|:---|:---|
|AXON-IMX8MP|WIZARD [axon-imx8mp_axon-wizard_android-13.0_qca9377_hdmi-1920x1080_20241226.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived//axon-imx8mp_axon-wizard_android-13.0_qca9377_hdmi-1920x1080_20241226.zip)|
|EDM-G-IMX8MP|WB [edm-g-imx8mp_edm-g-wb_android-13.0_qca9377_hdmi-1920x1080_20241226.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived//edm-g-imx8mp_edm-g-wb_android-13.0_qca9377_hdmi-1920x1080_20241226.zip)<br/>WIZARD [edm-g-imx8mp_edm-g-wizard_android-13.0_qca9377_hdmi-1920x1080_20241226.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived//edm-g-imx8mp_edm-g-wizard_android-13.0_qca9377_hdmi-1920x1080_20241226.zip)|
|EDM-G-IMX8MM|WB [edm-g-imx8mm_edm-g-wb_android-13.0_qca9377_lvds-1280x800_20241226.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived//edm-g-imx8mm_edm-g-wb_android-13.0_qca9377_lvds-1280x800_20241226.zip)<br/>WIZARD [edm-g-imx8mm_edm-g-wizard_android-13.0_qca9377_lvds-1280x800_20241226.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived//edm-g-imx8mm_edm-g-wizard_android-13.0_qca9377_lvds-1280x800_20241226.zip)|
|PICO-IMX8MM|PI [pico-imx8mm_pico-pi-8m_android-13.0_qca9377_mipi5-1280x720_20241226.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived//pico-imx8mm_pico-pi-8m_android-13.0_qca9377_mipi5-1280x720_20241226.zip)<br/>WIZARD [pico-imx8mm_pico-pi-wizard_android-13.0_qca9377_mipi5-1280x720_20241226.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived//pico-imx8mm_pico-pi-wizard_android-13.0_qca9377_mipi5-1280x720_20241226.zip)|
|PICO-IMX8MQ|PI [pico-imx8mq_pico-pi-8m_android-13.0_qca9377_mipi5-1280x720_20241226.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived//pico-imx8mq_pico-pi-8m_android-13.0_qca9377_mipi5-1280x720_20241226.zip)<br/>WIZARD [pico-imx8mq_pico-wizard_android-13.0_qca9377_mipi5-1280x720_20241226.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived//pico-imx8mq_pico-wizard_android-13.0_qca9377_mipi5-1280x720_20241226.zip)|

|HMI|Set|
|:---|:---|
|TEP-IMX8MP| [tep-imx8mp_android-13.0_hdmi_20241226.zip](https://download.technexion.com/demo_software/TEP/IMX8/tep-imx8mp/archived//tep-imx8mp_android-13.0_hdmi_20241226.zip)|

|BOX PC|Set|
|:---|:---|
|TEK-IMX8MP| [tek-imx8mp_android-13.0_hdmi_20241226.zip](https://download.technexion.com/demo_software/TEK/IMX8/tek-imx8mp/archived//tek-imx8mp_android-13.0_hdmi_20241226.zip)|
