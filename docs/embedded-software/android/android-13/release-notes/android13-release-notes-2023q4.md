---
title: Release Notes 2023Q4
description: Android 13 2023 Release Notes 2023, Q4
sidebar_position: 9
---
## Android 13 2023 Release Notes

### Supported Platforms in this Release
<!--
Please refer to Yocto release notes
Example [Release Notes: YP4.0 2023Q2](https://developer.technexion.com/docs/release-notes-yp40-2023q2)
-->

|System-On-Module|Baseboard|DISPLAY|Accessories|
|---|---|---|---|
|EDM-G-IMX8M-PLUS|1. EDM-G-WB  <br/> 2.EDM-G-WIZARD |1. HDMI  <br/>2. Vl215192108 21" LVDS panel  <br/>3. VL15613676 15.6" LVDS panel  <br/>4. Vl10112880 10" LVDS panel  | 1. TEVI-OV5640 camera  <br/>2. TEVI-AR0144 Camera  <br/>3. TEVI-AR0234 Camera  <br/>4. TEVI-AR0521/0522 Camera  <br/>5. TEVI-AR0821/0822 Camera  <br/>6. TEVI-AR1335 Camera  <br/>7. VIZ<PERSON>LINK with AR series |
|EDM-G-IMX8M-MINI|1. EDM-G-WB  <br/> 2.EDM-G-WIZARD |1. Vl215192108 21" LVDS panel  <br/>2. VL15613676 15.6" LVDS panel  <br/>3. Vl10112880 10" LVDS panel | 1. TEVI-OV5640 Camera  <br/>2. TEVI-AR0144 Camera  <br/>3. TEVI-AR0234 Camera  <br/>4. TEVI-AR0521/0522 Camera  <br/>5. TEVI-AR0821/0822 Camera  <br/>6. TEVI-AR1335 Camera  <br/>7. VIZIONLINK with AR series |

* * *

### Software Specifications

| tn-android-13.0.0_1.2.0_8m-next
|---
| U-boot version (2022.04)
| Google Kernel version (5.15.74)
| AOSP (android-13.0.0)

| Features | |
|:---|:---|
| Framework | 1. NEW KOTLIN based gpiod demo app   <br/> 2. GPIOD JNI library for Kotlin/Java App |
| Security | 1. Fully SELinux enabled   <br/> 2. Kernel Control Flow Integrity (kCFI)   <br/> 3. File-Based Encryption (FBE)
| System | 1. A/B partitions   <br/> 2. System Updater (New OTA)   <br/> 3. Factory reset default   <br/> 4. Device Tree Overlay using AVB 3.0 |
| Wireless | 1. WiFi STA/AP mode   <br/> 2. WiFi Direct   <br/> 3. Bluetooth classic   <br/> 4. Bluetooth LE|
| Vision | VIZIONLINK camera support - AR series|
| Neural Network | 1. TensorFlow-Lite (CPU, NPU)   <br/> 2. OpenCV (CPU) |
|Video support format | MP4, AVI, MKV, WEBM, 3gp |
|Audio support format | WAV, MP3, AAC (EAC3 need install plug-in apk) |

* * *

### Latest Update log:
**tn-android-13.0.0_1.2.0_8m-next**
2023/07/21: First release
  <br/>2023/08/15
* Fix LVDS brightness control
* Fix EDM-G-IMX8M-PLUS phone jack output issue
* Fix EDM-G-IMX8M-MINI TEVI-AR serials camera issue
* BT work
* OTA ready

#### Known issue
1. EDM-G-IMX8M-PLUS
    * VIZIONLINK with TEVI-OV5640 not work
2. EDM-G-IMX8M-MINI
    * VIZIONLINK with TEVI-OV5640 not work

#### Limitation
1. EDM-G-IMX8M-PLUS
    * NFC not supported
    * HDMI brightness control not supported
    * HDMI audio output not supported
2. EDM-G-IMX8M-MINI
    * NFC not supported
    * VIZIONLINK-TEVI-AR series screen will appear upside down

* * *

### Prebuilt Images
Prebuilt images are avialable for download via TechNexion's server.
|Product|Set|
|---|---|
|EDM-G-IMX8M-PLUS| WANDBOARD [edm-g-imx8mp_wandboard_android-13.0.0_1.2.0_QCA9377_hdmi-1920x1080_20231016](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wandboard_android-13.0.0_1.2.0_QCA9377_hdmi-1920x1080_20231016.zip)   <br/> WIZARD [edm-g-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi-1920x1080_20231016](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi-1920x1080_20231016.zip) |
|EDM-G-IMX8M-MINI| WANDBOARD [edm-g-imx8mm_wandboard_android-13.0.0_1.2.0_QCA9377_lvds-1280x800_20231016](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wandboard_android-13.0.0_1.2.0_QCA9377_lvds-1280x800_20231016.zip)  <br/>WIZARD [edm-g-imx8mm_wizard_android-13.0.0_1.2.0_QCA9377_lvds-1280x800_20231016](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wizard_android-13.0.0_1.2.0_QCA9377_lvds-1280x800_20231016.zip) |
