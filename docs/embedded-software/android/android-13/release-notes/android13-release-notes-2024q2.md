---
title: Release Notes 2024Q2
description: Android 13 2024Q2 Release Notes
sidebar_position: 8
---
## Android 13 2024 Release Notes

### Supported Platforms in this Release
<!-- :::(Internal) (About support list)
Please refer to Yocto release notes
Example [Release Notes: YP4.0 2023Q2](https://developer.technexion.com/docs/release-notes-yp40-2023q2)
:::-->
<>
  <style
    type="text/css"
    dangerouslySetInnerHTML={{
      __html:
        "\n.tg  {border-collapse:collapse;border-spacing:0;}\n.tg td{border-color:black;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;\n  overflow:hidden;padding:10px 5px;word-break:normal;}\n.tg th{border-color:black;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;\n  font-weight:normal;overflow:hidden;padding:10px 5px;word-break:normal;}\n.tg .tg-44z1{background-color:#ffce93;border-color:inherit;font-weight:bold;position:-webkit-sticky;position:sticky;\n  text-align:center;top:-1px;vertical-align:top;will-change:transform}\n.tg .tg-llyw{background-color:#c0c0c0;border-color:inherit;text-align:left;vertical-align:top}\n.tg .tg-8ccl{background-color:#ffce93;border-color:inherit;font-weight:bold;position:-webkit-sticky;position:sticky;text-align:left;\n  top:-1px;vertical-align:top;will-change:transform}\n.tg .tg-6e8n{background-color:#c0c0c0;border-color:inherit;font-weight:bold;text-align:left;vertical-align:top}\n.tg .tg-0pky{border-color:inherit;text-align:left;vertical-align:top}\n"
    }}
  />
 <table
    style={{
      fontSize: 12,
      height: 600,
      overflowX: "auto",
      overflow: "auto",
      display: "flexbox",
      minWidth: "100%"
    }}
 >
    <thead  style={{ position: "sticky", top: 0, zIndex: 2 }}>
      <tr style={{ backgroundColor: "#ffce93", textAlign: "left" }}>
        <th style={{ backgroundColor: "#f9f801" }}>
          <b>SOM</b> 
        </th>
        <th style={{ backgroundColor: "#96f8c8", textAlign: "left" }}>
          <b> AXON-IMX8MP </b>
        </th>
        <th
          style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
          colSpan={2}
        >
          <b> EDM-G-IMX8MP </b>
        </th>
        <th
          style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
          colSpan={2}
        >
          <b> EDM-G-IMX8MM </b>
        </th>
      </tr>
      <tr style={{ backgroundColor: "#fae197", height: 26, position: "sticky", top: 0, zIndex: 1 }}>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>Baseband</span>
        </td>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>AXON-WIZARD</span>
        </td>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>EDM-G-WB</span>
        </td>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>EDB-G-WIZARD</span>
        </td>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>EDM-G-WB</span>
        </td>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>EDM-G-WIZARD</span>
        </td>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>Wifi driver</span>
          <br />
        </td>
        <td className="tg-0pky">QCA9377-3</td>
        <td className="tg-0pky">
          QCA9377-3<br />IW416 <span style={{ color: "red" }}>(new)</span>
        </td>
        <td className="tg-0pky">
          QCA9377-3<br />IW416 <span style={{ color: "red" }}>(new)</span>
        </td>
        <td className="tg-0pky">
          QCA9377-3<br />IW416 <span style={{ color: "red" }}>(new)</span>
        </td>
        <td className="tg-0pky">
          QCA9377-3<br />IW416 <span style={{ color: "red" }}>(new)</span>
        </td>
      </tr>
      <tr style={{ backgroundColor: "#fae197" }}>
        <td className="tg-llyw">
          <span style={{ fontWeight: "bold", color: "black" }}> Panel support </span>
        </td>
        <td className="tg-llyw" colSpan={5} />
      </tr> 
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>HDMI</span>
        </td>
        <td className="tg-0pky">Yes (default)</td>
        <td className="tg-0pky">Yes (default)</td>
        <td className="tg-0pky">Yes (default)</td>
        <td className="tg-0pky">NA</td>
        <td className="tg-0pky">NA</td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>LVDS 10.1 inch</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl10112880
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl10112880
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl10112880
          </span>
        </td>
        <td className="tg-0pky">
          Yes (default) <br />
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay sn65dsi84-vl10112880
          </span>
        </td>
        <td className="tg-0pky">
          Yes (default) <br />
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay sn65dsi84-vl10112880
          </span>
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>LVDS 15.6 inch</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl15613676
          </span>{" "}
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl15613676
          </span>{" "}
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl15613676
          </span>{" "}
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay sn65dsi84-vl15613676
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay sn65dsi84-vl15613676
          </span>
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>LVDS 21.5 inch</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl215192108
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl215192108
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay lvds-vl215192108
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay sn65dsi84-vl215192108
          </span>{" "}
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay sn65dsi84-vl215192108
          </span>{" "}
        </td>
      </tr>
      <tr style={{ backgroundColor: "#fae197" }}>
        <td className="tg-6e8n">
          <span style={{ fontWeight: "bold", color: "black" }}>Camera</span>
        </td>
        <td className="tg-llyw" colSpan={5} />
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>TEVI-OV5640</span>
          <br />
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ov5640
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ov5640
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ov5640
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ov5640
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ov5640
          </span>
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>TEVI-AR series</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevi-ap1302
          </span>
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>Vizionlink with TEVI-OV5640</span>
        </td>
        <td className="tg-0pky">
          NA
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ov5640
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ov5640
          </span>
        </td>
        <td className="tg-0pky">
          NA
        </td>
        <td className="tg-0pky">
          NA
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>Vizionlink with TEVI-AR series</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ap1302
          </span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vizionlink-tevi-ap1302
          </span>
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>TEVS-AR series</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevs
          </span>
        </td>
        <td className="tg-0pky">
          Yes<span style={{ color: "red" }}> (new)</span>
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevs
          </span>
        </td>
        <td className="tg-0pky">
          NA
        </td>
        <td className="tg-0pky">
          Yes<span style={{ color: "red" }}> (new)</span>
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay tevs
          </span>
        </td>
        <td className="tg-0pky">
          NA
        </td>
      </tr>
      <tr>
        <td className="tg-0pky">
          <span style={{ color: "black" }}>Vizionlink with TEVS</span>
        </td>
        <td className="tg-0pky">
          Yes
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vls
          </span>
        </td>
        <td className="tg-0pky">
          Yes <span style={{ color: "red" }}>(new)</span>
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vls
          </span>
        </td>
        <td className="tg-0pky">
          NA
        </td>
        <td className="tg-0pky">
          Yes <span style={{ color: "red" }}>(new)</span>
          <span style={{ color: "#1A1A1B", backgroundColor: "white" }}>
            u-boot=&gt; setenv dtoverlay vls
          </span>
        </td>
        <td className="tg-0pky">
          NA
        </td>
      </tr>
    </tbody>
  </table>
</>


* * *

### Software Specifications

| tn-android-13.0.0_1.2.0_8m-next |
|---|
| U-boot version (2024.05)|
| Google Kernel version (5.15.71)|
| AOSP (android-13.0.0)|

| Features | |
|:---|:---|
| Framework | 1. NEW KOTLIN based gpiod demo app <br/> 2. GPIOD JNI library for Kotlin/Java App |
| Security | 1. Fully SELinux enabled <br/> 2. Kernel Control Flow Integrity (kCFI) <br/> 3. File-Based Encryption (FBE)
| System | 1. A/B partitions <br/> 2. System Updater (New OTA) <br/> 3. Factory reset default <br/> 4. Device Tree Overlay using AVB 3.0 |
| Wireless | 1. WiFi STA/AP mode <br/> 2. WiFi Direct <br/> 3. Bluetooth classic <br/> 4. Bluetooth LE|
| Vision | VIZIONLINK camera support - AR series|
| Nerual Network | 1. TensorFlow-Lite (CPU, NPU) <br/> 2. OpenCV (CPU) |
|Video support format | MP4, AVI, MKV, WEBM, 3gp |
|Audio support format | WAV, MP3, AAC (EAC3 need install plug-in apk) |

* * *

### Latest Update log:
**tn-android-13.0.0_1.2.0_8m-next**
2024/05/15
* Add Vizion TEVS and VLS support
* Add NXP IW416 wifi module driver
* Fix hotsopt (AP mode) issue
* Fix CFI issue on wifi

<br/>2023/08/15
* Fix LVDS brightness control
* Fix EDM-G-IMX8M-PLUS phone jack output issue
* Fix EDM-G-IMX8M-MINI TEVI-AR serials camera issue
* BT work
* OTA ready

<br/>2023/07/21
* First release

#### Known issue

#### Limitation
1. EDM-G-IMX8M-PLUS
    * NFC not supported
    * HDMI brightness control not supported
    * HDMI audio output not supported
2. EDM-G-IMX8M-MINI
    * NFC not supported
    * VIZIONLINK-TEVI-AR series screen will appear upside down

* * *

### Prebuilt Images
Prebuilt images are avialable for download via TechNexion's server.
|Product|Set|
|:---|:---|
|AXON-IMX8M-PLUS|WIZARD [axon-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240513](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240513.zip) |
|EDM-G-IMX8M-PLUS| WANDBOARD [edm-g-imx8mp_wandboard_android-13.0.0_1.2.0_QCA9377_hdmi_20240513](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wandboard_android-13.0.0_1.2.0_QCA9377_hdmi_20240513.zip) <br/> WIZARD [edm-g-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240513](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240513.zip) |
|EDM-G-IMX8M-MINI| WANDBOARD [edm-g-imx8mm_wandboard_android-13.0.0_1.2.0_QCA9377_lvds10_20240514](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wandboard_android-13.0.0_1.2.0_QCA9377_lvds10_20240514.zip)<br/>WIZARD [edm-g-imx8mm_wizard_android-13.0.0_1.2.0_QCA9377_lvds10_20240514](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wizard_android-13.0.0_1.2.0_QCA9377_lvds10_20240514.zip) |
