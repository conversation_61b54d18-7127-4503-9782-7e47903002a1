---
title: Release Notes 2024Q3
description: Android 13 release notes 2024, Q3
sidebar_position: 7
---
## Android 13 2024 Release Notes

### Supported Platforms in this Release
<!-- :::(Internal) (About support list)
Please refer to Yocto release notes
Example [Release Notes: YP4.0 2024Q1](https://developer.technexion.com/docs/release-notes-yp40-2024q1)
:::
 -->

import CopyableText from '@site/src/components/CopyableText.jsx';

### SOM
<table
    style={{
      fontFamily: "Arial",
      fontSize: 14,
      height: 600,
      overflowX: "auto",
      overflow: "auto",
      display: "flexbox",
      minWidth: "100%"
    }}
>
    <thead style={{ position: "sticky", top: 0, zIndex: 2 }}>
      <tr style={{ height: 26, backgroundColor: "#ffce93" }}>
        <td style={{ backgroundColor: "#f9f801", position: "sticky", left: 0 }}>
        <b> SOM </b>
      </td>
      <td
        style={{ backgroundColor: "#96f8c8", textAlign: "left" }}
        nowrap="nowrap"
      >
        <b> AXON-IMX8MP </b>
      </td>
      <td
        style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      >
        <b> EDM-G-IMX8MP </b>
      </td>
      <td
        style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      >
        <b> EDM-G-IMX8MM </b>
      </td>
    </tr>
    <tr style={{ backgroundColor: "#fae197", height: 26, position: "sticky", top: 0, zIndex: 1 }}>
      <td style={{ position: "sticky", left: 0 }}>
      <b>Baseband</b>
    </td>
      <td style={{ minWidth: 150 }}>
        <b>AXON-WIZARD</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WB</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDB-G-WIZARD</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WB</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WIZARD</b>
      </td>
    </tr>
  </thead>
  <tbody>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > Wifi driver </td>
      <td>QCA9377-3</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3<br />IW416</td>
      <td>QCA9377-3<br />IW416</td>
    </tr>
    <tr style={{ height: 26, backgroundColor: "#fae197" }}>
      <td style={{ position: "sticky", left: 0, zIndex: 1 }} nowrap="nowrap">
        <strong> Panel support </strong>
      </td>
      <td style={{ height: 26 }} colSpan={5}>&nbsp;</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >HDMI</td>
      <td>Yes (default)</td>
      <td>Yes (default)</td>
      <td>Yes (default)</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >LVDS 10.1 inch</td>
      <td>Yes<span style={{ color: "#FE0000" }}> (auto)</span></td>
      <td>Yes<span style={{ color: "#FE0000" }}> (auto)</span></td>
      <td>Yes<span style={{ color: "#FE0000" }}> (auto)</span></td>
      <td>Yes (default)</td>
      <td>Yes (default)</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >LVDS 15.6 inch</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >LVDS 21.5 inch</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl215192108"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl215192108"/></td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 10.1 inch panel</td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes <span style={{ color: "#ff0000" }}> (new)</span></td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 15.0 inch panel</td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes <span style={{ color: "#ff0000" }}> (new)</span></td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 15.6 inch panel</td>
      <td>NA</td>
      <td>NA</td>
      <td>Yes <span style={{ color: "#ff0000" }}> (new)</span></td>
      <td>NA</td>
      <td>NA</td>
    </tr>
      <tr style={{ height: 26, backgroundColor: "#fae197" }}>
        <td style={{ position: "sticky", left: 0 }}>
          <strong> Camera </strong>
        </td>
        <td style={{ height: 26 }} colSpan={5}>&nbsp;</td>
      </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > TEVI-OV5640 </td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > TEVI-AR series </td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > VLI-OV5640 </td>
      <td>NA</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640"/></td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      > VLI-AR Camera </td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >TEVS-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(auto)</span></td>
      <td>Yes <span style={{ color: "#FE0000" }}>(new) (auto)</span></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >VLS3-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>NA</td>
    </tr>
  </tbody>
</table>


### HMI
<table
  border={0}
  style={{ fontFamily: "Arial", fontSize: 14, tableLayout: "fixed", textAlign: "left" }}
>
  <colgroup>
    <col style={{ width: 250 }} />
    <col style={{ width: 250 }} />
  </colgroup>
  <thead>
    <tr>
      <th style={{ backgroundColor: "#f9f801" }}>Product</th>
      <th style={{ backgroundColor: "#96f8c8" }}>TEP-IMX8MP<span style={{ color: "#ff0000" }}> (new)</span></th>
    </tr>
  </thead>
  <tbody>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td><b>SOC variants</b></td>
      <td><b>i.mx8m Plus</b></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>DRAM Variants</td>
      <td>2/4/8GB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Wireless(Optional)</td>
      <td>QCA9377-5(PCIE)<br/>BT 5.0</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Atheros/Realtek phy</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Panel support</b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>HDMI</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>LVDS 15.6 inch</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>LVDS 21.5 inch</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VIZIONPANEL with 10.1 inch panel</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VIZIONPANEL with 15.0 inch panel</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VIZIONPANEL with 15.6 inch panel</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Camera </b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-OV5640</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLS3-AR Camera </td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
    </tr>
  </tbody>
</table>
<br/>

    
### Box PC
<table
  border={0}
  style={{ fontFamily: "Arial", fontSize: 14, tableLayout: "fixed", textAlign: "left" }}
>
  <colgroup>
    <col style={{ width: 250 }} />
    <col style={{ width: 250 }} />
  </colgroup>
  <thead>
    <tr>
      <th style={{ backgroundColor: "#f9f801" }}>Product</th>
      <th style={{ backgroundColor: "#96f8c8" }}>TEK-IMX8MP<span style={{ color: "#ff0000" }}> (new)</span></th>
    </tr>
  </thead>
  <tbody>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td><b>SOC variants</b></td>
      <td><b>i.mx8m Plus</b></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>DRAM Variants</td>
      <td>2/4/8GB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Wireless(Optional)</td>
      <td>QCA9377-5(PCIE)<br/>BT 5.0</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>Realtek phy</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Panel support</b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>HDMI</td>
      <td>Yes</td>
    </tr>
    <tr style={{ backgroundColor: "#fae197" }}>
      <td>
        <b>Camera</b>
      </td>
      <td />
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-OV5640</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ov5640"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLI-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vizionlink-tevi-ap1302"/></td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "antiquewhite" }}>VLS3-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
    </tr>
  </tbody>
</table>


* * *

### Software Specifications


AOSP (android-13.0.0) + U-boot (2022.04) + Kernel (5.15.71)
| | Branch | CoimmitID |
|:---|:---|:---|
| U-boot | [tn-android13_1.2.0-2022.04](https://github.com/technexion-android/uboot-imx/tree/tn-android13_1.2.0-2022.04)|beaaf4e5b92e31c9736b5133eb9652de3e0f6ff3|
| Kernel | [tn-android-13.0.0_1.2.0_8m-next](https://github.com/technexion-android/kernel_imx/tree/tn-android-13.0.0_1.2.0_8m-next)|58bbef1c8dbf7472e4b97418283df5f3218f31ab|
| Device | [tn-android-13.0.0_1.2.0_8m-next](https://github.com/technexion-android/platform_device_fsl/tree/tn-android-13.0.0_1.2.0_8m-next)|04fdf424831bbc45bcd56c984b7ba304dc645927|


| Features | |
|:---|---|
| Framework | 1. NEW KOTLIN based gpiod demo app <br/> 2. GPIOD JNI library for Kotlin/Java App |
| Security | 1. Fully SELinux enabled <br/> 2. Kernel Control Flow Integrity (kCFI) <br/> 3. File-Based Encryption (FBE)
| System | 1. A/B partitions <br/> 2. System Updater (New OTA) <br/> 3. Factory reset default <br/> 4. Device Tree Overlay using AVB 3.0 |
| Wireless | 1. WiFi STA/AP mode <br/> 2. WiFi Direct <br/> 3. Bluetooth classic <br/> 4. Bluetooth LE|
| Vision | VIZIONLINK camera support - AR series|
| Nerual Network | 1. TensorFlow-Lite (CPU, NPU) <br/> 2. OpenCV (CPU) |
|Video support format | MP4, AVI, MKV, WEBM, 3gp |
|Audio support format | WAV, MP3, AAC (EAC3 need install plug-in apk) |

* * *

### Latest Update log:
**tn-android-13.0.0_1.2.0_8m-next**
<br/>2024/08/15
- TEP-IMX8MP (new)
    - multi-display support with associate touch
    - QSPI flash support
    - fw_env tool support
-  TEK-IMX8MP (new)
    -  QSPI flash support
    -  fw_env tool support
-  EDM-G-IMX8MP
    - add tevs and vls support on wizard board
    - add vizionpanel support on wizard board
-  uuu tool update to 20240628
-  new support hardware
    - ath10k wifi
    - wm8904 audio
-  camera and panel auto detect

<br/>2024/05/15
* Add Vizion TEVS and VLS support
* Add NXP IW416 wifi module driver
* Fix hotsopt (AP mode) issue
* Fix CFI issue on wifi

<br/>2023/08/15
* Fix LVDS brightness control
* Fix EDM-G-IMX8M-PLUS phone jack output issue
* Fix EDM-G-IMX8M-MINI TEVI-AR serials camera issue
* BT work
* OTA ready

<br/>2023/07/21
* First release

#### Known issue
1. TEP-IMX8MP
    * BT not ready
2. TEK-IMX8MP
    * BT not ready
  
#### Limitation
1. EDM-G-IMX8M-PLUS
    * NFC not supported
    * HDMI brightness control not supported
    * HDMI audio output not supported
2. EDM-G-IMX8M-MINI
    * NFC not supported
    * VIZIONLINK-TEVI-AR series screen will appear upside down

* * *

### Prebuilt Images
Prebuilt images are avialable for download via TechNexion's server.
|SOM|Set|
|:---|:---|
|AXON-IMX8M-PLUS|WIZARD [axon-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240807.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240807.zip) |
|EDM-G-IMX8M-PLUS| WANDBOARD [edm-g-imx8mp_wandboard_android-13.0.0_1.2.0_QCA9377_hdmi_20240807.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wandboard_android-13.0.0_1.2.0_QCA9377_hdmi_20240807.zip) <br/> WIZARD [edm-g-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240807.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wizard_android-13.0.0_1.2.0_QCA9377_hdmi_20240807.zip) |
|EDM-G-IMX8M-MINI| WANDBOARD [edm-g-imx8mm_wandboard_android-13.0.0_1.2.0_QCA9377_lvds10_20240807.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wandboard_android-13.0.0_1.2.0_QCA9377_lvds10_20240807.zip)<br/>WIZARD [edm-g-imx8mm_wizard_android-13.0.0_1.2.0_QCA9377_lvds10_20240807.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wizard_android-13.0.0_1.2.0_QCA9377_lvds10_20240807.zip) |

|HMI||
|:---|:---|
|TEP-IMX8MP|[tep-imx8mp_android-13.0.0_1.2.0_QCA9377_lvds_20240807.zip](https://download.technexion.com/demo_software/TEP/IMX8/tep-imx8mp/archived/tep-imx8mp_android-13.0.0_1.2.0_QCA9377_lvds_20240807.zip)|

|BOX PC||
|:---|:---|
|TEK-IMX8MP|[tek-imx8mp_android-13.0.0_1.2.0_QCA9377_hdmi_20240729.zip](https://download.technexion.com/demo_software/TEK/IMX8/tek-imx8mp/archived/tek-imx8mp_android-13.0.0_1.2.0_QCA9377_hdmi_20240729.zip)|
