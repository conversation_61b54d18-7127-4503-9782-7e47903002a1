---
title: Setup, Build, and Flashing for ARM64 Systems
description: Android 8 Setup, Build, and Flashing for ARM64 Systems
sidebar_position: 0
---
### Download The Source code

Github way (Prepare repo command first is recommended)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-o8.1.0_1.3.0_8m-ga
$ repo sync -j<N> (N is up to cors numbers on your host PC)
```
    
How to download repo manually:
```shell
$ curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
$ chmod a+x repo
$ sudo mv repo /usr/bin/repo
```

### Compiling Environment Setup

General packages installation ( Ubuntu 16.04 or above)

```shell
$ sudo apt-get install uuid uuid-dev zlib1g-dev liblz-dev liblzo2-2 liblzo2-dev lzop \
git-core curl u-boot-tools mtd-utils android-tools-fsutils device-tree-compiler gdisk \
gnupg flex bison gperf build-essential zip curl zlib1g-dev gcc-multilib g++-multilib \
libc6-dev-i386 lib32ncurses5-dev x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev \
libxml2-utils xsltproc unzip sshpass ssh-askpass zip xz-utils kpartx vim screen sudo wget \
bc locales openjdk-8-jdk rsync docker.io
```

Technexion Docker image generation

```shell
$ cd cookers
$ docker build -t build_droid8 .
$ sudo docker run --privileged=true --name mx8_build  -v /home/<USER>/<source folder>:/home/<USER>
(first time)

$ sudo docker ps -a
$ sudo docker start <your container id>
$ sudo docker exec -it mx8_build bash
(after first time)
```


### Starting Compile The Source Code
 
* Source the compile relative commands:

    **For PICO-IMX8M HDMI**
    ```shell
    $ source cookers/env.bash.imx8.pico-8m.pi.hdmi
    ```
    <br/>

    **For PICO-IMX8M 5-inch LCD (1280x720 resolution via MIPI-DSI interface)**
    ```shell
    $ source cookers/env.bash.imx8.pico-8m.pi.lcd
    ```
    <br/>

    **For PICO-IMX8M Dual Display : HDMI and MIPI-to-LVDS 7-inch Panel with 1024x600 resolution (Demo Stage)**
    ```shell
    $ source cookers/env.bash.imx8.pico-8m.pi.dual-sn65dsi84
    ```
    <br/>

* Get the NXP restricted extra packages (recommended):
    ```shell
    $ merge_restricted_extras
    (sometimes could be stocking on the waiting github response, please try again)
    ```

* For a full clean build:
    ```shell
    $ cook -j<N> (N is up to cors numbers on your host PC)
    ```

* For an incremental build:
    ```shell
    $ heat -j<N> (N is up to cors numbers on your host PC)
    ```

* For clean the all build files:
    ```shell
    $ throw
    ```

* To Configuration in Linux Kernel part:
    ```shell
    $ cd vendor/nxp-opensource/kernel_imx/
    $ recipe (or make menuconfig)
    ```

***
### Flashing The Output Images

Output relative image files of path:

```shell
$ ls <source>/out/target/product/pico_8m/
```

Quick way for flashing to board:

```shell
$ flashcard /dev/sd<x> (x is up to your device node)
```

About how to mount your board as mass storage, please refer:
* [HERE](https://github.com/TechNexion/u-boot-edm/wiki/Use-mfgtool-to-flash-eMMC)

***
### Enabling WiFi/BT Function

**Prepare WiFi/BT firmware**

This SDK is supporting Qualcomm(QCA) WLAN module - QCA9377 as default configuration, Because of the license restriction, please contact TechNexion FAE or Sales to get licensed firmware files.

    Contact Window: <EMAIL>

After getting the WiFi firmware binary, decompress the tarball and put all the WiFi firmware files into 

```shell
<source folder>/device/fsl/pico_8m/wifi-firmware/
```

Then take the QCA9377 folder as target path such as:

```shell
<source folder>/device/fsl/pico_8m/wifi-firmware/QCA9377
```

After getting the BT firmware binary, decompress the tarball and put all the BT(Bluetooth) firmware files into 

```shell
<source folder>/device/fsl/pico_8m/bluetooth/nvm_tlv_3.2.bin
<source folder>/device/fsl/pico_8m/bluetooth/rampatch_tlv_3.2.tlv
```

Issue the command cook/heat again as previous Chapter "[Compiling Environment Setup](#compiling-environment-setup)", WiFi/BT function will be working! Enjoy!

***
### Frequently Asked Questions

**1. jack-server compile error base on Ubuntu 20.04**

Due to Google already stop update Android 8, it will cause compile error if users adapt newer openjdk revision such as Ubuntu 20.04 or above.

Users  have two methods to fix this issue, one is refer docker way of [chapter Compiling Environment Setup](#compiling-environment-setup), that container is base on Ubuntu 16.04, another way is users need modify security configuration of openjdk using Ubuntu 20.04 host OS as follows:

Step 1: edit /etc/java-8-openjdk/security/java.security

Step 2. Disable TLSv1, TLSv1.1 protocols for Android 8 like this:
```
jdk.tls.disabledAlgorithms=SSLv3, RC4, DES, MD5withRSA, \
    DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, \
    include jdk.disabled.namedCurves
```

Step 3. Restart jack-server
```
cd <source>/prebuilts/sdk/tools/
./jack-admin kill-server && ./jack-admin start-server
```

Step4. Recompile again, it should works.

