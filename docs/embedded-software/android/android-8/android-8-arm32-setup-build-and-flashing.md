---
title: Setup, Build, and Flashing for ARM32 Systems
description: Android 8 Setup, Build, and Flashing for ARM32 Systems
sidebar_position: 1
---
### Download The Source code

Github way (prepare repo command first is recommended)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-o8.0.0_1.0.0-ga
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```
    
How to download repo manually:
```shell
$ curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
$ chmod a+x repo
$ sudo mv repo /usr/bin/repo
```

### Compiling Environment Setup

General packages installation ( Ubuntu 16.04 or above)

```shell
$ sudo apt-get install uuid uuid-dev zlib1g-dev liblz-dev liblzo2-2 liblzo2-dev lzop \
git-core curl u-boot-tools mtd-utils android-tools-fsutils device-tree-compiler gdisk \
gnupg flex bison gperf build-essential zip curl zlib1g-dev gcc-multilib g++-multilib \
libc6-dev-i386 lib32ncurses5-dev x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev \
libxml2-utils xsltproc unzip sshpass ssh-askpass zip xz-utils kpartx vim screen sudo wget \
bc locales openjdk-8-jdk rsync docker.io
```

Technexion Docker image generation

```shell
$ cd cookers
$ docker build -t build_droid8 .
$ sudo docker run --privileged=true --name mx8_build  -v /home/<USER>/<source folder>:/home/<USER>
(first time)

$ sudo docker ps -a
$ sudo docker start <your container id>
$ sudo docker exec -it mx8_build bash
(after first time)
```

### Starting Compile The Source Code

* Source the compile relative commands (SoM product line):

    **EDM1-IMX6 onto FAIRY: HDMI**
    ```shell
    $ source cookers/env.bash.imx6.edm1cf-pmic.fairy.hdmi
    ```
    <br/>

    **EDM1-IMX6 with PMIC onto FAIRY: 7-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6.edm1cf-pmic.fairy.lcd
    ```
    <br/>

    **PICO-IMX6 onto DWARF: HDMI**
    ```shell
    $ source cookers/env.bash.imx6.pico.dwarf.hdmi
    ```
    <br/>

    **PICO-IMX6 onto DWARF: 7-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6.pico.dwarf.lcd
    ```
    <br/>

    **PICO-IMX6 onto HOBBIT: 7-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6.pico.hobbit.lcd
    ```
    <br/>

    **PICO-IMX6 onto NYMPH: HDMI**
    ```shell
    $ source cookers/env.bash.imx6.pico.nymph.hdmi
    ```
    <br/>

    **PICO-IMX6 onto NYMPH: 7-inch LVDS (1024x600 resolution via LVDS interface)**
    ```shell
    $ source cookers/env.bash.imx6.pico.nymph.lvds
    ```
    <br/>

    **PICO-IMX6 onto NYMPH: VGA**
    ```shell
    $ source cookers/env.bash.imx6.pico.nymph.vga
    ```
    <br/>

    **PICO-IMX6 onto PI: 7-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx6.pico.pi.lcd
    ```
    <br/>

    **PICO-IMX7 onto DWARF: 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx7.pico.dwarf.lcd
    ```
    <br/>

    **PICO-IMX7 onto HOBBIT: 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx7.pico.hobbit.lcd
    ```
    <br/>

    **PICO-IMX7 onto NYMPH: VGA**
    ```shell
    $ source cookers/env.bash.imx7.pico.nymph.vga
    ```
    <br/>

    **PICO-IMX7 onto PI: 5-inch LCD (800x480 resolution via LCD interface)**
    ```shell
    $ source cookers/env.bash.imx7.pico.pi.lcd
    ```
    <br/>

* Source the compile relative commands (HMI product line):

    **i.MX6 TC0700 (7-inch 1024x600 resolution Panel PC)**
    ```shell
    $ source cookers/env.bash.imx6.edm1cf-pmic.fairy.tc0700
    ```
    <br/>

    **i.MX6 TC01000 (10-inch 1280x800 resolution Panel PC)**
    ```shell
    $ source cookers/env.bash.imx6.edm1cf-pmic.fairy.tc1000
    ```
    <br/>

    **i.MX7 TEP1 (5-inch 800x480 Panel PC)**
    ```shell
    $ source cookers/env.bash.imx7.tep1.tep1.lcd
    ```
    <br/>

* Source the compile relative commands (Wandboard community):
  
    **Wandboard(i.MX6): HDMI**
    ```shell
    $ source cookers/env.bash.imx6.edm1cf-pmic.wandboard.hdmi
    ```
    <br/>

    **Wandboard(i.MX6): 5-inch 800x480 LCD panel**
    ```shell
    $ source cookers/env.bash.imx6.edm1cf-pmic.wandboard.lcd
    ```
    <br/>

* Get the NXP restricted extra packages (recommended):
    ```shell   
       $ merge_restricted_extras
       (sometimes could be stocking on the waiting github response, please try again)
    ```

* For a full clean build:
    ```shell
    $ cook -j<N> (N is up to cors numbers on your host PC)
    ```

* For an incremental build:
    ```shell
    $ heat -j<N> (N is up to cors numbers on your host PC)
    ```

* For clean the all build files:
    ```shell
    $ throw
    ```

* To configuration in Linux Kernel part:
    ```shell
    $ cd vendor/nxp-opensource/kernel_imx/
    $ recipe (or make menuconfig)
    ```

***
### Flashing The Output Images

Output relative image files of path:

```shell
$ ls <source>/out/target/product/<your target platform>/
```

Quick way for flashing to board:

```shell
$ flashcard /dev/sd<x> (x is up to your device node)
```

About how to mount your board as mass storage, please refer:
* [HERE](https://github.com/TechNexion/u-boot-edm/wiki/Use-mfgtool-to-flash-eMMC)

***
### Enabling WiFi/BT function

**Prepare WiFi/BT firmware**

This BSP is supporting Qualcomm(QCA) WLAN module - QCA9377 as default configuration, Because of the license restriction, please contact TechNexion FAE or Sales to get licensed firmware files.

Contact Window: <EMAIL>

After getting the firmware binary: .. Decompress the tarball and put all the firmware files into

```shell
<source folder>/device/fsl/<your target platform>/wifi-firmware/
```

Then take the QCA9377 folder as target path such as:

```shell
<source folder>/device/fsl/<your target platform>/wifi-firmware/QCA9377
```

Issue the command cook/heat again as previous Chapter "[Compiling Environment Setup](#compiling-environment-setup)", WiFi/BT function will be working! Enjoy!

***
### Frequently Asked Questions

**1. jack-server compile error base on Ubuntu 20.04**

Due to Google already stop update Android 8, it will cause compile error if users adapt newer openjdk revision such as Ubuntu 20.04 or above.

Users  have two methods to fix this issue, one is refer docker way of [ chapter Compiling Environment Setup](#compiling-environment-setup), that container is base on Ubuntu 16.04, another way is users need modify security configuration of openjdk using Ubuntu 20.04 host OS as follows:

Step 1: edit /etc/java-8-openjdk/security/java.security

Step 2. Disable TLSv1, TLSv1.1 protocols for Android 8 like this:
```
jdk.tls.disabledAlgorithms=SSLv3, RC4, DES, MD5withRSA, \
    DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, \
    include jdk.disabled.namedCurves
```

Step 3. Restart jack-server
```
cd <source>/prebuilts/sdk/tools/
./jack-admin kill-server && ./jack-admin start-server
```

Step4. Recompile again, it should works.