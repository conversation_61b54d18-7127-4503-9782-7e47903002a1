---
title: Custom Configuration
description: Android 11 Custom Configuration
sidebar_position: 1
---
### Device Tree Overlay Configuration

Technexion implement a device-tree overlay method using AVB 3.0.

#### Example: runtime stage
Please boot up into u-boot prompt, and issue commands as follows example:

Overlay vl10112880 lvds panel
```shell
u-boot=> setenv overlay_name 'lvds_10'
```

Overlay vl215192108 lvds panel
```shell
u-boot=> setenv overlay_name 'lvds_21'
```

Change back original device tree without overlay (HDMI)
```shell
u-boot=> setenv overlay_name 'no_overlay'
```

save your overlay configuration for every boot later, if you just want to boot current setting once, you can ignore this step.
```shell
u-boot=> saveenv
```
    
boot into Android system
```shell
u-boot=> boot
```

Overlay Table

| platform | overlay function | overlay_name name | default overlay_name |
|:---|:---|:---|:---|
|EDM-G-IMX8M-PLUS| VL10112880 10" LVDS panel <br/> Vl215192108 21" LVDS panel <br/> TEVI-OV5640 <br/> TEVI-AR0521 <br/> TEVI-AR0144 <br/> TEVI-AR0234 | lvds_10 <br/> lvds_21 <br/> cam_ov5640 <br/> cam_ar0521 <br/> cam_ar0144 <br/> cam_ar0234 | cam_ov5640
|EDM-G-IMX8M-MINI| VL10112880 10" LVDS panel <br/> VL215192108 21" LVDS panel <br/> TEVI-OV5640 | lvds_10 <br/> lvds_21 <br/> cam_ov5640 | lvds_10 cam_ov5640 |
|PICO-IMX8M|  ILI9881C 5" MIPI LCD <br/> G101UAN02 10" MIPI LCD<br/> TEVI-OV5640 <br/> OV5645 <br/> CLIX_NFC <br/> MIPI to HDMI <br/> MIPI to VL10112880 10" LVDS pnael <br/> MIPI to VL15613676 15" LVDS pnael <br/> MIPI to VL215192108 21" LVDS pnael <br/>  | mipi_5 <br/> mipi_10 <br/> cam_ov5640 <br/> cam_ov5645 <br/> clix1_nfc <br/> mipi_to_hdmi <br/> mipi_to_lvds_10 <br/> mipi_to_lvds_15 <br/> mipi_to_lvds_21 | cam_ov5640|
|PICO-IMX8M-MINI|  ILI9881C 5" MIPI LCD <br/> G101UAN02 10" MIPI LCD<br/> TEVI-OV5640 <br/> OV5645 <br/> CLIX_NFC <br/> MIPI to HDMI <br/> MIPI to VL10112880 10" LVDS pnael <br/> MIPI to VL15613676 15" LVDS pnael <br/> MIPI to VL215192108 21" LVDS pnael <br/> |  mipi_5 <br/> mipi_10 <br/> cam_ov5640 <br/> cam_ov5645 <br/> clix1_nfc <br/> mipi_to_hdmi <br/> mipi_to_lvds_10 <br/> mipi_to_lvds_15 <br/> mipi_to_lvds_21   | mipi_5 cam_ov5640 |

#### Example: compile stage
You also can fixed your overlay setting in u-boot header file according your target board such as: 

```shell
<source>/vendor/nxp-opensource/uboot-imx/include/configs/<your board>_android.h
"overlay_name=<change to what you want>\0"» \
```
---

### Change Boot Arguments
According to your device, you might need to change properties let your system performance better, list common properties as follows:

* **screen rotation**<br/>
Because of some DRM-panel driver has clock issue in GKI kernel, so we provide standard hwrotation property or a workaround "swrotation" property in u-boot arguments according to your platform:<br/>
**EDM-G-IMX8M-PLUS = hwrotation (0, 90, 180, 270)**<br/>
**EDM-G-IMX8M-MINI = N/A**<br/>
**PICO-IMX8M =  swrotation(0, 90)**<br/>
Note that if you use PICO-IMX8M with Camera and you would like to rotate 90 angle in camera preview mode, you can set swrotation=90 to implement it.<br/>
**PICO-IMX8M-MINI = N/A**<br/>

* **cma size = cma**
* **resolution = displaymode**
* **WiFi country code for AP mode =wificountrycode**
* **display density =lcd_density**


About CMA size, it's up to your DRAM size for each platforms, please modify CMA size as follows table:
| platform | DRAM size | recommend properties | default properties |
|:---|:---|:---|:---|
|EDM-G-IMX8M-PLUS| 1. 1GiB <br/> 2. 2GiB and above  | 1. cma=320M@0x400M-0xb80M androidboot.displaymode=720p galcore.contiguousSize=33554432 <br/> 2. cma=800M@0x400M-0xb80M | cma=800M@0x400M-0xb80M |
|EDM-G-IMX8M-MINI| 1. 1GiB <br/> 2. 2GiB and above  | 1. cma=480M@0x400M-0xb80M <br/>2. cma=800M@0x400M-0xb80M | cma=480M@0x400M-0xb80M |
|PICO-IMX8M| 1. 1GiB <br/> 2. 2GiB and above  |  1. cma=385M androidboot.displaymode=720p <br/>2. cma=736M | cma=736M |
|PICO-IMX8M-MINI|  1. 1GiB <br/> 2. 2GiB and above  |  1. cma=400M@0x400M-0xb80M <br/> 2. cma=800M@0x400M-0xb80M |  cma=400M@0x400M-0xb80M |

#### Example: runtime stage

EDM-G-IMX8M-PLUS

```shell
u-boot=> setenv bootargs console=ttymxc1,115200 init=/init androidboot.console=ttymxc1 androidboot.hardware=nxp androidboot.hwrotation=0 firmware_class.path=/vendor/firmware loop.max_part=7 transparent_hugepage=never swiotlb=65536 androidboot.vendor.sysrq=1 androidboot.lcd_density=240 androidboot.primary_display=imx-drm androidboot.wificountrycode=TW androidboot.usb.debugging=1 cma=800M@0x400M-0xb80M quiet
u-boot=> saveenv
u-boot=> boot
```

EDM-G-IMX8M-MINI

```shell
u-boot=> setenv bootargs console=ttymxc1,115200 init=/init androidboot.console=ttymxc1 androidboot.hardware=nxp firmware_class.path=/vendor/firmware loop.max_part=7 transparent_hugepage=never androidboot.vendor.sysrq=1 androidboot.lcd_density=240 androidboot.primary_display=imx-drm androidboot.wificountrycode=TW androidboot.usb.debugging=1 cma=480M@0x400M-0xb80M quiet
u-boot=> saveenv
u-boot=> boot
```

EDM-G-IMX8M-NANO

```shell
u-boot=> setenv bootargs console=ttymxc1,115200 init=/init androidboot.console=ttymxc1 androidboot.hardware=nxp firmware_class.path=/vendor/firmware loop.max_part=7 transparent_hugepage=never androidboot.vendor.sysrq=1 androidboot.lcd_density=240 androidboot.primary_display=imx-drm androidboot.wificountrycode=TW androidboot.usb.debugging=1 cma=800M@0x400M-0xb80M quiet
u-boot=> saveenv
u-boot=> boot
```

PICO-IMX8M

```shell
u-boot=> setenv bootargs console=ttymxc0,115200 init=/init androidboot.console=ttymxc0 androidboot.hardware=nxp androidboot.hwrotation=0 firmware_class.path=/vendor/firmware loop.max_part=7 androidboot.fbTileSupport=enable transparent_hugepage=never androidboot.vendor.sysrq=1 androidboot.lcd_density=240 androidboot.primary_display=imx-dcss androidboot.displaymode=1080p androidboot.wificountrycode=TW androidboot.usb.debugging=1 cma=736M quiet
u-boot=> saveenv
u-boot=> boot
```

PICO-IMX8M-MINI

```shell
u-boot=> setenv bootargs console=ttymxc1,115200 init=/init androidboot.console=ttymxc1 androidboot.hardware=nxp androidboot.swrotation=90 firmware_class.path=/vendor/firmware loop.max_part=7  transparent_hugepage=never androidboot.vendor.sysrq=1 androidboot.lcd_density=240 androidboot.primary_display=imx-drm androidboot.wificountrycode=TW androidboot.usb.debugging=1 cma=400M@0x400M-0xb80M quiet
u-boot=> saveenv
u-boot=> boot
```

#### Example: compile stage
You also can fixed your overlay setting in u-boot header file according your target board such as: 

```shell
<source>/vendor/nxp-opensource/uboot-imx/include/configs/<your board>_android.h
"bootargs=<change to what you want>\0"» \
```
---

### Enableing NFC function
 
Users can enable NFC function in < source >/cookers/env.bash (default is disabled except for EDM-G-IMX8M-PLUS), then re-compile again:

```shell
export NFC_ACTIVE=false
```
to
```shell
export NFC_ACTIVE=true
```
---
### Enableing 5GNR function
 
**Support platform: EDM-G-IMX8M-PLUS**

Step 1: Prepare SIM8202 prebuilt Radio Interface Layer (RIL) libraries

This BSP does support SIMCOM 5GNR module - SIM8202G-M2, but because of the license restriction, please contact TechNexion FAE or Sales to get licensed RIL relate files, default is disabled.

Contact Window: <EMAIL>

After getting the RIL files, please decompress the tarball and put modem folder into specific device folder as follows:

```shell
$ cp -rv modem <source folder>/device/nxp/edm_g_imx8mp
```

Step 2. Enable SIM8202 configuration

Users need enable 5gnr function in < source >/cookers/env.bash (default is disabled, note that modify top SIM8202_MODEM_ACTIVE parameter is enough), then re-compile again:

```shell
export SIM8202_MODEM_ACTIVE=false
```
    to
```shell
export SIM8202_MODEM_ACTIVE=true
```
---

### LIBGPIOD JNI APIs
 
Technexion provide a demo app about libgpiod JNI Test, specific source code as following:
* [source code](https://github.com/technexion-android/packages_apps_GpiodJniTest.git)

Users can implement own GUI using our INPUT/OUTPUT APIs

Setting GPIO as output with specific value:
```c
public native String  setGpioInfo(int gpiobank,int gpioline, int value);
```

Setting GPIO as input and get a value:
```c
public native String  getGpioInfo(int gpiobank,int gpioline);
```
---
### New OTA system: systemupgrade

Our Android 11 does support full update and incremental update, please contact our window to get detail instruction:<br/>
<EMAIL>

---
### TensorFlow-Lite for Machine Learning

EDM-G-IMX8MP has a NPU (Neural Processing Unit) inside the SoC for machine learning applications, it has a very good performance using TFL (tensorflow-lite), Technexion provide two simple TFL apps let users testing as follows steps:<br/>
Note that other platforms

Image classification using CPU/NPU

```shell
$ wget https://download.technexion.com/development_resources/NXP/android/11.0/machine-learning_demo/TFL-ImageClassify.zip
    
$ unzip TFL-ImageClassify.zip
```

Speech commands using CPU

```shell
$ wget https://download.technexion.com/development_resources/NXP/android/11.0/machine-learning_demo/TFL-SpeechCommand.zip
$ unzip TFL-SpeechCommand.zip
```

Quick install to Android image

```shell
$ adb install TFL-ImageClassify.apk
$ adb install TFL-SpeechCommand.apk
```

Build-in to Technexion Android BSP

```shell
$ mkdir -p <source code>/packages/apps/TFL-ImageClassify
$ mv TFL-ImageClassify.apk Android.mk <source code>/packages/apps/TFL-ImageClassify
$ cd <source code>/packages/apps/TFL-ImageClassify
$ mm
$ cd -
$ cook -j4

$ mkdir -p <source code>/packages/apps/TFL-SpeechCommand
$ mv TFL-ImageClassify.apk Android.mk <source code>/packages/apps/TFL-SpeechCommand
$ cd <source code>/packages/apps/TFL-SpeechCommand
$ mm
$ cd -
$ cook -j4
```

****