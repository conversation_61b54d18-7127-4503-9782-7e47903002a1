---
title: Setup, Build, and Flashing
description: Android 11 Setup, Build, and Flashing
sidebar_position: 0
---
### Download The Source Code
Github way (Prepare repo command first is recommended)

Install repo first:

```shell
$ curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
$ chmod a+x repo
$ sudo mv repo /usr/bin/repo
```

Download latest LTS release (LTS branch):

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-android-11.0.0_1.2.0_8m-next
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```

Download latest stable release: TN2.0 (20211014)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-android-11.0.0_1.2.0_8m-ga-tn2.0
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```

Download earlier stable release: TN1.0 (20210607)

```shell
$ repo init -u https://github.com/technexion-android/manifest -b tn-android-11.0.0_1.2.0_8m-ga-tn1.0
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```

***
### Compiling Environment Setup
 
There are two different methods you can use to set up the build environment. One is to install the required packages onto your host filesystem.<br/>
Another is to use a docker container, where the installation of the required packages is automated for you.

General Packages Installation ( Ubuntu 16.04 or above, note that some packages are different name with Ubuntu 20.04 )

```shell
$ sudo apt-get install uuid uuid-dev zlib1g-dev liblz-dev liblzo2-2 liblzo2-dev lzop \
git-core curl u-boot-tools mtd-utils android-tools-fsutils device-tree-compiler gdisk \
gnupg flex bison gperf build-essential zip curl zlib1g-dev gcc-multilib g++-multilib \
libc6-dev-i386 lib32ncurses5-dev x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev \
libxml2-utils xsltproc unzip sshpass ssh-askpass zip xz-utils kpartx vim screen sudo wget \
bc locales openjdk-8-jdk rsync docker.io python3 kmod cgpt bsdmainutils lzip hdparm libssl-dev cpio
```

Or adapt Docker Container based compile environment (Optional)

```shell
$ cd cookers
$ docker build -t build_droid10 .
$ sudo docker run --privileged=true --name mx8_build  -v /home/<USER>/<source folder>:/home/<USER>
(first time)

$ sudo docker ps -a
$ sudo docker start <your container id>
$ sudo docker exec -it mx8_build bash (after first time)
```

***
### Starting Compile The Source Code

* Source the compile relative commands:

    **For EDM-G-IMX8MP + WANDBOARD (HDMI)**

    Overlayable displays: HDMI, LVDS: VL10112880, VL215192108
    ```shell
    $ source cookers/env.bash.imx8.edm-g-imx8mp.wandboard.hdmi
    ```
    <br/>

    **For EDM-G-IMX8MM + WANDBOARD (LVDS VL10112880 10" LVDS panel)**

    Overlayable displays: LVDS: VL10112880, VL215192108<br/>
    Overlayable accessories: TEVI-OV5640
    ```shell
    $ source cookers/env.bash.imx8.edm-g-imx8mm.wandboard.lvds_vl10112880
    ```
    <br/>

    **For PICO-IMX8M + PI (HDMI)**

    Overlayable accessories: TEVI-OV5640, OV5645, CLIX-NFC
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m.pi.hdmi
    ```
    <br/>

    **For PICO-IMX8M + WIZARD (HDMI)**

    Overlayable accessories: TEVI-OV5640, OV5645, CLIX-NFC
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8m.wizard.hdmi
    ```
    <br/>

    **For PICO-IMX8M-MINI + PI (MIPI-DSI ILI9881C 5" MIPI panel)**

    Overlayable accessories: TEVI-OV5640, OV5645, CLIX-NFC
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8mm.pi.mipi-dsi_ili9881c
    ```
    <br/>

    **For PICO-IMX8M-MINI + WIZARD (MIPI-DSI ILI9881C 5" MIPI panel)**

    Overlayable accessories: TEVI-OV5640, OV5645, CLIX-NFC
    ```shell
    $ source cookers/env.bash.imx8.pico-imx8mm.wizard.mipi-dsi_ili9881c
    ```
    <br/>

* Get the NXP restricted extra packages (recommended):

    ```shell
    $ merge_restricted_extras
    ```
    sometimes could be stocking on the waiting github response, please try again.<br/>
    **Note:** that it will showing up a EULA message before merge packages, please type 'yes' to continue the process as follows:

    ```shell
    Could you agree this EULA and keep install packages?yes
    ```

* For a full build:

    ```shell
    $ cook -j<N> (N is up to cors numbers on your host PC)
    ```

* For clean the all compiled files:

    ```shell
    $ throw
    ```

***
### Flashing The Output Images

Output relative image files of path:

```shell
$ ls <source>/out/target/product/<target board>/ (edm_g_imx8mp or others)
```

#### uuu way (recommended)
Step 1. Download uuu tool first:
* [NXP uuu release](https://github.com/NXPmicro/mfgtools/releases)
* Technexion uuu release: https://download.technexion.com/development_resources/development_tools/installer/imx-mfg-uuu-tool_20200629.zip

About Technexion uuu Detial:
* [HERE](https://github.com/TechNexion/u-boot-edm/wiki/Use-mfgtool-%22uuu%22-to-flash-eMMC)

Step 2. Then install uuu to different host environment:

* [Refer "UUU environment settings" on README file of demo image](https://raw.githubusercontent.com/technexion-android/Documents/android-11/txt/README.txt)

Step 3. Quick way for flashing to board (adapt uuu based flash script):

* Ubuntu host:
    ```shell
    $ cd <source>/out/target/product/<target board>/
    $ sudo ./uuu_imx_android_flash.sh -f <platform_name> -a -e -c <eMMC_size> -D .
    (platform_name is up to your SoC platform of device, such as imx8mp, imx8mm, imx8mq, imx8mn)
    (eMMC_size is up to your eMMC size, 16GB: eMMC_size=13, 32GB: eMMC_size=28, minimal 9GB size for demo: eMMC_size=9)
    ```

* Example:
    ```shell
    $ sudo ./uuu_imx_android_flash.sh -f imx8mp -a -e -c 28 -D .
    ```

* Windows example:
    ```shell
    $ uuu_imx_android_flash.bat -f <platform_name> -a -e -c 28
    ```

:::info Note: 
**Steps for boot mode change when flash the image:**<br/>
Firstly, the user must be change the boot mode to serial download mode and connect a OTG cable from board to host PC. Then, running the uuu commands as above post. In the end, change back the boot mode to eMMC boot mode, that's it.
:::
<br/>
 
#### big image way (easier but spend much time for image flashing)

Step 1. Source the compile relative commands first.<br/>
Step 2. issue command to auto generate a local image for flashing

```shell
$ gen_local_images <image_size>
```
(eMMC_size is up to your eMMC size, 16GB: image_size=13, 32GB: image_size=28)

Step 3. You'll see a test.img in `<source>/out/target/product/<target board>/`<br/>
Step 4. You can use flash this image to eMMC using uuu tool, ums or other classic ways.<br/>
**Note:** users need change to serial download mode if adapt uuu tool, and ums just keep eMMC boot mode is enough.
<br/>

#### Generate mass production image folder

Another situation is when you need move your image flash tool to factory side for MP, you can do these steps:

Step 1. issue command to generate a protable MP image folder, the output folder name is 'auto_test'

```shell
$ gen_mp_images
```

Step 2. compress auto_test folder to zip file, then you can take it to factory.<br/>
Step 3. prepare computers for MP, install Ubuntu host PC with require packages as Compiling-Environment-Setup chapter.<br/>
Step 4. extract auto_test.zip, then issue flash command as uuu way commands to flash image to target boards.<br/>

Technexion also provide a quicker way for MP stage, welcome to send <NAME_EMAIL> if you have interesting.

***
### Enabling WiFi/BT function
 
#### Prepare WiFi/BT firmware

This BSP does support Qualcomm(QCA) WLAN module - QCA9377 as default configuration, Because of the license restriction, please contact TechNexion FAE or Sales to get licensed firmware files, default is disabled.

Contact Window: <EMAIL>

#### WiFi part

After getting the firmware binary: .. Decompress the tarball and put all the firmware files into a created 'wifi-firmware' folder such as

```shell
<source folder>/device/nxp/imx8m/edm_g_imx8mp/wifi-firmware/
```

Then take the qca9377 and wlan folder to the specific path such as:

```shell
$ cp -rv qca9377/ <source folder>/device/nxp/edm_g_imx8mp/wifi-firmware/
$ cp -rv wlan/ <source folder>/device/nxp/edm_g_imx8mp/wifi-firmware/qca9377/
```
 
#### Bluetooth part

After getting the firmware binary: .. Decompress the tarball and put all the firmware files to specific path such as:

```shell
$ cp -rv qca/ <source folder>/device/nxp/edm_g_imx8mp/bluetooth/
```

Issue the command cook again as previous Chapter "Compiling Environment Setup", WiFi/BT function will be packaged in output image!