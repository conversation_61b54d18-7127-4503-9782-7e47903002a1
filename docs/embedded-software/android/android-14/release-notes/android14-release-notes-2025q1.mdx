---
title: Release Notes 2025Q1
description: Add document description here
sidebar_position: 9
---
## Android 14 2025 Release Notes

### Links for source repositories
AOSP (android-14.0.0) + U-boot (2024.04) + <PERSON><PERSON> (6.6.36)
Tag: tn-android-14-2025Q1
| |Branch|Version|CoimmitID|
|:---|:---|:---|:---|
| <PERSON><PERSON> | [tn-android-14.0.0_2.2.0](https://github.com/technexion-android/platform_device_fsl/tree/tn-android-14.0.0_2.2.0)|14.0.0_r55|f3a0d3d41cc126aa1ec002de3734db10d038a31b|
| U-boot | [tn-android-14_2.2.0_2024.04](https://github.com/technexion-android/uboot-imx/tree/tn-android-14_2.2.0_2024.04)|2024.04|d386db156a973e4b6a5030bc7bbf0f21b7d1e06d|
| <PERSON><PERSON> | [tn-android-14_2.2.0_6.6.36](https://github.com/technexion-android/kernel_imx/tree/tn-android-14_2.2.0_6.6.36)|6.6.36|1ac33dd46e8298f75b958190e44f71e7836a7ced|

<img src="/img/A14-6.6.36.png" alt="A14-6.6.36.png" style={{ maxWidth: '70%' }} />


---

### Supported Platforms in this Release

<!--
Please refer to Yocto release notes
Example [Release Notes: YP5.0 2024Q4](https://developer.technexion.com/docs/release-notes-yp50-2024q4)
-->

import CopyableText from '@site/src/components/CopyableText.jsx';

#### SOM
<table
  style={{
    fontFamily: "Arial",
    fontSize: 14,
    height: 600,
    overflowX: "auto",
    overflow: "auto",
    display: "flexbox",
    minWidth: "100%"
  }}
  border={0}
>
  <thead style={{ position: "sticky", top: 0, zIndex: 2 }}>
    <tr style={{ height: 26, backgroundColor: "#ffce93" }}>
      <td
        style={{
          backgroundColor: "#f9f801",
          position: "sticky",
          left: 0,
          zIndex: 3
        }}
      ><strong> SOM </strong></td>
      <td
        style={{ backgroundColor: "#f9c7f9", textAlign: "center" }}
        colSpan={2}
        nowrap="nowrap"
      ><strong> EDM-G-IMX8MP </strong></td>
    </tr>
    <tr
      style={{
        height: 26,
        backgroundColor: "#fae197",
        position: "sticky",
        top: 0
      }}
    >
      <td style={{ position: "sticky", left: 0, zIndex: 3 }}>
        <b>Baseband</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDM-G-WB</b>
      </td>
      <td style={{ minWidth: 150 }}>
        <b>EDB-G-WIZARD</b>
      </td>
    </tr>
  </thead>
  <tbody>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Wireless</td>
      <td style={{ textAlign: "center" }} colSpan={2}>QCA9377-3 / IW416<br />BT 5.0</td>
    </tr>
    <tr style={{ height: 26, backgroundColor: "#fae197" }}>
      <td style={{ position: "sticky", left: 0, zIndex: 1 }} nowrap="nowrap">
        <b> Panel support</b>{" "}
      </td>
      <td style={{ height: 26 }} colSpan={7}>&nbsp;</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >HDMI</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
        nowrap="nowrap"
      >MIPI to HDMI</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
        nowrap="nowrap"
      >MIPI panel 5 inch</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >LVDS 10.1 inch</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >LVDS 15.6 inch</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >LVDS 21.5 inch</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 10.1 inch panel</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 15.0 inch panel</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >Viizionpanel with 15.6 inch panel</td>
      <td>NA</td>
      <td>NA</td>
    </tr>
    <tr style={{ height: 26, backgroundColor: "#fae197" }}>
      <td style={{ position: "sticky", left: 0 }}>
        <b> Camera</b>{" "}
      </td>
      <td style={{ height: 26 }} colSpan={7}>&nbsp;</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >TEVS-AR Camera</td>
      <td>Yes</td>
      <td>Yes</td>
    </tr>
    <tr style={{ height: 26 }}>
      <td
        style={{
          backgroundColor: "antiquewhite",
          position: "sticky",
          left: 0,
          zIndex: 1
        }}
      >VLS3-AR Camera</td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
      <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
    </tr>
  </tbody>
</table>

:::info notes
#### Device Tree Overlay Configuration
Technexion implement a device-tree overlay method using AVB 3.0.

For panel and camera detection, manual setup via the U-Boot prompt is required as indicated by 'Yes' in the support table, along with the corresponding U-Boot commands. Get U-Boot commands with expanding toggle symbol `▶`. Proceed to boot into the U-Boot prompt and execute these commands.

Certain peripherals feature auto-detection, denoted solely by 'Yes' in the support table. No manual U-Boot intervention is necessary for these devices.

**Example**
- Using Vizionlink with TEVS-AR (VLS3-AR) camera on edm-g-imx8mp, please type command in u-boot:
```
u-boot=> setenv dtoverlay vls
```
- Using TEVS-AR camera on edm-g-imx8mp, no need to set `dtoverlay` env in u-boot.
:::
<br/>

* * *
#### Wifi/BT module
- i.mx8 supports QCA9377/IW416 module

  <img src="/img/Android-wifi-bt.png" alt="A14-wifi-bt.png" style={{ maxWidth: '70%' }} />

#### Camera module
- i.mx8 only supports [TEVS-AR series camera](/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-4.2-mickledore-release/edm-g-imx8m-tevs-camera-usage-guide.md) modules

  <img src="/img/Android-camera.png" alt="A14-vls.png" style={{ maxWidth: '70%' }} />


* * *


### Latest Update log
**tn-android-14.0.0_2.2.0**

2025/1/15
 - edm-g-imx8mp the first release

<!--
#### ToDo list
- vizionpanel support on wizard board
- performance enhance
- GPIOD application
-->

#### Known issue
- When get permission denied issue, please try "setenforce 0".

#### Limitation
- QCA9377 do not support BT APCF (Address Packet Count Filtering) feature.

* * *

### Prebuilt Images
Prebuilt images are avialable for download via TechNexion's server.
|SOM|Set|
|:---|:---|
|EDM-G-IMX8MP|WB [edm-g-imx8mp_edm-g-wb_android-14.0_qca9377_hdmi-1920x1080_20250113.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived//edm-g-imx8mp_edm-g-wb_android-14.0_qca9377_hdmi-1920x1080_20250113.zip)<br/>WIZARD [edm-g-imx8mp_edm-g-wizard_android-14.0_qca9377_hdmi-1920x1080_20250113.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived//edm-g-imx8mp_edm-g-wizard_android-14.0_qca9377_hdmi-1920x1080_20250113.zip)|
