---
title: Release Notes 2025Q2
description: Add document description here
sidebar_position: 8
---

## Android 14 2025 Release Notes

### Links for source repositories
AOSP (android-14.0.0) + U-boot (2024.04) + <PERSON><PERSON> (6.6.36)
Tag: tn-android-14-2025Q2
| |Branch|Version|CoimmitID|
|:---|:---|:---|:---|
| <PERSON><PERSON> | [tn-android-14.0.0_2.2.0](https://github.com/technexion-android/platform_device_fsl/tree/tn-android-14.0.0_2.2.0)|14.0.0_r55|d15a9730eed18aa0ebf92f7159464018f1d89ed3|
| U-boot | [tn-android-14_2.2.0_2024.04](https://github.com/technexion-android/uboot-imx/tree/tn-android-14_2.2.0_2024.04)|2024.04|7fabcb2fc32da87734f131f7fbf5d9a3155a19bb|
| <PERSON><PERSON> | [tn-android-14_2.2.0_6.6.36](https://github.com/technexion-android/kernel_imx/tree/tn-android-14_2.2.0_6.6.36)|6.6.36|f43e68489e74b127e5d6c06c69e74aa8675a9199|

<img src="/img/A14-6.6.36.png" alt="A14-6.6.36.png" style={{ maxWidth: '70%' }} />


---

### Supported Platforms in this Release
<!--
Please refer to Yocto release notes
Example [Release Notes: YP5.0 2025Q2](/docs/embedded-software/linux/yocto/release-notes/release-notes-yp50-2025q2)
-->

import CopyableText from '@site/src/components/CopyableText.jsx';

#### SOM
  <table className="tn-table tn-table-color">
    <thead>
      <tr>
        <td>SOM</td>
        <td>AXON-IMX8MP</td>
        <td colSpan={2} align="center">EDM-G-IMX8MP</td>
        <td colSpan={2} align="center">EDM-G-IMX8MM</td>
        <td colSpan={2} align="center">PICO-IMX8MM</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#fae197" }}>Baseband</td>
        <td>AXON-WIZARD</td>
        <td>EDM-G-WB</td>
        <td>EDB-G-WIZARD</td>
        <td>EDM-G-WB</td>
        <td>EDM-G-WIZARD</td>
        <td>PICO-PI</td>
        <td>PICO-WIZARD</td>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>Wifi driver</td>
        <td>QCA9377-3</td>
        <td>QCA9377-3<br />IW416</td>
        <td>QCA9377-3<br />IW416</td>
        <td>QCA9377-3<br />IW416</td>
        <td>QCA9377-3<br />IW416</td>
        <td>QCA9377-3</td>
        <td>QCA9377-3</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#fae197", fontWeight: 'bold' }}>Panel support</td>
        <td style={{ backgroundColor: "#fae197" }} colSpan={7}>&nbsp;</td>
      </tr>
      <tr>
        <td>HDMI</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
      </tr>
      <tr>
        <td>MIPI to HDMI</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>Yes</td>
      </tr>
      <tr>
        <td>MIPI panel 5 inch</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>NA</td>
        <td>Yes</td>
        <td>Yes</td>
      </tr>
      <tr>
        <td>LVDS 10.1 inch</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>NA</td>
        <td>NA</td>
      </tr>
      <tr>
        <td>LVDS 15.6 inch</td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
        <td>NA</td>
        <td>NA</td>
      </tr>
      <tr>
        <td>LVDS 21.5 inch</td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl215192108"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl215192108"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl215192108"/></td>
        <td>NA</td>
        <td>NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#fae197", fontWeight: 'bold' }}>Camera</td>
        <td style={{ backgroundColor: "#fae197" }} colSpan={7}>&nbsp;</td>
      </tr>
      <tr>
        <td>TEVS-AR Camera</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes</td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay tevs"/></td>
        <td>Yes</td>
        <td>NA</td>
      </tr>
      <tr>
        <td>VLS3-AR Camera</td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
        <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay vls"/></td>
        <td>NA</td>
        <td>NA</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>

:::info notes
#### Device Tree Overlay Configuration
Technexion implement a device-tree overlay method using AVB 3.0.

For panel and camera detection, manual setup via the U-Boot prompt is required as indicated by 'Yes' in the support table, along with the corresponding U-Boot commands. Get U-Boot commands with expanding toggle symbol `▶`. Proceed to boot into the U-Boot prompt and execute these commands.

Certain peripherals feature auto-detection, denoted solely by 'Yes' in the support table. No manual U-Boot intervention is necessary for these devices.

**Example**
- Using Vizionlink with TEVS-AR (VLS3-AR) camera on axon-imx8mp, please type command in u-boot:
```
u-boot=> setenv dtoverlay vls
```
- Using TEVS-AR camera on axon-imx8mp, no need to set `dtoverlay` env in u-boot.
:::
<br/>

* * *

#### Wifi/BT module
- i.mx8 supports QCA9377/IW416 module

  <img src="/img/Android-wifi-bt.png" alt="A14-wifi-bt.png" style={{ maxWidth: '70%' }} />

#### Camera module
- i.mx8 only supports [TEVS-AR series camera](/docs/embedded-vision/tevs/usage-guides/nxp/technexion-development-kits/yocto-4.2-mickledore-release/edm-g-imx8m-tevs-camera-usage-guide.md) modules

  <img src="/img/Android-camera.png" alt="A14-vls.png" style={{ maxWidth: '70%' }} />


* * *


### Latest Update log
**tn-android-14.0.0_2.2.0**

2025/4/15
 - imx8 series release
     - edm-g-imx8mm
     - axon-imx8mp
     - pico-imx8mm
 - GPIOD application is ready

2025/1/15
 - edm-g-imx8mp the first release

<!--
#### ToDo list
- tlv320 support on pico-imx8mm wizard
- tevs support on pico-imx8mm wizard
- vizionpanel support on wizard board
- performance enhance
-->

#### Known issue
- When get permission denied issue, please try "setenforce 0".


#### Limitation
- QCA9377 do not support BT APCF (Address Packet Count Filtering) feature.
- Higher thermal and power consumption on kernel 6.6

* * *

### Prebuilt Images
Prebuilt images are available for download via TechNexion's server.
|SOM|Set|
|:---|:---|
|AXON-IMX8MP|WIZARD [axon-imx8mp_axon-wizard_android-14.0_qca9377_hdmi-1920x1080_20250415.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived//axon-imx8mp_axon-wizard_android-14.0_qca9377_hdmi-1920x1080_20250415.zip)|
|EDM-G-IMX8MP|WB [edm-g-imx8mp_edm-g-wb_android-14.0_qca9377_hdmi-1920x1080_20250415.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived//edm-g-imx8mp_edm-g-wb_android-14.0_qca9377_hdmi-1920x1080_20250415.zip)<br/>WIZARD [edm-g-imx8mp_edm-g-wizard_android-14.0_qca9377_hdmi-1920x1080_20250415.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived//edm-g-imx8mp_edm-g-wizard_android-14.0_qca9377_hdmi-1920x1080_20250415.zip)|
|EDM-G-IMX8MM|WB [edm-g-imx8mm_edm-g-wb_android-14.0_qca9377_lvds-1280x800_20250415.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived//edm-g-imx8mm_edm-g-wb_android-14.0_qca9377_lvds-1280x800_20250415.zip)<br/>WIZARD [edm-g-imx8mm_edm-g-wizard_android-14.0_qca9377_lvds-1280x800_20250415.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived//edm-g-imx8mm_edm-g-wizard_android-14.0_qca9377_lvds-1280x800_20250415.zip)|
|PICO-IMX8MM|PI [pico-imx8mm_pico-pi-8m_android-14.0_qca9377_mipi5-1280x720_20250415.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived//pico-imx8mm_pico-pi-8m_android-14.0_qca9377_mipi5-1280x720_20250415.zip)<br/>WIZARD [pico-imx8mm_pico-pi-wizard_android-14.0_qca9377_mipi5-1280x720_20250415.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived//pico-imx8mm_pico-pi-wizard_android-14.0_qca9377_mipi5-1280x720_20250415.zip)|