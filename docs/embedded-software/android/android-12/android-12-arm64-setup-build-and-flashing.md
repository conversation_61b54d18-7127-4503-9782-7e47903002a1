---
title: Setup, Build, and Flashing
description: Android 12 Setup, Build, and Flashing
sidebar_position: 0
---
### Download The Source Code
Github way (Prepare repo command first is recommended)

Install repo first:

```bash
$ curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > repo
$ chmod a+x repo
$ sudo mv repo /usr/bin/repo
```

Download latest LTS release (LTS branch):

```bash
$ repo init -u https://github.com/technexion-android/manifest -b tn-android-12.0.0_1.0.0_8m-next
$ repo sync -j<N> (N is up to cores numbers on your host PC)
```

### Compiling Environment Setup
 
There are two different methods you can use to set up the build environment. One is to install the required packages onto your host filesystem.<br/>
Another is to use a docker container, where the installation of the required packages is automated for you.<br/>

General Packages Installation (Ubuntu 20.04 or above)

```bash
sudo apt-get install uuid uuid-dev zlib1g-dev liblz-dev liblzo2-2 liblzo2-dev lzop \
git-core curl u-boot-tools mtd-utils android-tools-fsutils device-tree-compiler gdisk \
gnupg flex bison gperf build-essential zip curl zlib1g-dev gcc-multilib g++-multilib \
libc6-dev-i386 lib32ncurses5-dev x11proto-core-dev libx11-dev lib32z-dev libgl1-mesa-dev \
libxml2-utils xsltproc unzip sshpass ssh-askpass zip xz-utils kpartx vim screen sudo wget \
bc locales openjdk-8-jdk rsync docker.io python3 kmod cgpt bsdmainutils lzip hdparm libssl-dev cpio cmake
```

Or adapt Docker Container based compile environment (Optional)

```bash
$ cd cookers
$ docker build -t build_droid12 .
$ sudo docker run --privileged=true --name mx8_build  -v /home/<USER>/<source folder>:/home/<USER>
(first time)

$ sudo docker ps -a
$ sudo docker start <your container id>
$ sudo docker exec -it mx8_build bash (after first time)
```

### Starting Compile The Source Code

Source the compile relative commands:

**For EDM-G-IMX8MP + WANDBOARD (HDMI)**

Overlayable displays: HDMI, LVDS: VL10112880, VL215192108
```bash
$ source cookers/env.bash.imx8.edm-g-imx8mp.wandboard.hdmi
```
---
**For EDM-G-IMX8MP + WIZARD (HDMI)**

Overlayable displays: HDMI, LVDS: VL10112880, VL215192108
```bash
$ source cookers/env.bash.imx8.edm-g-imx8mp.wizard.hdmi
```
---

Get the NXP restricted extra packages (recommended):
```bash
$ merge_restricted_extras
```
sometimes could be stocking on the waiting github response, please try again.
:::info Note
That it will showing up a EULA message before merge packages, please type 'yes' to continue the process as follows:

Could you agree this EULA and keep install packages?yes
:::
<br/>
Get the Technexion firmware for WiFi/Bluetooth functions (recommended):

```shell
$ get_tn_firmware
```
Sometimes could be stocking on the waiting github response, please try again.
It will install to target board of device folder automatically.
<br/>
For a full build:

```shell
$ cook -j<N>
```
 (N is up to cors numbers on your host PC)
 <br/>

For clean the all compiled files:
```shell
$ throw
```

### Flashing The Output Images

Output relative image files of path:

```shell
$ ls <source>/out/target/product/<target board>/ (edm_g_imx8mp or others)
```

#### uuu way (recommended)
Step 1. Download uuu tool first:
* [TechNexion uuu release](https://download.technexion.com/development_resources/development_tools/installer/imx-mfg-uuu-tool.zip)<br/>
    Please download version 1.4.182 or above.<br/>
    Move uuu to /usr/bin/ and give avaliable permission:

    ```shell
    $ chmod a+x /usr/bin/uuu
    ```

    Note that we tested a bug from host PC with up to kernel 5.17, it will cause UUU script cannot work.


About Technexion uuu Detial:
* [HERE](https://github.com/TechNexion/u-boot-edm/wiki/Use-mfgtool-%22uuu%22-to-flash-eMMC)

Step 2. Then install uuu to different host environment:

* [Refer "UUU environment settings" on README file of demo image](https://raw.githubusercontent.com/technexion-android/Documents/android-11/txt/README.txt)

Step 3. Quick way for flashing to board (adapt uuu based flash script):

Ubuntu host:
```bash
$ cd <source>/out/target/product/<target board>/
$ sudo ./uuu_imx_android_flash.sh -f <platform_name> -a -e -c <eMMC_size> -D .
(platform_name is up to your SoC platform of device, such as imx8mp, imx8mm, imx8mq, imx8mn)
(eMMC_size is up to your eMMC size, 16GB: eMMC_size=13, 32GB: eMMC_size=28,)
```

Example:
```bash
$ sudo ./uuu_imx_android_flash.sh -f imx8mp -a -e -c 28 -D .
```

Windows example:
```shell
$ uuu_imx_android_flash.bat -f <platform_name> -a -e -c 28
```

:::info Note
Steps for boot mode change when flash the image:<br/>
Firstly, the user must be change the boot mode to serial download mode and connect a OTG cable from board to host PC. Then, running the uuu commands as above post. In the end, change back the boot mode to eMMC boot mode, that's it.
:::

#### big image way (easier but spend much time for image flashing)

Step 1. Source the compile relative commands first.<br/>
Step 2. issue command to auto generate a local image for flashing

```shell
$ gen_local_images <image_size>
(eMMC_size is up to your eMMC size, 16GB: image_size=13, 32GB: image_size=28)
```

Step 3. You'll see a test.img in `<source>/out/target/product/<target board>/`<br/>
Step 4. You can use flash this image to eMMC using uuu tool, ums or other classic ways.<br/>
**Note:** users need change to serial download mode if adapt uuu tool, and ums just keep eMMC boot mode is enough.


#### Generate mass production image folder

Another situation is when you need move your image flash tool to factory side for MP, you can do these steps:

Step 1. issue command to generate a protable MP image folder, the output folder name is 'auto_test'

```shell
$ gen_mp_images
```

Step 2. compress auto_test folder to zip file, then you can take it to factory.<br/>
Step 3. prepare computers for MP, install Ubuntu host PC with require packages as Compiling-Environment-Setup chapter.<br/>
Step 4. extract auto_test.zip, then issue flash command as uuu way commands to flash image to target boards.<br/>

Technexion also provide a quicker way for MP stage, welcome to send <NAME_EMAIL> if you have interesting.