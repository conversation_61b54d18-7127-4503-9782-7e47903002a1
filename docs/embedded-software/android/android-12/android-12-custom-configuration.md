---
title: Custom Configuration
description: Android 12 Custom Configuration
sidebar_position: 1
---
### Device Tree Overlay Configuration

Technexion implement a device-tree overlay method using AVB 3.0.

#### Example: runtime stage
Please boot up into u-boot prompt, and issue commands as follows example:

Overlay vl10112880 lvds panel
```shell
u-boot=> setenv dtoverlay lvds-vl10112880
```

Overlay vl215192108 lvds panel
```shell
u-boot=> setenv dtoverlay lvds-vl215192108
```

Save your overlay configuration for every boot later, if you just want to boot current setting once, you can ignore this step.
```shell
u-boot=> saveenv
```

Boot into Android system
```shell
u-boot=> boot
```

***
Overlay Table

| platform | overlay function | overlay_name name |
|:---|:---|:---|
|EDM-G-IMX8M-PLUS| VL10112880 10" LVDS panel <br/> Vl215192108 21" LVDS panel <br/> TEVI-OV5640 <br/> TEVI-AR series <br/> VIZIONLINK with AR series | lvds-vl10112880 <br/> lvds-vl215192108 <br/> tevi-ov5640 <br/> tevi-ap1302  <br/> vizionlink-tevi-ap1302 |


#### Example: compile stage
You also can fixed your overlay setting in u-boot header file according your target board such as: 

```shell
<source>/vendor/nxp-opensource/uboot-imx/include/configs/<your board>_android.h
"dtoverlay=<change to what you want>\0"» \
```
---

### Change Boot Arguments
According to your device, you might need to change properties let your system performance better, list common properties as follows:

About CMA size, it's up to your DRAM size for each platforms, please modify CMA size as follows table:
| platform | DRAM size | recommend properties | default properties |
|:---|:---|:---|:---|
|EDM-G-IMX8M-PLUS| 1. 1GiB <br/> 2. 2GiB and above  | 1. cma=320M@0x400M-0xb80M androidboot.displaymode=720p galcore.contiguousSize=33554432 <br/> 2. cma=800M@0x400M-0xb80M | cma=800M@0x400M-0xb80M |


#### Example: runtime stage

EDM-G-IMX8M-PLUS
```shell
u-boot=> setenv bootargs stack_depot_disable=on kasan.stacktrace=off console=ttymxc1,115200 earlycon=ec_imx6q,0x30890000,115200 init=/init firmware_class.path=/vendor/firmware loop.max_part=7 transparent_hugepage=never swiotlb=65536 pci=nomsi cma=800M@0x400M-0x1000M bootconfig buildvariant=userdebug loglevel=0
u-boot=> saveenv
u-boot=> boot
```

#### Example: compile stage
You also can fixed your overlay setting in u-boot header file according your target board such as: 
```bash
<source>/vendor/nxp-opensource/uboot-imx/include/configs/<your board>_android.h
"bootargs=<change to what you want>\0"» \
```
---

### LIBGPIOD JNI demo and APIs
 
Technexion provide a demo app about libgpiod JNI Test, specific source code as following:
* [source code](https://github.com/technexion-android/kotlin_gpiod_demo)

Users can implement own GUI using our INPUT/OUTPUT APIs

Get total number of GPIO bank:<br/>
Java:
```shell
public native Int getGpioTotalBank();
```

Kotlin:
```shell
external fun getGpioTotalBank(): Int
```
<br/>    
Setting GPIO as output with specific value:<br/>
Java:
```shell
public native String  setGpioInfo(int bank,int line, int value);
```

Kotlin:
```shell
external fun setGpioInfo(bank: Int, line: Int, value: Int): String
```
<br/>
Setting GPIO as input and get a value:<br/>
Java:
```shell
public native String  getGpioInfo(int bank,int line);
```

Kotlin:
```shell
external fun getGpioInfo(bank: Int, line: Int): String
```
    
Users also can import JNI library to your own app using Androd Studio, detail instruction as follows:
[https://github.com/technexion-android/kotlin_gpiod_demo#kotlin_gpiod_demo](https://github.com/technexion-android/kotlin_gpiod_demo#kotlin_gpiod_demo)


---
### New OTA system: systemupgrade

Our Android 12 does support full update and incremental update, please contact our window to get detail instruction:
<EMAIL>

---

****