---
title: TN customized table example
sidebar_position: 0
sidebar_class_name: hidden
---

import CopyableText from '@site/src/components/CopyableText.jsx';

<!-- html table example -->
<table className="tn-table tn-table-color">
    <thead>
        <tr>
            <td>SOM</td>
            <td>AXON</td>
            <td colSpan={2} align="center">EDM-G-IMX8MP</td>
            <td colSpan={2} align="center">EDM-G-IMX8MM</td>
            <td colSpan={2} align="center">PICO-IMX8MM</td>
            <td colSpan={2} align="center">PICO-IMX8MQ</td>
        </tr>
        <tr>
            <td>Baseboard</td>
            <td>WIZARD</td>
            <td>EDM-G-WB</td>
            <td>EDM-G-WIZARD</td>
            <td>EDM-G-WB</td>
            <td>EDM-G-WIZARD</td>
            <td>PI</td>
            <td>Wizard</td>
            <td>PI</td>
            <td>Wizard</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Wifi driver</td>
            <td>QCA9377</td>
            <td>QCA9377<br/>IW416</td>
            <td>QCA9377<br/>IW416</td>
            <td>QCA9377<br/>IW416</td>
            <td>QCA9377<br/>IW416</td>
            <td>QCA9377</td>
            <td>QCA9377</td>
            <td>QCA9377</td>
            <td>QCA9377</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Panel Support</b></td>
            <td colSpan={9} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>HDMI</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>MIPI to HDMI</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>MIPI panel 5 inch</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>LVDS 10.1 inch</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>LVDS 15.6 inch</td>
            <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
            <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
            <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay lvds-vl15613676"/></td>
            <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
            <td>Yes<CopyableText fullText="u-boot=> setenv dtoverlay sn65dsi84-vl15613676"/></td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>Viizionpanel with 10.1 inch panel</td>
            <td>Yes <span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vizionpanel-vl10112880"/></td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
                <tr>
            <td>Viizionpanel with 15.0 inch panel</td>
            <td>Yes <span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vizionpanel-vl10112880"/></td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
                <tr>
            <td>Viizionpanel with 15.6 inch panel</td>
            <td>Yes <span style={{ color: "#ff0000" }}> (new)</span><CopyableText fullText="u-boot=> setenv dtoverlay vizionpanel-vl10112880"/></td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
    </tbody>
</table>

<!-- Markdown table example -->
<div className="md-table" style={{ fontSize: 12 }}>
| SOM | Axon-imx8mp |edm-g-imx8mp|| edm-g-imx8mm || pico-imx8mm ||
|---|---|---|---|---|---|---|---|
|**Baseband**|WZ|WB|WZ|WB|WZ|PI|WZ|
|Wifi driver|QCA9377-3|QCA9377-3<br/>IW416|QCA9377-3<br/>IW416|QCA9377-3<br/>IW416|QCA9377-3<br/>IW416|QCA9377-3|QCA9377-3|
|**Panel Support**||||||||
|HDMI|Yes|Yes|Yes|NA|NA|NA|NA|
|LVDS 10.1 inch|Yes|Yes|Yes|Yes|Yes|NA|NA|
|Vizionpanel with 10.1 inch panel|Yes <CopyableText fullText="copy vizionpanle101"/>|Yes|Yes|Yes|Yes|NA|NA|
|**Camera**||||||||
|TEVS-AR series|Yes|Yes|Yes|Yes<CopyableText fullText="copy tevs"/>|Yes<CopyableText fullText="copy tevs"/>|NA|NA|
|VLS3 series|Yes|Yes|Yes|Yes<CopyableText fullText="copy vls"/>|Yes<CopyableText fullText="copy vls"/>|NA|NA|
</div>
