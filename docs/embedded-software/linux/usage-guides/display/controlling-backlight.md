---
title: Controlling Backlight
description: Controlling Backlight
---
If there is an LCD present and connected to the system, the backlight brightness can be controlled by reading and writing to files in the /sys filesystem (sysfs).

``` bash
# cd /sys/class/backlight/backlight
# cat max_brightness
100
```
The `max_brightness`file contains a value that pertains to the maximum brightness of the backlight. The brightness level can be controlled by writing a number between 0 and `max_brightness`to the `brightness`file.

``` bash
# cat brightness
50
```
Off:
```bash
# echo 0 > brightness 
```
50%:
```bash
# echo 50 > brightness
```
Maximum:
```bash
# echo 100 > brightness
```