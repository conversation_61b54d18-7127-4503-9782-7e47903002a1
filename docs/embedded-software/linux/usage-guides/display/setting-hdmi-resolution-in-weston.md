---
title: Setting HDMI Resolution in Weston
description: Setting HDMI Resolution in Weston
---
## Applicable Platforms
Applicable Platforms for this article are those that are running Weston with built-in HDMI interfaces:

-   [PICO-IMX6](/docs/system-on-modules/pico/pico-imx6)
-   [PICO-IMX8M](/docs/system-on-modules/pico/pico-imx8m)
-   [AXON-IMX8M-PLUS](/docs/system-on-modules/axon/axon-imx8m-plus)
-   [EDM-G-IMX8M-PLUS](/docs/system-on-modules/edm/edm-g-imx8m-plus)

## Introduction
The following article describes a simple and quick way to set the screen resolution of your Weston GUI desktop without messing around with kernel display parameters.

### Determining the modes supported by the display

Each HDMI display will be able to support different modes. A mode in this case refers to a resolution, display refresh frequencies, whether or not the display is interlaced, and so on. This information is referred to as [Extended Display Identification Data](https://en.wikipedia.org/wiki/Extended_Display_Identification_Data) (EDID) and stored on an EEPROM located on the monitor. This is read by the SOC over a dedicated (in some cases, not always) I2C bus called [Display Data Channel](https://en.wikipedia.org/wiki/Display_Data_Channel) (DDC).

DRM-based graphics frontends report the supported modes in sysfs:

```bash
$ cat /sys/class/drm/card0-HDMI-A-1/modes
1280x720
1920x1080
1920x1080
1280x720
720x576
720x480
```

### Weston Desktop configuration file

The weston configuration file resides at the following location in your yocto/openembedded root filesystem.

**/etc/xdg/weston/weston.ini**

The content of the weston.ini looks like the following (this may vary with software versions and platform, so this might not look exactly like the file in your target system):

```text
[core]
# i.MX: Disable idle timeout
idle-time=0
use-g2d=1
gbm-format=argb8888

[libinput]
touchscreen_calibrator=true

#[output]
#name=HDMI-A-1
#mode=1280x720@60
#transform=90

[output]
name=DSI-1
transform=90
mode=1280x720@60

[shell]
size=1920x1080

#[output]
#name=HDMI-A-2
#mode=off
#       WIDTHxHEIGHT    Resolution size width and height in pixels
#       off             Disables the output
#       preferred       Uses the preferred mode
#       current         Uses the current crt controller mode
#transform=90

[screen-share]
command=/usr/bin/weston --backend=rdp-backend.so --shell=fullscreen-shell.so --no-clients-resize
```
### Modifying weston.ini
Uncomment the output section that includes the `HDMI-A-1` from the weston configuration file, and configure the resolution in the `mode` line:

```text
[output]
name=HDMI-A-1
mode=1920x1080@60
#transform=90
```
You will also need to set the correct name for your display card inorder for the output to take effect. To look for the correct device name, please examine your **/var/log/weston.log** file.
:::info
Once you save the weston.ini configuration file with the modifications, you will need to reboot the device or restart wayland/weston for the settings to take effect.
:::
### Restart Weston

You can restart weston by using systemctl:

```bash
$ systemctl restart weston
```