---
title: LVGL Benchmark Guide for AXON/EDM/PICO-IMX91 and AXON/EDM/PICO-IMX93
description: LVGL Benchmark Guide for AXON/EDM/PICO-IMX91 and AXON/EDM/PICO-IMX93
---
## Applicable Platforms
This guide applies to the following SOMs.
* AXON-IMX91
* AXON-IMX93
* EDM-IMX91
* EDM-IMX93

## Introduction
This evaluation focuses on four NXP i.MX platform development boards—the AXON-IMX93 and EDM-IMX93, as well as the AXON-IMX91 and EDM-IMX91. Using LVGL’s built-in benchmark module, we will quantitatively compare their multi-object animation rendering, update rate (FPS) , CPU load and average time under identical display resolution and test conditions.


## Part 1: Project setup
## 1.1 Set Up the Build Environment
1. Refer to the [Step-by-Step LVGL Development Guide for AXON/EDM-IMX91 and AXON/EDM-IMX93](https://developer.technexion.com/docs/testing-lvgl-demo-on-axon-imx91edm-imx91).

## 1.2 Run the project

1. Clone the repository:
```bash
git clone --recurse-submodules https://github.com/lvgl/lv_port_nxp_imx93.git
cd lv_port_nxp_imx93
```
2. default application from lv_port_linux runs the widget demo. To run the benchmark demo, modify **lv_port_linux/main.c** :
```bash
/*Create a Demo*/
// lv_demo_widgets();
// lv_demo_widgets_start_slideshow();
lv_demo_benchmark();
```
3. Choose a Configuration:
```bash
cp lv_conf_example/lv_conf_fb_2_threads.h lv_port_linux/lv_conf.h
```
4. build :
```bash
cd lv_port_linux
source /opt/fsl-imx-xwayland/6.6-scarthgap/environment-setup-armv8a-poky-linux
cmake -GNinja -B build
ninja -C build
```

## Part2: Transfer the executable on the board
## 2.1 Prepare Target Board

1. Ensure your target board is powered on and connected to your network
2. Go to the root filesystem

## 2.2 Copy the LVGL Application
```bash
cp  /bin/lvglsim  /media/username/root/usr/bin/
```
Alternatively, if your board is accessible via SSH:
```bash
scp /bin/lvglsim  root@board-ip-address:/usr/bin/
```
## Part3: Running the LVGL Benchmark Demo
1.Start the application
```bash
######################################
## WARNING: do not stop these services if using wayland demo
systemctl stop weston.socket
systemctl stop weston.service
######################################
/usr/bin/lvglsim
```
![lvgl_benchmark.png](//img/lvgl_benchmark.png)


**AXON-IMX93/EDM-IMX93**
![lvgl.png](//img/lvgl.png)

**AXON-IMX91/EDM-IMX91**
![lvgl(1).png](//img/lvgl%281%29.png)

## Related comparisons
We are running `glmark2-es2-wayland` on the weston of these two platforms and comparing it with the i.MX8M Plus platform.

:::(Info) (NOTE)
i.MX91 family lacks a dedicated GPU, i.MX93 family includes a hardware compositor but no 3D accelerator.
:::

| **Category**    | **Configuration**                                                                 | **axon-imx91 FPS** | **axon-imx91 FrameTime (ms)** | **axon-imx93 FPS** | **axon-imx93 FrameTime (ms)** | **edm-g-imx8mp FPS** | **edm-g-imx8mp FrameTime (ms)** |
|------------------|-----------------------------------------------------------------------------------|--------------------|--------------------------------|--------------------|--------------------------------|----------------------|---------------------------------|
| **build**        | `use-vbo=false`                                                                   | 2                  | 674.318                       | 10                 | 103.173                       | 1147                 | 0.872                          |
| **build**        | `use-vbo=true`                                                                    | 2                  | 690.164                       | 10                 | 103.291                       | 2091                 | 0.478                          |
| **texture**      | `texture-filter=nearest`                                                          | 2                  | 803.125                       | 6                  | 184.201                       | 1920                 | 0.521                          |
| **texture**      | `texture-filter=linear`                                                           | 2                  | 837.053                       | 5                  | 214.452                       | 1847                 | 0.542                          |
| **texture**      | `texture-filter=mipmap`                                                           | 1                  | 1237.384                      | 2                  | 546.167                       | 1779                 | 0.562                          |
| **shading**      | `shading=gouraud`                                                                 | 2                  | 810.113                       | 6                  | 197.329                       | 1351                 | 0.741                          |
| **shading**      | `shading=blinn-phong-inf`                                                         | 2                  | 997.827                       | 3                  | 342.969                       | 1193                 | 0.838                          |
| **shading**      | `shading=phong`                                                                   | 1                  | 1357.790                      | 2                  | 629.969                       | 702                  | 1.425                          |
| **shading**      | `shading=cel`                                                                     | 1                  | 1511.842                      | 2                  | 764.881                       | 573                  | 1.748                          |
| **bump**         | `bump-render=high-poly`                                                           | 1                  | 1363.253                      | 2                  | 639.441                       | 655                  | 1.527                          |
| **bump**         | `bump-render=normals`                                                             | 2                  | 884.319                       | 4                  | 251.701                       | 1840                 | 0.544                          |
| **bump**         | `bump-render=height`                                                              | 1                  | 1018.196                      | 3                  | 350.506                       | 968                  | 1.034                          |
| **effect2d**     | `kernel=0,1,0;1,-4,1;0,1,0;`                                                       | 1                  | 2644.105                      | 1                  | 1675.449                      | 382                  | 2.619                          |
| **effect2d**     | `kernel=1,1,1,1,1;1,1,1,1,1;1,1,1,1,1;`                                             | 1                  | 6178.061                      | 1                  | 4594.844                      | 133                  | 7.531                          |
| **pulsar**       | `light=false:quads=5:texture=false`                                               | 2                  | 776.025                       | 9                  | 115.370                       | 1155                 | 0.866                          |
| **desktop**      | `blur-radius=5:effect=blur:passes=1:separable=true:windows=4`                     | 1                  | 5335.190                      | 1                  | 4356.413                      | 174                  | 5.747                          |
| **desktop**      | `effect=shadow:windows=4`                                                         | 1                  | 1655.753                      | 2                  | 962.357                       | 604                  | 1.656                          |
| **buffer**       | `columns=200:interleave=false:update-dispersion=0.9:update-fraction=0.5:update-method=map` | 1                  | 1192.949                      | 2                  | 504.013                       | 165                  | 6.084                          |
| **buffer**       | `columns=200:interleave=false:update-dispersion=0.9:update-fraction=0.5:update-method=subdata` | 1                  | 1193.040                      | 2                  | 500.001                       | 164                  | 6.103                          |
| **buffer**       | `columns=200:interleave=true:update-dispersion=0.9:update-fraction=0.5:update-method=map` | 1                  | 1191.833                      | 3                  | 499.483                       | 220                  | 4.549                          |
| **ideas**        | `speed=duration`                                                                  | 2                  | 938.843                       | 5                  | 241.214                       | 339                  | 2.951                          |
| **jellyfish**    | `<default>`                                                                       | 1                  | 2258.228                      | 1                  | 1468.858                      | 252                  | 3.971                          |
| **terrain**      | `<default>`                                                                       | 1                  | 43405.943                     | 1                  | 34764.409                     | 20                   | 51.285                         |
| **shadow**       | `<default>`                                                                       | 1                  | 1170.134                      | 3                  | 476.992                       | 747                  | 1.340                          |
| **refract**      | `<default>`                                                                       | 1                  | 13565.137                     | 1                  | 10761.557                     | 61                   | 16.404                         |
| **conditionals** | `fragment-steps=0:vertex-steps=0`                                                 | 2                  | 800.324                       | 6                  | 183.049                       | 1386                 | 0.722                          |
| **conditionals** | `fragment-steps=5:vertex-steps=0`                                                 | 1                  | 1382.836                      | 2                  | 643.121                       | 226                  | 4.432                          |
| **conditionals** | `fragment-steps=0:vertex-steps=5`                                                 | 2                  | 823.486                       | 6                  | 191.288                       | 1252                 | 0.799                          |
| **function**     | `fragment-complexity=low:fragment-steps=5`                                        | 2                  | 982.666                       | 4                  | 325.235                       | 490                  | 2.042                          |
| **function**     | `fragment-complexity=medium:fragment-steps=5`                                     | 1                  | 1222.552                      | 2                  | 522.210                       | 249                  | 4.028                          |
| **loop**         | `fragment-loop=false:fragment-steps=5:vertex-steps=5`                             | 1                  | 1001.692                      | 3                  | 335.408                       | 478                  | 2.096                          |
| **loop**         | `fragment-steps=5:fragment-uniform=false:vertex-steps=5`                          | 1                  | 1003.065                      | 3                  | 335.774                       | 476                  | 2.103                          |
| **loop**         | `fragment-steps=5:fragment-uniform=true:vertex-steps=5`                           | 1                  | 1448.505                      | 2                  | 704.637                       | 262                  | 3.829                          |

## Conclusion
The i.MX91 family lacks a dedicated GPU and is primarily optimized for 2D graphics and basic rendering tasks, making it challenging to display complex dynamic graphics (e.g., Wayland weston, QT applications, Ubuntu Gnome) or 3D graphics. The i.MX93 family, in contrast, includes a hardware compositor (e.g., for 2D overlays and basic GPU acceleration) but does not feature a 3D graphics accelerator, limiting its ability to handle advanced 3D rendering.

While the i.MX93 can support simple GUI operations (e.g., static or low-complexity interfaces), the i.MX91 is even more constrained. For the i.MX91 series, the data from the previous table suggests that LVGL (a lightweight 2D graphics framework) is the most practical choice for fixed-display applications, as it aligns with the chip's limited rendering capabilities. For applications requiring a basic operating system interface (e.g., desktop environments, shadow effects) with low power consumption, the i.MX93 series is a viable option due to its hardware compositor.

However, for scenarios involving 3D graphics, complex 2D effects, or high-performance GUIs (e.g., [terrain], [effect2d], or [buffer] tests in the table), the edm-g-imx8mp (or similar i.MX8MP-based devices) is strongly recommended due to its significantly higher FPS and lower FrameTime in these workloads. This highlights the importance of matching hardware capabilities to application requirements when selecting a platform for embedded graphics development.

## Reference
* [LVGL Benchmark GitHub Repository](https://github.com/lvgl/lv_port_nxp_imx93?tab=readme-ov-file)
* [TN's Yocto GitHub Repository](https://github.com/TechNexion/tn-imx-yocto-manifest)