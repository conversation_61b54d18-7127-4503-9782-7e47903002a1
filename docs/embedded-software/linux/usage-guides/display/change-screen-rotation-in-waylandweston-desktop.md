---
title: Change Screen Rotation in Wayland/Weston Desktop
description: Change Screen Rotation in Wayland/Weston Desktop
---
## Introduction
Wayland/Weston is the de facto GUI desktop provided by Yocto/Openembedded Distribution

Since there are numerous Display Panels available in the market, it is sometimes necessary to rotate the GUI desktop to either Landscape or Portrait setting to ensure a better representation of your UI contents.

The following article describes a simple and quick way to set the rotation of your Wayland/Weston GUI desktop without messing around with kernel display parameters.

### Wayland/Weston Desktop configuration file
The weston configuration file resides at the following location in your yocto/openembedded root filesystem.

**/etc/xdg/weston/weston.ini**

The content of the weston.ini looks like the following
```text
[core]
#gbm-format=argb8888
idle-time=0
use-g2d=1
xwayland=true
#enable-overlay-view=1

#[shell]
#size=1920x1080

[libinput]
touchscreen_calibrator=true

#[output]
#name=HDMI-A-1
#mode=1920x1080@60
#transform=90

#[output]
#name=HDMI-A-2
#mode=off
#       WIDTHxHEIGHT    Resolution size width and height in pixels
#       off             Disables the output
#       preferred       Uses the preferred mode
#       current         Uses the current crt controller mode
#transform=90

[screen-share]
command=@bindir@/weston --backend=rdp-backend.so --shell=fullscreen-shell.so --no-clients-resize
```
### Determine your display card name for the output section 

You will need to either uncomment an output section from the weston configuration file, or add additional output section to enable screen rotation of the wayland/weston desktop.

You will also need to set the correct name for your display card in order for the output to take effect.To look for the correct device name, please examine your **/var/log/weston.log** file.

### PICO-IMX6 NYMPH board with HDMI output

```text
    ...
    [11:30:15.320] Creating fbdev output.
    [11:30:15.320] Chosen EGL config details:
    RGBA bits: 8 8 8 0
    swap interval range: 0 – 10
    [11:30:15.321] fbdev output 1280×720 px
    guessing 61 Hz and 96 dpi
    [11:30:15.322] Output /dev/fb0 enabled with head(s) /dev/fb0
    [11:30:15.322] Compositor capabilities:
    arbitrary surface rotation: yes
    screen capture uses y-flip: yes
    presentation clock: CLOCK_MONOTONIC_RAW, id 4
    presentation clock resolution: 0.000000001s
```

Since there are no specific device name given, and "fbdev" is created. We could then add an output section below to enable the Wayland/Weston desktop rotation to 90 degree clockwise.

#### Additional Output Section to add into /etc/xdg/weston/weston.ini

```text
[output]
name=fbdev
mode=1280x@720
transform=90 # rotate 90 degrees clockwise
```
### Example: PICO-IMX6 NYMPH board with LVDS 7 inch display

```text
    ...
    [11:30:15.302] Creating fbdev output.
    [11:30:15.303] Chosen EGL config details:
    RGBA bits: 8 8 8 0
    swap interval range: 0 - 10
    [11:30:15.304] fbdev output 1024×600 px
    guessing 68 Hz and 96 dpi
    [11:30:15.304] Output /dev/fb0 enabled with head(s) /dev/fb0
    [11:30:15.304] Compositor capabilities:
    arbitrary surface rotation: yes
    screen capture uses y-flip: yes
    presentation clock: CLOCK_MONOTONIC_RAW, id 4
    presentation clock resolution: 0.000000001s
    ...
```
Again there are no specific name given, but "fbdev" is created, we can set the following output section.

#### Additional Output Section to add into /etc/xdg/weston/weston.ini

```text
[output]
name=fbdev
mode=1024x@600
transform=90 # rotate 90 degrees clockwise
```
### PICO-IMX8-MINI and PICO-IMX8M with 5\" MIPI DSI Display

For PICO-IMX8M-MINI and PICO-IMX8M, the default display orientation is portrait mode (as the 5\" MIPI display is a portrait mode display). It doesn't make use of fbdev, so here is the change to make the display manager appear in landscape mode:
```text
[output]
name=DSI-1
transform=90
mode=1280x720@60
```
:::info
Once you save the weston.ini configuration file with the modifications, you will need to reboot the device or restart wayland/weston for the settings to take effect.
:::
