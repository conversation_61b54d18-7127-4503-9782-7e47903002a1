---
title: Setting number of CPUs and CPU frequency
description: Setting number of CPUs and CPU frequency
---
## Introduction
This article shows you how to disable and enable CPU cores and set the CPU frequency.

### Setting number of active CPU cores

This can be set either at boot or at runtime.  To set this at boot, enter the U-boot prompt and set the following environment variables.

The following example sets the number of CPUs to 2 (assuming a quad-core system):
```text
=> setenv extraargs maxcpus=2
=> setenv mmcargs 'setenv bootargs ${jh_clk} console=${console} root=${mmcroot} ${extraargs}'
=> saveenv
=> boot
```
To check the number of CPUs:
```text
# cat /proc/cpuinfo
processor       : 0
BogoMIPS        : 16.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32 cpuid
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd03
CPU revision    : 4

processor       : 1
BogoMIPS        : 16.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32 cpuid
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd03
CPU revision    : 4
```
To disable/enable CPUs at runtime:
```text
# echo 0 > /sys/devices/system/cpu/cpuN/online
```
where `N` is the number of the CPU core. In a 4-core system, N will be a number between 0 and 3.

### CPU Frequency
There are several frequency governors which determine the frequency policy.
To list all available governors:
```text
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_available_governors
conservative userspace powersave ondemand performance schedutil
```
The default frequency governor is "ondemand", which sets the CPU frequency depending on the current system load.

More information regarding CPU frequency and governors is available in the kernel documentation:
[https://github.com/TechNexion/linux-tn-imx/blob/tn-imx_5.4.70_2.3.0-stable/Documentation/admin-guide/pm/cpufreq.rst](https://github.com/TechNexion/linux-tn-imx/blob/tn-imx_5.4.70_2.3.0-stable/Documentation/admin-guide/pm/cpufreq.rst)

To set the governor:
```text
# echo <governor> > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor
```
For example, to set the governor to `powersave`:
```text
# echo powersave > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor
```
List the available frequencies:
```text
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_available_frequencies
1200000 1600000 1800000
```
Check the current CPU frequency:
```text
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq
1200000
```
To check the current maximum frequency:
```text
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq
1800000
```
Set the maximum frequency:
```text
# echo <frequency> > /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq
```
Where `frequency` is from the list of available frequencies. For example, to set the maximum frequency to 1.6GHz (i.MX8M Mini, or i.MX8M Plus):
```text
# echo 1600000 > /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq
1600000
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq
1200000
```
In the above example, we set the maximum frequency to 1.6GHz, but current frequency is 1.2GHz.

Suppose we want to set the CPU frequency to the maximum? We can set the governor to `performance` and the CPU frequency will automatically be set to the maximum allowed scaling frequency.
```text
# echo 1800000 > /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq
# echo performance > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor
# cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq
1800000
```

