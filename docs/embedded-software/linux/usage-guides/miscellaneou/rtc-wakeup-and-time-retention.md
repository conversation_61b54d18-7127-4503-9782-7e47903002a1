---
title: RTC Wakeup and Time Retention
description: RTC Wakeup and Time Retention
---
This guide explains how to set and read an hardware RTC, and how to schedule an RTC wakeup from suspend.

## Introduction
Many TechNexion boards contain a hardware RTC. A hardware RTC often has a backup power mechanism in the form of a coincell battery or a large capacitor to power the RTC when there is no power to the board.

### Setting the time in the hardware RTC
The usual way to set the hardware RTC is to first set the system time (which is a software timer, reset when the board is reset). The system time can then be copied into a hardware RTC.

The system time is typically set with the `date -s` command. Unfortunately, the syntax differs between versions of 'date' command. The minimal busybox date requires a different, simplified format than the standard UNIX date command. For the standard date command the
easiest way to set it, is to issue the command `date` and then feed back its own output format.

**Example:**

Query the system time:

```bash
$ date
Fri Oct 16 09:58:32 UTC 2015
```
then set the system time:
```bash
$ date -s "Aug 01 15:11:12 UTC 2018"
```
Now the hardware RTC can be set using:
```bash
$ hwclock --systohc
```
### Reading the hardware RTC
After a reboot most operating systems will synchronize the system time from the RTC automatically.

It can also be done manually by:
```bash
$ hwclock --hwtosys
```
and then displaying the system time with
```bash
$ date
```
### Letting the RTC wake up the system from suspend

This feature is only present on boards where the RTCs primary interrupt pin (typically named INT_A or the like) is connected to the CPU.

It also requires the RTC interrupt to be enabled in the RTC driver, and the RTC should be set as a wakeup source in the power management settings.

The easiest way to set a wakeup alarm is using the `rtcwake` command. The command takes arguments `-s` and number of seconds, or in the case of multiple RTCs (see troubleshooting section) a separate RTC device using `-d /dev/rtxX`.

Example to setup a wake in 20 seconds:
```bash
$ rtcwake -d /dev/rtc0 -s 20
```
and then suspend:
```bash
$ echo mem > /sys/power/state
```
### Using time from internet server

The usual way to synchronize a clock with the internet is to use the NTP service. A simple command for a one time sync is:
```bash
$ ntpdate -u ntp.ubuntu.com
```
### Troubleshooting

Problem 1: Time stored are not preserved, despite backup battery.

Suggestion: Make sure the right RTC is used. Some CPUs, like the iMX6, contain a built in RTC, the "SNVS RTC". The built in RTC does not store time, but is reset when the system is reset. Maybe the SNVS RTC is queried instead of the dedicated RTC?

Alternatively: Most software use `/dev/rtc0` as the default. Maybe the hardware RTC is not loaded or loaded as a different device than `/dev/rtc0`?
