---
title: Using PWM from a Linux Shell
description: Using PWM from a Linux Shell
---
This mini guide explains how to enable and test a pwm signal using linux command line.

## Instructions

To enable a PWM the steps are as follows (some details might differ depending on the product)
1. Maneuver to the `/sys/class/pwm` folder\
In this folder the pwm controllers should be visible as folders `pwmchip0`, `pwmchip1`, ...
2. Enter the chosen pwmchip folder.
```shell
$ cd pwmchip0
```
3. Export the first pwm (the iMX pwm controllers have one pwm each) by
```shell
$ echo 0 > export
```
This exposes the actual PWM in folder pwm0.
:::info
If the PWM has been allocated for use by another driver such as a display backlight driver (for control of the backlight brightness), then it might throw an error. If so, check your device tree
:::
4. Enter the pwm folder
```shell
$ cd pwm0
```
and configure the pwm:
```shell
$ echo 10000 > period
$ echo 6000 > duty_cycle
$ echo 1 > enable
```
This should set the PWM frequency to 100 kHz and set the duty cycle to 60 kHz.
