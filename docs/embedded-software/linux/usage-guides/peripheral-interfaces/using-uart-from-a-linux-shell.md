---
title: Using UART from a Linux Shell
description: Using UART from a Linux Shell
---
This guide explains how to use a serial port using linux shell. It shows how to set and read serial port settings, and how to send and receive data on a serial port.

### Steps
To follow this guide, a terminal/shell is needed on the TechNexion development kit. The easiest way is to use the debug console facility, but can also be achieved using `ssh` or `adb`.

### UART naming and numbering {#uart_naming_and_numbering}
In hardware manuals, schematics and the like, the UARTs are often named "UART1" or "UART2" and so forth. Typically, there is no "UART0". Software, on the other hand, typically numbers the serial port devices starting with the number 0.

To make a long story short, what in schematics or user guides is called "UART1" is `/dev/ttymxc0` in software, "UART2" is `/dev/ttymxc1`, and so on.

### Setting and reading UART parameters
UART parameters are read and set with the `stty` command. To set the baud rate on UART3 to 115200, use the command:

```shell
$ stty -F /dev/ttymxc2 115200
```
To enable RTS/CTS flow control, use

```shell
$ stty -F /dev/ttymxc2 crtscts
```
and disable flow control with

```shell
$ stty -F /dev/ttymxc2 -crtscts
```
To view the current settings for UART3, use

```shell
$ stty -a -F /dev/ttymxc2
```
Just replace `/dev/ttymxc2` with the name of the UART device.

### Sending and Receiving data
This is very easy, sending is done by writing data to the device file, for instance by

```shell
$ echo "Hello world" > /dev/ttymxc2
```
and to receive is to read the device file
```shell
$ cat /dev/ttymxc2
```
Please note that the receiving terminal can have buffering enabled, so the data read might not be displayed immediately. The buffer is flushed once enough data is entered, or when enough newlines are encountered.
