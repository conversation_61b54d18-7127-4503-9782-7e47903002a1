---
title: CAN Bus
description: CAN Bus
---
## Introduction
This article shows how to set up a CAN network between two boards. This assumes you are working with a product which supports CAN such as EDM1-FAIRY (with EDM1-IMX6), or PICO-NYMPH (with PICO-IMX6, PICO-IMX7, or PICO-IMX6UL/ULL).

### Board connections
Set up two boards so that they can exchange CAN information.

| Board A |`<->`| Board B
|---|---|---
| CAN_H |`<->`|CAN_H
| CAN_L | `<->`|CAN_L
|  GND | `<->`|GND

### Set up CAN on both boards
Initialize and bring up the CAN interface on both boards.
```text
# ip link set can0 up type can bitrate 125000
```
On board A, start `candump` in order to show any received CAN data;
```text
# candump can0
```
On board B, send some data:
```text
# cansend can0 500#1E.10.10
```