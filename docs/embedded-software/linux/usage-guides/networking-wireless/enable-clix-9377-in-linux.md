---
title: Enable CLIX-9377 in Linux
description: Enable CLIX-9377 in Linux
---
## Introduction
This article shows how to enable TechNexion CLIX-9377 in Linux.

### About CLIX-9377
The CLIX-9377 is a pluggable version of the PIXI-9377-S. It is a compact combo WLAN/BT module based on the Qualcomm QCA9377 chipset.

![clix-9377-top-cropped.png](//img/clix-9377-top-cropped%281%29.png)

For more information on the CLIX-9377, refer to the TechNexion website:

[CLIX-9377 on TechNexion website](https://www.technexion.com/products/connectivity/clix-9377/)

### Getting Started
To enable WIFI for CLIX-9377, it requires to integrate the QCACLD driver into the Linux kernel, set up a few GPIO control pins and SDIO interface in the kernel device tree.

### Steps

#### QCACLD Driver
Please refer to the following page for instructions on how to build the QCA9377 off-tree driver for linux.
[Build qcacld-2.0 off-tree driver for Linux](https://github.com/TechNexion/qcacld-2.0#qcacld-20-for-qualcomm-wifi-module)

:::info
If you are using a TechNexion demo image or have used the TechNexion Yocto BSP to build your custom image, it is likely that the QCACLD driver is already built into the image.
:::

#### Device Tree Configuration
There are three signals that need to be configured in the device tree to ensure the correct power state of CLIX-9377: **WL_REG_ON**, **BT_REG_ON** and **CLIX_RESET**.

In order to operate SDIO interface at 3.3 V, add "no-1-8-v" attribute in device tree.
![CLIX-9377_GPIO_control_pin.png](//img/CLIX-9377_GPIO_control_pin.png)

#### Example:
You can add device tree overlay for CLIX-9377 as below:
```
/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target-path = "/";
		__overlay__ {
			clix_bt_reg_on: clix_btreg_on {
				compatible = "gpio-reset";
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_clix_bt_ctrl>;
				reset-gpios = <&gpio5 11 GPIO_ACTIVE_LOW>;
				reset-delay-us = <2000>;
				reset-post-delay-ms = <40>;
				#reset-cells = <0>;
			};

			regulators {
				clix_wl_reg_on: clix_wlreg_on {
					compatible = "regulator-fixed";
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_clix_wifi_ctrl>;
					regulator-name = "CLIX_WL_REG_ON";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					gpio = <&gpio5 13 GPIO_ACTIVE_HIGH>;
					enable-active-high;
					regulator-always-on;
				};

				clix_rst: clix_reset {
					compatible = "regulator-fixed";
					regulator-name = "CLIX_RESET";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					gpio = <&gpio4 28 GPIO_ACTIVE_HIGH>;
					enable-active-high;
					regulator-always-on;
				};
			};
		};
	};

	fragment@1 {
		target = <&ecspi2>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@2 {
		target = <&uart1>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@3 {
		target = <&usdhc1>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@4 {
		target = <&uart3>;
		__overlay__ {
			resets = <&clix_bt_reg_on>;
			status = "okay";
		};
	};

	fragment@5 {
		target = <&usdhc2>;
		__overlay__ {
			vdd-supply = <&clix_wl_reg_on>;
			no-1-8-v;
			pm-ignore-notify;
			keep-power-in-suspend;
			non-removable;
			status = "okay";
		};
	};
};
```

The SDIO bus information would be shown as below:
```
# cat /sys/kernel/debug/mmc0/ios
clock:          50000000 Hz
actual clock:   50000000 Hz
vdd:            21 (3.3 ~ 3.4 V)
bus mode:       2 (push-pull)
chip select:    0 (don't care)
power mode:     2 (on)
bus width:      2 (4 bits)
timing spec:    2 (sd high-speed)
signal voltage: 0 (3.30 V)
driver type:    0 (driver type B)
```