---
title: Using BLE (Bluetooth Low Energy)
description: Using BLE (Bluetooth Low Energy)
---

## Introduction
This article shows how to use BLE with TechNexion products.
### Discovering BLE devices
```text
# btmgmt find -l
<PERSON> started
hci0 type 6 discovering on
hci0 dev_found: 4B:F4:C9:73:F2:76 type LE Random rssi -39 flags 0x0000
AD flags 0x1a
name Proximity
hci0 type 6 discovering off
```
In the above example, a device named "Heart Rate" was discovered with a random MAC address `5F:CC:75:BD:8D:7E`.

### Connecting to a BLE device
You can use either `gatttool` or `btgatt-client` to connect to the device.

In the absence of an actual BLE device, an app can be installed and run a mobile phone or tablet such as [<PERSON>Blue from PunchThrough](https://punchthrough.com/lightblue/). This can be used to both discover available services and implement virtual BLE devices.


Using `btgatt-client`:
```text
root@edm-g-imx8mp:/# btgatt-client -t random -d 5F:CC:75:BD:8D:7E -i hci0
Connecting to device... Done
[GATT client]# Service Added - UUID: 00001800-0000-1000-8000-00805f9b34fb start: 0x0001 end: 0x0005
[GATT client]# Service Added - UUID: 00001801-0000-1000-8000-00805f9b34fb start: 0x0006 end: 0x0009
[GATT client]# Service Added - UUID: d0611e78-bbb4-4591-a5f8-487910ae4366 start: 0x000a end: 0x000e
[GATT client]# sService Added - UUID: 9fa480e0-**************-d343dc5d04ae start: 0x000f end: 0x0013
[GATT client]# eService Added - UUID: 0000180f-0000-1000-8000-00805f9b34fb start: 0x0014 end: 0x0017
[GATT client]# rService Added - UUID: 00001805-0000-1000-8000-00805f9b34fb start: 0x0018 end: 0x001d
[GATT client]# Service Added - UUID: 0000180a-0000-1000-8000-00805f9b34fb start: 0x001e end: 0x0022
[GATT client]# vicService Added - UUID: 7905f431-b5ce-4e99-a40f-4b1e122d00d0 start: 0x0023 end: 0x002c
[GATT client]# es
GATT client not initialized
[GATT client]# Service Added - UUID: 89d3502b-0f36-433a-8ef4-c502ad55f8dc start: 0x002d end: 0x0038
[GATT client]# Service Added - UUID: 00001803-0000-1000-8000-00805f9b34fb start: 0x0039 end: 0x003c
[GATT client]# Service Added - UUID: 00001802-0000-1000-8000-00805f9b34fb start: 0x0040 end: 0x0042
[GATT client]# Service Added - UUID: 00001804-0000-1000-8000-00805f9b34fb start: 0x0043 end: 0x0046
[GATT client]# GATT discovery procedures complete
[GATT client]#
service - start: 0x0001, end: 0x0005, type: primary, uuid: 00001800-0000-1000-8000-00805f9b34fb
          charac - start: 0x0002, value: 0x0003, props: 0x02, ext_props: 0x0000, uuid: 00002a00-0000-1000-8000-00805f9b34fb
          charac - start: 0x0004, value: 0x0005, props: 0x02, ext_props: 0x0000, uuid: 00002a01-0000-1000-8000-00805f9b34fb

service - start: 0x0006, end: 0x0009, type: primary, uuid: 00001801-0000-1000-8000-00805f9b34fb
          charac - start: 0x0007, value: 0x0008, props: 0x20, ext_props: 0x0000, uuid: 00002a05-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x0009, uuid: 00002902-0000-1000-8000-00805f9b34fb

service - start: 0x000a, end: 0x000e, type: primary, uuid: d0611e78-bbb4-4591-a5f8-487910ae4366
          charac - start: 0x000b, value: 0x000c, props: 0x98, ext_props: 0x0001, uuid: 8667556c-9a37-4c91-84ed-54ee27d90049
                  descr - handle: 0x000d, uuid: 00002900-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x000e, uuid: 00002902-0000-1000-8000-00805f9b34fb

service - start: 0x000f, end: 0x0013, type: primary, uuid: 9fa480e0-**************-d343dc5d04ae
          charac - start: 0x0010, value: 0x0011, props: 0x98, ext_props: 0x0001, uuid: af0badb1-5b99-43cd-917a-a77bc549e3cc
                  descr - handle: 0x0012, uuid: 00002900-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x0013, uuid: 00002902-0000-1000-8000-00805f9b34fb

service - start: 0x0014, end: 0x0017, type: primary, uuid: 0000180f-0000-1000-8000-00805f9b34fb
          charac - start: 0x0015, value: 0x0016, props: 0x12, ext_props: 0x0000, uuid: 00002a19-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x0017, uuid: 00002902-0000-1000-8000-00805f9b34fb

service - start: 0x0018, end: 0x001d, type: primary, uuid: 00001805-0000-1000-8000-00805f9b34fb
          charac - start: 0x0019, value: 0x001a, props: 0x12, ext_props: 0x0000, uuid: 00002a2b-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x001b, uuid: 00002902-0000-1000-8000-00805f9b34fb
          charac - start: 0x001c, value: 0x001d, props: 0x02, ext_props: 0x0000, uuid: 00002a0f-0000-1000-8000-00805f9b34fb

service - start: 0x001e, end: 0x0022, type: primary, uuid: 0000180a-0000-1000-8000-00805f9b34fb
          charac - start: 0x001f, value: 0x0020, props: 0x02, ext_props: 0x0000, uuid: 00002a29-0000-1000-8000-00805f9b34fb
          charac - start: 0x0021, value: 0x0022, props: 0x02, ext_props: 0x0000, uuid: 00002a24-0000-1000-8000-00805f9b34fb

service - start: 0x0023, end: 0x002c, type: primary, uuid: 7905f431-b5ce-4e99-a40f-4b1e122d00d0
          charac - start: 0x0024, value: 0x0025, props: 0x88, ext_props: 0x0001, uuid: 69d1d8f3-45e1-49a8-9821-9bbdfdaad9d9
                  descr - handle: 0x0026, uuid: 00002900-0000-1000-8000-00805f9b34fb
          charac - start: 0x0027, value: 0x0028, props: 0x10, ext_props: 0x0000, uuid: 9fbf120d-6301-42d9-8c58-25e699a21dbd
                  descr - handle: 0x0029, uuid: 00002902-0000-1000-8000-00805f9b34fb
          charac - start: 0x002a, value: 0x002b, props: 0x10, ext_props: 0x0000, uuid: 22eac6e9-24d6-4bb5-be44-b36ace7c7bfb
                  descr - handle: 0x002c, uuid: 00002902-0000-1000-8000-00805f9b34fb

service - start: 0x002d, end: 0x0038, type: primary, uuid: 89d3502b-0f36-433a-8ef4-c502ad55f8dc
          charac - start: 0x002e, value: 0x002f, props: 0x98, ext_props: 0x0001, uuid: 9b3c81d8-57b1-4a8a-b8df-0e56f7ca51c2
                  descr - handle: 0x0030, uuid: 00002900-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x0031, uuid: 00002902-0000-1000-8000-00805f9b34fb
          charac - start: 0x0032, value: 0x0033, props: 0x98, ext_props: 0x0001, uuid: 2f7cabce-808d-411f-9a0c-bb92ba96c102
                  descr - handle: 0x0034, uuid: 00002900-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x0035, uuid: 00002902-0000-1000-8000-00805f9b34fb
          charac - start: 0x0036, value: 0x0037, props: 0x8a, ext_props: 0x0001, uuid: c6b2f38c-23ab-46d8-a6ab-a3a870bbd5d7
                  descr - handle: 0x0038, uuid: 00002900-0000-1000-8000-00805f9b34fb

service - start: 0x0039, end: 0x003c, type: primary, uuid: 00001803-0000-1000-8000-00805f9b34fb
          charac - start: 0x003a, value: 0x003b, props: 0x8a, ext_props: 0x0001, uuid: 00002a06-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x003c, uuid: 00002900-0000-1000-8000-00805f9b34fb

service - start: 0x0040, end: 0x0042, type: primary, uuid: 00001802-0000-1000-8000-00805f9b34fb
          charac - start: 0x0041, value: 0x0042, props: 0x00, ext_props: 0x0000, uuid: 00002a06-0000-1000-8000-00805f9b34fb

service - start: 0x0043, end: 0x0046, type: primary, uuid: 00001804-0000-1000-8000-00805f9b34fb
          charac - start: 0x0044, value: 0x0045, props: 0x02, ext_props: 0x0000, uuid: 00002a07-0000-1000-8000-00805f9b34fb
                  descr - handle: 0x0046, uuid: 00002901-0000-1000-8000-00805f9b34fb

[GATT client]# Device disconnected: Connection reset by peer
```
Using `gatttool`:

```text
# gatttool -t random -b 5F:CC:75:BD:8D:7E -I
[5F:CC:75:BD:8D:7E][LE]> connect
Attempting to connect to 5F:CC:75:BD:8D:7E
Error: connect error: Function not implemented (38)
[5F:CC:75:BD:8D:7E][LE]> connect
Attempting to connect to 5F:CC:75:BD:8D:7E
Connection successful
Indication   handle = 0x0008 value: 0a 00 ff ff
[5F:CC:75:BD:8D:7E][LE]>
(gatttool:1598): GLib-WARNING **: 19:43:46.743: Invalid file descriptor.
[5F:CC:75:BD:8D:7E][LE]> connect
Attempting to connect to 5F:CC:75:BD:8D:7E
Connection successful
Indication   handle = 0x0008 value: 0a 00 ff ff
[5F:CC:75:BD:8D:7E][LE]>
```
### Running as a BLE Server

```text
# btmgmt -i hci0 le on
hci0 Set Low Energy complete, settings: powered bondable ssp br/edr le secure-conn
# btmgmt -i hci0 connectable on
hci0 Set Connectable complete, settings: powered connectable bondable ssp br/edr le secure-conn
# btmgmt -i hci0 name "my_ble_server"
# btmgmt -i hci0 advertising on
```
```text
# btgatt-server -i hci0 -r -v
Started listening on ATT channel. Waiting for connections
Connect from 5D:E1:62:A8:25:73
Running GATT server
[GATT server]# att: > 1d 08 00 0a 00 ff ff                             .......
[GATT server]# att: ATT PDU received: 0x1d
[GATT server]# att: ATT op 0x01
[GATT server]# att: < 01 1d 00 00 06                                   .....
[GATT server]# att: Physical link disconnected: Connection reset by peer
[GATT server]# Device disconnected: Connection reset by peer


Shutting down...
