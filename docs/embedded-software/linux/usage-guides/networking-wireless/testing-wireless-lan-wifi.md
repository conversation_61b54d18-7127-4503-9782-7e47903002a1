---
title: Testing Wireless LAN (WiFi)
description: Add document description here
---
## Test Wireless LAN in Station (STA) mode

In station mode, unit establishes a connection to a nearby access point. This is normally abbreviated as "STA" mode. The other mode is **Access Point** mode (or "AP" mode). AP mode is covered further down in this article.

[![Tutorial how-to setup Wi-Fi STA or AP mode on TechNexion Hardware.](https://img.youtube.com/vi/zZygEr8K6cQ/0.jpg)](https://www.youtube.com/watch?v=zZygEr8K6cQ)

### WIFI connection

Check if the wifi module is loaded.

For QCA WLAN:
```bash
~# lsmod
Module               Size  Used by
wlan                 4673536  0
```
Run "connmanctl" in interactive mode.
```bash
~# connmanctl
```
Scan and list the wifi hotspots, then register the agent to handle user requests.
```shell
connmanctl> scan wifi
Scan completed for wifi
connmanctl> services
    hotspot           wifi_4439c4970d84_544543484e4558494f4e_managed_psk
connmanctl> agent on
Agent registered
Connect to the hotspot and enter the passphrase.
  connmanctl> connect wifi_4439c4970d84_544543484e4558494f4e_managed_psk
 Agent RequestInput wifi_4439c4970d84_544543484e4558494f4e_managed_psk
   Passphrase = [ Type=psk, Requirement=mandatory, Alternates=[ WPS ] ]
   WPS = [ Type=wpspin, Requirement=alternate ]
 Passphrase?
 Connected wifi_4439c4970d84_544543484e4558494f4e_managed_psk
```
Quit the interactive mode of "connmanctl":
```shell
connmanctl> quit
```
Test if wifi actually works.
```bash
~# ping www.google.com
PING www.google.com (**************): 56 data bytes
64 bytes from **************: seq=0 ttl=59 time=4.905 ms
64 bytes from **************: seq=1 ttl=59 time=12.278 ms
64 bytes from **************: seq=2 ttl=59 time=4.307 ms
```
For the next boot, connman will automatically connect to the hotspot you used before.

## Test WIFI Access Point (AP) mode

In this tutorial, the unit is connected via physical Ethernet port to a local network.

In our default builds in Yocto when you connect an Ethernet cable to the unit, the unit will attempt to establish a network connection to the local network, including getting an IP address from the local DHCP server. If for some reason this doesn\'t occur, you can start a DHCP client yourself to get an IP address:

```bash
~# udhcpc -i eth0
udhcpc: started, v1.27.2
udhcpc: sending discover
udhcpc: sending select for ************
udhcpc: lease of ************ obtained, lease time 1200
/etc/udhcpc.d/50default: Adding DNS ************
/etc/udhcpc.d/50default: Adding DNS *******
/etc/udhcpc.d/50default: Adding DNS ************
```
Create hotspot with WPA + WPA2 passphrase.
```bash
~# connmanctl tether wifi on <ssid> <passphrase>
```
e.g.
```
~# connmanctl tether wifi on SAP 12345678
```
Use another device such as a mobile phone to connect to the access point **`SAP`** to get wifi connectivity.
:::info
Unlike a station connection to an Access Point, tether interfaces are not persistent across reboots.
:::
### Clean the stored settings of hotspot.
```bash
~# rm /var/lib/connman/*/settings
```
### Switch off wifi

```bash
~# connmanctl disable wifi
```
### Switch on wifi
```bash
~# connmanctl enable wifi
```
## Operating WIFI in Concurrent Mode

In many situations there may not be a wired connection available. TechNexion wireless modules such as the QCA9377 can operate in concurrent mode. In concurrent mode, it can be treated as an access point and can also establish a connection to a local access point as a Station, and then bridge the two connections. One example where you might find this useful in when provisioning a device into a wireless network for the first time.

[![Configure Wi-Fi Concurrent Mode on TechNexion SOMs](https://img.youtube.com/vi/syVJlS_FVl0/0.jpg)](https://www.youtube.com/watch?v=syVJlS_FVl0)

If when you boot your unit and after running `ifconfig` you do **not** see a `p2p0` interface, the QCA9377 kernel driver (loadable module) has not been built to enable the P2P node, so this much be turned to enable concurrent mode for NAT/forwarding usage by re-compiling wifi module. By default, distributions built with Yocto Project enable the P2P interface.

```bash
CONFIG_P2P_INTERFACE=y
```
For more detail information for building the wifi module, please refer to the following link: [https://github.com/TechNexion/qcacld-2.0](https://github.com/TechNexion/qcacld-2.0)

### Check that all of the required interfaces are present
After enabling the P2P node of wifi driver and boot up the system to
check the P2P0 node is exist or not?To check the mainly two nodes
**p2p0** and **wlan0**

```bash
root@xore-imx8mm:~# ifconfig -a
eth0      Link encap:Ethernet  HWaddr a6:4a:23:a5:03:f4
          UP BROADCAST MULTICAST DYNAMIC  MTU:1500  Metric:1
          RX packets:0 errors:0 dropped:0 overruns:0 frame:0
          TX packets:0 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000
          RX bytes:0 (0.0 B)  TX bytes:0 (0.0 B)
lo        Link encap:Local Loopback
          inet addr:127.0.0.1  Mask:*********
          inet6 addr: ::1/128 Scope:Host
          UP LOOPBACK RUNNING  MTU:65536  Metric:1
          RX packets:2 errors:0 dropped:0 overruns:0 frame:0
          TX packets:2 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000
          RX bytes:140 (140.0 B)  TX bytes:140 (140.0 B)
p2p0      Link encap:Ethernet  HWaddr 02:1f:7b:b5:8f:f6
          BROADCAST MULTICAST  MTU:1500  Metric:1
          RX packets:0 errors:0 dropped:0 overruns:0 frame:0
          TX packets:0 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:3000
          RX bytes:0 (0.0 B)  TX bytes:0 (0.0 B)
wlan0     Link encap:Ethernet  HWaddr 00:1f:7b:31:8f:f6
          BROADCAST MULTICAST  MTU:1500  Metric:1
          RX packets:0 errors:0 dropped:0 overruns:0 frame:0
          TX packets:0 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:3000
          RX bytes:0 (0.0 B)  TX bytes:0 (0.0 B)
```
To ensure all of wireless network setting has been cleared.
```bash
root@xore-imx8mm:~# rm -rf /var/lib/connman/
```
The Yocto system was using CONNMAN utility for networking management in default and executed the connmanctl utility in the command line:
```bash
root@xore-imx8mm:~# connmanctl
```
### Setup the tethering peer and make the p2p0 port enter forwarding state
```bash
connmanctl> tether wifi myAP 12345678
Wifi SSID set
Wifi passphrase set
```
where: SSID = myAP; passphrase = 12345678
```bash
connmanctl> tether wifi on
connmanctl> [  369.346704] bridge: filtering via arp/ip/ip6tables is no longer available by default. Update your scripts to load br_netfilter if you need this.
Enabled tethering for wifi
connmanctl> [  369.669782] tether: port 1(p2p0) entered blocking state
[  369.675374] tether: port 1(p2p0) entered disabled state
[  369.680937] device p2p0 entered promiscuous mode
[  369.685632] audit: type=1700 audit(1588753496.488:4): dev=p2p0 prom=256 old_prom=0 auid=4294967295 uid=0 gid=0 ses=4294967295
[  369.686241] IPv6: ADDRCONF(NETDEV_CHANGE): p2p0: link becomes ready
[  369.697038] audit: type=1300 audit(1588753496.488:4): arch=c00000b7 syscall=29 success=yes exit=0 a0=14 a1=89a2 a2=fffffad79818 a3=ffffffffffffffff items=0 ppid=1 pid=2804 auid=4294967295 uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=4294967295 comm="connmand" exe="/usr/sbin/connmand" key=(null)
[  369.704286] tether: port 1(p2p0) entered blocking state
[  369.731672] audit: type=1327 audit(1588753496.488:4): proctitle=2F7573722F7362696E2F636F6E6E6D616E64002D6E
[  369.736871] tether: port 1(p2p0) entered forwarding state
```
### Connecting the wifi to another access point

Adapt the wireless antenna and start to do scan the useful access point
in the near field:
```bash
connmanctl> scan wifi
Scan completed for wifi
```
List the services to show the access points available:
```bash
connmanctl> services
TECHNEXION-RDTEST    wifi_021f7bb58ff6_544543484e4558494f4e2d524454455354_managed_psk
TECHNEXION-RDTEST    wifi_001f7b318ff6_544543484e4558494f4e2d524454455354_managed_psk
```
Select the service id based on the **physical MAC address** of wlan0 node:

::: info NOTE
When the P2P interface is enabled, for every access point service available (physically), you will see two services listed. One of them includes the physical MAC address of the wlan0 interface (always starts with `wifi_001f7b...` in TechNexion products). The other is a slightly modified virtual service belonging to the p2p0 interface (usually starts with `wifi_0`**`2`**`1f7b`). When tethering, you need to connect to the service with the physical MAC address of `wlan0`.
:::
```bash
connmanctl> agent on
Agent registered
connmanctl> connect wifi_001f7b318ff6_544543484e4558494f4e2d524454455354_managed_psk
Agent RequestInput wifi_001f7b318ff6_544543484e4558494f4e2d524454455354_managed_psk
  Passphrase = [ Type=psk, Requirement=mandatory, Alternates=[ WPS ] ]
  WPS = [ Type=wpspin, Requirement=alternate ]
Passphrase? <Enter access point password here>
onnmanctl> [ 1619.839276] IPv6: ADDRCONF(NETDEV_CHANGE): wlan0: link becomes ready
[ 1630.578209] ip_tables: (C) 2000-2006 Netfilter Core Team
[ 1630.589920] nf_conntrack version 0.5.0 (16384 buckets, 65536 max)
[ 1630.606166] audit: type=1325 audit(1588754757.424:6): table=nat family=2 entries=0
[ 1630.684170] audit: type=1300 audit(1588754757.424:6): arch=c00000b7 syscall=273 success=yes exit=0 a0=a a1=419fd0 a2=0 a3=aitems=0 ppid=2804 pid=2908 auid=4294967295 uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=4294967295comm="modprobe" exe="/bin/kmod" key=(null)
[ 1630.709978] audit: type=1327 audit(1588754757.424:6): proctitle=2F7362696E2F6D6F6470726F62650069707461626C655F6E6174002D71
[ 1630.872002] audit: type=1325 audit(1588754757.512:7): table=nat family=2 entries=5
[ 1630.879669] audit: type=1300 audit(1588754757.512:7): arch=c00000b7 syscall=208 success=yes exit=0 a0=1b a1=0 a2=40 a3=1f02ea00items=0 ppid=1 pid=2804 auid=4294967295 uid=0 gid=0 euid=0 suid=0 fsuid=0 egid=0 sgid=0 fsgid=0 tty=(none) ses=4294967295comm="connmand" exe="/usr/sbin/connmand" key=(null)
[ 1630.906405] audit: type=1327 audit(1588754757.512:7): proctitle=2F7573722F7362696E2F636F6E6E6D616E64002D6E
connmanctl> quit
```
After leaving the connection manager, we could check there is a tether peer was added and assigned a IP address of class c network segment.
```bash
root@xore-imx8mm:~# ifconfig
eth0      Link encap:Ethernet  HWaddr a6:4a:23:a5:03:f4
          UP BROADCAST MULTICAST DYNAMIC  MTU:1500  Metric:1
          RX packets:0 errors:0 dropped:0 overruns:0 frame:0
          TX packets:0 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000
          RX bytes:0 (0.0 B)  TX bytes:0 (0.0 B)
lo        Link encap:Local Loopback
          inet addr:127.0.0.1  Mask:*********
          inet6 addr: ::1/128 Scope:Host
          UP LOOPBACK RUNNING  MTU:65536  Metric:1
          RX packets:6882 errors:0 dropped:0 overruns:0 frame:0
          TX packets:6882 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000
          RX bytes:495500 (483.8 KiB)  TX bytes:495500 (483.8 KiB)
p2p0      Link encap:Ethernet  HWaddr 02:1f:7b:b5:8f:f6
          inet6 addr: fe80::1f:7bff:feb5:8ff6/64 Scope:Link
          UP BROADCAST RUNNING MULTICAST DYNAMIC  MTU:1500  Metric:1
          RX packets:0 errors:0 dropped:0 overruns:0 frame:0
          TX packets:64 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:3000
          RX bytes:0 (0.0 B)  TX bytes:11196 (10.9 KiB)
tether    Link encap:Ethernet  HWaddr 02:1f:7b:b5:8f:f6
          inet addr:***********  Bcast:*************  Mask:*************
          inet6 addr: fe80::e02d:e4ff:fed6:dbb7/64 Scope:Link
          UP BROADCAST RUNNING MULTICAST DYNAMIC  MTU:1500  Metric:1
          RX packets:0 errors:0 dropped:0 overruns:0 frame:0
          TX packets:44 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000
          RX bytes:0 (0.0 B)  TX bytes:7855 (7.6 KiB)
wlan0     Link encap:Ethernet  HWaddr 00:1f:7b:31:8f:f6
          inet addr:xx.xx.xx.99  Bcast:10.88.88.255  Mask:*************
          inet6 addr: fe80::21f:7bff:fe31:8ff6/64 Scope:Link
          UP BROADCAST RUNNING MULTICAST DYNAMIC  MTU:1500  Metric:1
          RX packets:100 errors:0 dropped:0 overruns:0 frame:0
          TX packets:53 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:3000
          RX bytes:12613 (12.3 KiB)  TX bytes:10497 (10.2 KiB)
```
Now the wifi concurrent mode setup is complete.

### Connecting and testing concurrent mode

There are so many ways to find or scan the SSID of access point that we just set earlier (e.g. PC or cell phone). You can use your cell phone to connect to the hotspot (myAP) as usual and start using the Internet or networking.

### Troubleshooting

1.  Duplicated AP name shows when scanning wifi hotspots:

e.g.
```bash
connmanctl> services
    TECHNEXION(5G)       wifi_001f7b311132_544543484e4558494f4e28354729_managed_psk
    TECHNEXION(5G)       wifi_021f7bb51132_544543484e4558494f4e28354729_managed_psk
```
The string:
```
wifi_001f7b311132_544543484e4558494f4e28354729_managed_psk
```
The prefix `001f7b` is the MAC address specifically for TechNexion WIFI chip.
```
wifi_021f7bb51132_544543484e4558494f4e28354729_managed_psk
```
The prefix `021f7b` is the virtual MAC address generated for p2p interface. (Note: This virtual MAC address follows the [administered addresses](https://en.wikipedia.org/wiki/MAC_address) rule to be generated.)

If you don't want `p2p` to show in hotspot scan: `iw dev p2p0 del`
