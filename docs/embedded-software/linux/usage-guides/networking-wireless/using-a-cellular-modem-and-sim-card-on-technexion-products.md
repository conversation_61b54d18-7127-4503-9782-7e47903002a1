---
title: Using a Cellular Modem and SIM Card
description: Using a Cellular Modem and SIM Card
---
## Introduction
This guide lines out how to scan and connect to a 3G phone network using TechNexion products with a SIM card slot.

### Getting Started

To follow this guide a SIM-card and a (PCIe) 3G modem is needed.

Plug in the SIM card in the SIM card slot, and the modem in the corresponding PCIe / M2 slot.

Compile a driver for your modem and boot up your system.

### Scanning for Networks

The communication with the SIM card will be performed by the modem, and controlled with old-fashioned AT-commands.

First enable the modem driver if not built-in, example driver loading could be:
```bash
# modprobe usb_wwan
```
or
```bash
# modprobe option
```
but the exact modules to load depend on the modem used.

After the modem is initialized, a USB serialk interface should appear as something like `/dev/ttyUSB0` or `/dev/ttyACM0`.

Use a terminal emulator client for interactive communication with the modem, i.e.
```bash
# minicom -D /dev/ttyUSB0
```
In the terminal emulator, some usefule commands are:

-   `ATI` -- list device information
-   `AT+COPS=?` -- scan and list available phone networks
-   `AT+COPS=0,2,`short_name -- connects to network by its short name
-   `AT^SYSINFO` -- connection status

The connection status string is of the format
```bash
^SYSINFO:2,3,0,5,1,,4
```
, where:

-   the first number after the colon indicate the connection status; 0 = No connection, 2 = OK
-   the second is connection type; 0 = No connection, 1 = data, 2 = phone, 3 = both
-   the third number is roaming state; 0 = No roaming, 1 = roaming on
-   the fourth is service mode; 0 = No, 3 = GSM, 5 = WCDMA
-   the fifth number is SIM status; 0 = NG, 1 = OK, 2-4 = partially OK, 255 = No SIM card detected

### Example session
```bash
ATI
---------
OK
---------
AT+CFUN=1
---------
OK
---------
AT+CPIN="0000"
```
Some of the commands below requires to wait 2\~3 mins to get response
```bash
AT+COPS=?
--------------------------------------------------------------------
+COPS: (2,"Chunghwa Telecom","Chunghwa","46692",2),(1,"TW Mobile","TWM","46697",2),(1,"Far EasTone","FET","46601",2),,(0,1,2,3,4),(1,2)
---------------------------------------------------------------------

AT+COPS=0,2, Chunghwa
---------------------
OK
---------------------

AT^SYSINFO
-------------------------------------------------
^SYSINFO:2,3,0,5,1,,4
-------------------------------------------------
```