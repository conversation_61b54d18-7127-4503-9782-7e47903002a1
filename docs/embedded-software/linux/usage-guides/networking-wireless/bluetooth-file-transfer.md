---
title: Bluetooth File Transfer
description: Bluetooth File Transfer
---
<!-- :::(Internal) (Private notes)
This is a placeholder. For some reason, obexctl does not work (hangs) on EDM-G-IMX8M-PLUS (5.4.70) systems.
:::
 -->

## Introduction
This article show show to transfer files with Bluetooth.

### Pair a Bluetooth Device
Ensure that your other Bluetooth device is discoverable or in pairing mode, then:
```text
# bluetoothctl
[bluetooth]# pairable on
[bluetooth]# scan on
```
Copy the MAC address of the other Bluetooth device.
```text
[bluetooth]# scan off
[bluetooth]# pair <MAC address>
```
At this point you may be prompted to approve a pairing request. Do so.
```text
[bluetooth]# trust <MAC address>
[bluetooth]# quit
```
