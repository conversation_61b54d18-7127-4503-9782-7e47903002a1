---
title: Ethernet Performance Testing
description: Ethernet Performance Testing
---
## Introduction
This article shows how to test Ethernet performance. This requires an external device for the **device under test** (DUT) to connect to.

### Setup an iperf3 server on DUT
First, get the IP address of the DUT:
```text
# ifconfig
```
Then, start up iperf3 as a server:
```text
# iperf3 -s
```
On the other device (which you are using to test), run the client:
```text
Run a TCP test for 30 seconds:
# iperf3 -c <ip address of DUT> -t 30
```
```text
Run a UDP test for 30 seconds:
# iperf3 -c <ip address of DUT> -u -t 30 -P 2 -b 0
```
Either the DUT or the partner device can be the client or the server.