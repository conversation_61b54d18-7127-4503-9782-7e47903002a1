---
title: Quicker Backup of SD Cards
description: Quicker Backup of SD Cards
---
## Introduction

This article shows a script that copies an SD card to hard drive using efficient reading and minimizing the amount of data read.

The ideas are two: only copy the area that is used (i.e. partitioned with non-swap partitions), and do the reads in larger chunks than the typical erase block.

Using `fdisk -lcu /dev/sdX` the partitions are listed in number of sectors of 512 bytes. Naively reading a large count of 512 byte blocks in a `dd` command could lead to a many small reads and be potentially very inefficient. Instead the script uses the following observation:
reading *N* blocks of 512 bytes, equals reading 512 blocks of *N* bytes. It is perfectly possible to swap the block size and the count parameters in a `dd` command.

### Script

The script is as below. It takes two arguments: block device of the sd card and the filename for the copied SD card image.

```bash
#!/bin/sh

dev=$1
outfile=$2
if [ X$dev = X ] || [ Y$outfile = Y ]
then
        echo Usage : $0 device filename
        exit
fi

devminor=`basename $dev`
bs=`echo pq | fdisk -u $dev | grep -v 'swap' | grep $devminor | tail -1 | awk '{print $3 }'`
bs=`expr $bs + 1`
dd if=$dev of=$outfile bs=$bs count=512 2>&1 > /dev/null
```
