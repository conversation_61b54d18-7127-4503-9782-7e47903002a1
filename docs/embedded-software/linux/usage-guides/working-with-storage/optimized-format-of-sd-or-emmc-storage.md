---
title: Optimized format of SD or e.MMC Storage
description: Optimized format of SD or e.MMC Storage
---
This mini article shows how to format an ext4 partition on an eMMC storage or SD card in a way that can improve the performance and reduce the wear on the device.

### Background
Physically SD cards or eMMC storage devices store its content in something called *erase blocks*.One erase block is (roughly) the smallest unit that can be accessed by one read, write or erase operation. The size of an erase block varies between devices, but are typically around 256kB or 512kB. Every time a smaller amount of data is to be read or written the whole erase block is read, modified in memory, and then written again. Potentially causing many unneccesary reads and writes.

While this sounds quite bad, modern Linux kernels (and eMMC firmwares) are getting better at keeping content cached and not updating the physical storage as often. But especially older kernels and older eMMC chips this could impact performance and wear quite badly.

### Remedies
There is no 100% cure. One thing that can be done is to format the filesystem in a way that aligns its internal data blocks better with the physical characteristics of modern eMMC / SD storage. The default settings are still optimized for rotating mechanical hard drives, which are very different from modern flash-based storage.TechNexion engineers use the following commands to format Linux ext4 partitions:

```shell
$ mkfs.ext4 -E stripe-width=64,stride=64 -O sparse_super -m0 -J size=4 /dev/sddX
$ tune2fs -O ^huge_file /dev/sddX
$ tune2fs -c -1 /dev/sddX
```
where `sddX` is the partition to format.

By mount the filesystem with the `noatime` option can reduce wear and increase speed since the filesystem is not updating every file access time. (Even if updating a timestamp is not much data, due to the nature of erase blocks even small updates can cause largish writes).
