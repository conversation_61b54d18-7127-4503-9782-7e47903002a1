---
title: Building the 5.4 Kernel for PICO-IMX8M-MINI
description: Building the 5.4 Kernel for PICO-IMX8M-MINI
---
## Introduction
This article will take you through the steps of building the Linux kernel for the PICO-IMX8M-MINI.

### Download and setup a cross toolchain
If you haven't already done so, you will need download and setup a [cross toolchain for a 64-bit ARM target SOC](/docs/embedded-software/linux/usage-guides/kernel-bootloader/preparing-a-toolchain-for-building-arm-binaries-on-x86-hosts).

### Clone the Linux kernel git repository and checkout the 5.4 branch:
```bash
$ git clone https://github.com/TechNexion/linux-tn-imx.git
$ git checkout tn-imx_5.4.47_2.2.0-next
```
:::info
As of the writing of this article the latest version of the kernel for Yocto 3.0 is 5.4.70
:::

### Configure the kernel to build for the 64-bit i.MX8 target:
```bash
$ make tn_imx8_defconfig
```
### Build the kernel:
```bash
$ make -j$(nproc)
```
The resulting binary image file is located in `arch/arm64/boot/Image` .

### Locate the device tree blobs:

By default the following device tree blobs (binaries) are located in `arch/arm64/boot/dts/freescale` .

| File Name | Description |
|-----------------------------|------------------------------------------------------------|
| imx8mm-pico-pi.dtb |          Basic device tree to enable PICO-PI functions (no display) |
|  imx8mm-pico-pi-ili9881c.dtb |  Device tree with 5\" MIPI 720x1280 MIPI display enabled |
|  imx8mm-pico-pi-voicehat.dtb  | Device tree with voicehat enabled (no display) |

### Specify DTB to be loaded in kernel

The DTB must be loaded into memory prior to kernel boot. This is done with U-boot. You specify the DTB file to be loaded in an environment variable in U-boot that is named `fdt_file`. U-boot then scans the boot partition (a FAT partition) for a file of that name. If it finds this file, it will load it into the prescribed memory to location prior to loading and booting the kernel.

You can list the DTB files available in the boot partition using the `fatls` command. First, you can figure out which is the correct boot device using the `mmc list` command:
```bash
u-boot=> mmc list
FSL_SDHC: 1
FSL_SDHC: 2 (eMMC)
```
Assuming we are booting from e.MMC, the mmc device is 2.

Then run the `fatls` command:
```bash
u-boot=> fatls mmc 2
 23715848   Image
     6320   hello_world.bin
    51947   imx8mm-pico-pi-ili9881c.dtb
    54429   imx8mm-pico-pi-voicehat.dtb
    51425   imx8mm-pico-pi.dtb
    16888   rpmsg_lite_pingpong_rtos_linux_remote.bin
    16520   rpmsg_lite_str_echo_rtos_imxcm4.bin
   209878   splash.bmp
```
Then, you can set the environment variable `fdt_file` to the DTB of your choice:
```
u-boot=> setenv fdt_file imx8mm-pico-pi-ili9881c.dtb
u-boot=> saveenv
```