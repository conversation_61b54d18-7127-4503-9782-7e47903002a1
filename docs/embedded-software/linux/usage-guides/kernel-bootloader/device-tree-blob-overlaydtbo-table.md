---
title: Device Tree Blob Overlay(DTBO) table
description: Device Tree Blob Overlay(DTBO) table
---
#### **DTBO naming:**

Device tree overlay files are named in the kernel source code using the following pattern:
**`[soc]-[form_factor]-[baseboard]-[peripheral]-overlay.dts`**

After compilation:
**`[soc]-[form_factor]-[baseboard]-[peripheral].dtbo`**

#### Examples (linux 5.4.70):

| **DTBO source code** | **DTBO file** | **dtoverlay value** | **Deccription** |
| --- | --- | --- | --- |
| imx8mp-edm-g-wb-hdmi2mipi-tc358743-dual-overlay.dts | imx8mp-edm-g-wb-`hdmi2mipi-tc358743-dual`.dtbo | hdmi2mipi-tc358743-dual | Dual HDMI to MIPI(tc358743) |
| imx8mp-edm-g-wb-hdmi2mipi-tc358743-single-cam1-overlay.dts | imx8mp-edm-g-wb-`hdmi2mipi-tc358743-single-cam1`.dtbo | hdmi2mipi-tc358743-single-cam1 | HDMI to MIPI(tc358743) |
| imx8mp-edm-g-wb-lvds-vl10112880-overlay.dts | imx8mp-edm-g-wb-`lvds-vl10112880`.dtbo | lvds-vl10112880 | Native LVDS 10“ m101nwwb |
| imx8mp-edm-g-wb-lvds-vl15613676-overlay.dts | imx8mp-edm-g-wb-`lvds-vl15613676`.dtbo | lvds-vl15613676 | Native LVDS LVDS 15” g156xw01 |
| imx8mp-edm-g-wb-lvds-vl215192108-overlay.dts | imx8mp-edm-g-wb-`lvds-vl215192108`.dtbo | lvds-vl215192108 | Native LVDS 21“ g215hvn01 |
| imx8mp-edm-g-wb-tevi-ap1302-overlay.dts | imx8mp-edm-g-wb-`tevi-ap1302`.dtbo | tevi-ap1302 | TEVI AR Series Camera |
| imx8mp-edm-g-wb-tevi-ov5640-overlay.dts | imx8mp-edm-g-wb-`tevi-ov5640`.dtbo | tevi-ov5640 | TEVI OV5640 Camera |
| imx8mm-edm-g-wb-hdmi2mipi-tc358743-overlay.dts | imx8mm-edm-g-wb-`hdmi2mipi-tc358743`.dtbo | hdmi2mipi-tc358743 | HDMI to MIPI(tc358743) |
| imx8mm-pico-pi-clixnfc-overlay.dts | imx8mm-pico-pi-`clixnfc`.dtbo | clixnfc | CLIX NFC |
| imx8mm-pico-wizard-clix1nfc-overlay.dts | imx8mm-pico-wizard-`clix1nfc`.dtbo | clix1nfc | CLIX1 NFC |
| imx8mm-pico-wizard-clix2nfc-overlay.dts | imx8mm-pico-wizard-`clix2nfc`.dtbo | clix2nfc | CLIX2 NFC |
| imx8mm-pico-wizard-ili9881c-overlay.dts | imx8mm-pico-wizard-`ili9881c`.dtbo | ili9881c | MIPI 5“ ili9881c |
| imx8mm-pico-wizard-g080uan01-overlay.dts | imx8mm-pico-wizard-`g080uan01`.dtbo | g080uan01 | MIPI 8” g080uan01 |
| imx8mm-pico-wizard-g101uan02-overlay.dts | imx8mm-pico-wizard-`g101uan02`.dtbo | g101uan02 | MIPI 10“ g101uan02 |
| imx8mm-pico-wizard-mipi2hdmi-adv7535-overlay.dts | imx8mm-pico-wizard-`mipi2hdmi-adv7535`.dtbo | mipi2hdmi-adv7535 | MIPI to HDMI(adv7535) |
| imx8mm-pico-wizard-voicehat-overlay.dts | imx8mm-pico-wizard-`voicehat`.dtbo | voicehat | TN VoiceHat |
| imx8mm-pico-wizard-sn65dsi84-vl10112880-overlay.dts | imx8mm-pico-wizard-`sn65dsi84-vl10112880`.dtbo | sn65dsi84-vl10112880 | MIPI to LVDS 10” m101nwwb |
| imx8mm-pico-wizard-sn65dsi84-vl15613676-overlay.dts | imx8mm-pico-wizard-`sn65dsi84-vl15613676`.dtbo | sn65dsi84-vl15613676 | MIPI to LVDS 15“ g156xw01 |
| imx8mm-pico-wizard-sn65dsi84-vl215192108-overlay.dts | imx8mm-pico-wizard-`sn65dsi84-vl215192108`.dtbo | sn65dsi84-vl215192108 | MIPI to LVDS 21” g215hvn01 |
| imx8mm-flex-pi-clixwifi-overlay.dts | imx8mm-flex-pi-`clixwifi`.dtbo | clixwifi | CLIX WIFI |
| imx8mm-flex-wizard-clix1wifi-overlay.dts | imx8mm-flex-wizard-`clix1wifi`.dtbo | clix1wifi | CLIX1 WIFI |
| imx8mq-pico-pi-dual-overlay.dts | imx8mq-pico-pi-`dual`.dtbo | dual | Dual display with HDMI and MIPI 5" ili9881c |

#### How to use on u-boot:

Suppose you want to use MIPI 5" panel, Camera OV5640, VoiceHat and CLIX-NFC peripherals at the same time:

```
u-boot=> setenv dtoverlay "ili9881c ov5640 voicehat chix1nfc" 
```

or set dtoverlay into uEnv.txt:

```
dtoverlay="ili9881c ov5640 voicehat chix1nfc" 
```
