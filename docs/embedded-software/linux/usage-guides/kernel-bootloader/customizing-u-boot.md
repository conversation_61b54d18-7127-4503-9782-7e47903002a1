---
title: Customizing U-boot
description: Customizing U-boot
---
## Introduction

Customizing U-boot for your platform is sometimes necessary in order to add features, change the boot splash screen, and enable new hardware. This article takes you through the process of downloading and building U-boot yourself for your target platform.
This article assumes that you are building U-boot from the [TechNexion u-boot repository](https://github.com/TechNexion/u-boot-tn-imx/).

### Toolchain

You need to set up a toolchain to use to build U-boot. We use different toolchains depending on the processor architecture (32-bit or 64-bit ARM). For this, please refer to the following article.

[Preparing a Toolchain](/docs/embedded-software/linux/usage-guides/kernel-bootloader/preparing-a-toolchain-for-building-arm-binaries-on-x86-hosts)

Once you have the toolchain installed, make sure that the **ARCH** and **CROSS_COMPILE** environment variables are set correctly so that these are used to build U-boot.

For 32-bit ARM targets (i.MX6UL/ULL, i.MX6, i.MX7):
```bash
$ echo ${ARCH}
arm
$ echo ${CROSS_COMPILE}
arm-linux-gnueabihf-
```
For 64-bit ARM targets (i.MX8M, i.MX8M Mini, etc.)
```bash
$ echo ${ARCH}
arm64
$ echo ${CROSS_COMPILE}
aarch64-linux-gnu-
```
Finally, make sure that you have the toolchain in your **PATH** environment variable. One simple way to do this is to make sure you can run the cross compiler from the shell:
```bash
$ ${CROSS_COMPILE}gcc --version
arm-linux-gnueabihf-gcc (Linaro GCC 6.4-2018.05) 6.4.1 20180425 [linaro-6.4-2018.05 revision7b15d0869c096fe39603ad63dc19ab7cf035eb70]
Copyright (C) 2017 Free Software Foundation, Inc.
This is free software; see the source for copying conditions. There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
```
### Download U-boot

Get the source code for U-boot. Here we are checking out the latest stable branch. If you want to work with the latest code, some of which might not be completely tested, you can use the branch ending in -next.
```bash
$ git clone https://github.com/TechNexion/u-boot-tn-imx.git
$ cd u-boot-tn-imx
```
Checkout the stable branch:
```bash
$ git checkout tn-imx_v2018.03_4.14.98_2.0.0_ga-stable
```
(Or whichever branch you wish to use)

### Configure U-boot for your target platform

Now configure u-boot for the target platform you are using. This depends on the board, based on the following table:

|  Board                                               |  Configuration File
|  -----------------------------------------------------| ---------------------------
|  PICO-IMX7D                                     |       pico-imx7d_spl_defconfig
|  PICO-IMX6UL, PICO-IMX6ULL        |                     pico-imx6ul_spl_defconfig
|  PICO-IMX6                                        |     pico-imx6_spl_defconfig
|  PICO-IMX8M                                     |       pico-imx8mq_defconfig
|  PICO-IMX8M-MINI                            |           pico-imx8mm_defconfig
|  EDM1-IMX6 Solo, EDM1-IMX6 Dual Lite, EDM1-IMX6 Quad |  edm-imx6_spl_defconfig
|  EDM-IMX8M                                      |       edm-imx8mq_defconfig
|  AXON-IMX8M-MINI                          |             axon-imx8mm_defconfig
|  FLEX-IMX8M-MINI                            |           flex-imx8mm_defconfig
|  XORE-IMX8M-MINI                           |           xore-imx8mm_defconfig
|  TEP0700-IMX7                                  |        tep1-imx7d_spl_defconfig
|  TEP0500-IMX6UL/TEP0700-IMX6UL |                         tep-imx6ul_spl_defconfig

### Configure U-boot
```bash
$ make
```
For example, if you were using PICO-IMX8M-MINI:
```bash
$ make pico-imx8mm_defconfig
```
### Build U-boot for the first time
```bash
$ make
```
U-boot is not a very large project to build, so this usually doesn\'t take very long on most computers. If you have multiple CPU cores available, you can use the \"-j\" switch to increase the number of parallel threads used and speed up the build. If you have 4 CPUs available, then you can do the following:
```bash
$ make -j 4
```
The resulting binary images are placed in the project directory. There is an SPL (which means Secondary Program Loader) and a U-boot binary image. The SPL is loaded into the boot media (in our case, this is always EMMC or microSD card) at a specific location. On 32-bit processors, this starts at 1KB into the storage device (or 2 512-byte blocks) in). On the 64-bit products (e.g. i.MX8M, i.MX8M-MINI), this is 33KB into the boot media. The SOC expects to find the SPL located at that specific location in the boot media.

On the 32-bit SOCs, the resulting u-boot binary (the main u-boot image) is a file named \"u-boot.img\". This file is loaded by the SPL from the boot partition, which is a FAT filesystem in the first partition. (NOTE: this is likely going to change because we want to use the method to load U-boot from all of our SOC types).
On the 64-bit SOCs, the resulting u-boot binary is named u-boot.bin. This file is combined with a number of other binary firmware files and is stored into raw block space in the EMMC or microSD card.

If you wanted to make a change to the overall U-boot configuration, you can use the configuration menu.
```bash
$ make menuconfig
```
Once you are done, you can save the configuration and rebuild:
```bash
$ make
```