---
title: How to Resize the Debug Console
description: How to Resize the Debug Console
---
This guide explains how the size of a debug console terminal can be increased from the default 80 times 25 characters.

## Introduction and Background
The terminal in the debug console often defaults it size to 80 times 25 characters. This is the traditional terminal size, chosen to match the size of 1970s punch cards. Modern text terminal windows can support much larger "resolutions". Doing so will not only give better overview it can also reduce some of the annoying issues `bash` has with line wrapping.While it sounds simple, it is not, since there are surprisingly many layers involved:

-   Topmost we have an (X11) text terminal emulator, such as xterm. This is typically running on the client PC that connects to the board using a serial debug console.
-   Inside the X11 terminal application, a *local* terminal is mapped.
-   Inside the local terminal, there is a *local*shell running.
-   Inside the local shell, a terminal emulator (such as `minicom`) is running.
-   The local terminal emulator connects to the remote terminal on the board using a serial line.
-   Inside the remote terminal, there is a remote shell running.

To successfully have a larger than 80×25 debug console, the change has to be present in all-of-the above.

## Steps

The steps that are really needed can vary a little becauses of differences of what applications are used for the layers above. There often can be signalling between them, so a change in one changes the size parameters in some of the others (like when you resize the terminal window (change in Xorg/X11), the terminal inside the window gets the changes automatically, and then signals the shell inside.In the example here we set the debug console terminal size to 140×50 characters.In the debug console issue the following commands:

```shell
$ stty -F /dev/tty cols 140
$ stty -F /dev/tty rows 50
$ reset
```

This should set things right on the remote unit. Sometimes these changes are not enough, but a resizing of the local terminal window (X11) often helps.In the worst case you also need to apply the same stty commands on the remote debug port, i.e:
```shell
$ stty -F /dev/ttymxc0 cols 140
$ stty -F /dev/ttymxc0 rows 50
$ reset
```
where `ttymxc0` corresponds to the debug console on the remote system.

Such simple things can be so complicated.
