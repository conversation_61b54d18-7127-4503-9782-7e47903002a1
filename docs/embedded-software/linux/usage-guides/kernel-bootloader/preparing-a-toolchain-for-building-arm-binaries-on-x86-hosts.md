---
title: Preparing a Toolchain for Building ARM Binaries on x86 Hosts
description: Preparing a Toolchain for Building ARM Binaries on x86 Hosts
sidebar_position: 1
---

:::warning
This article requires an update to recent versions of software.
:::

## Introduction
If you are building from source with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> automatically builds a native toolchain from source. In many cases, you will also want to build binaries for 32-bit or 64-bit ARM targets without using <PERSON><PERSON><PERSON> to build a toolchain for you. The following steps will show you how to download and prepare your host with a Linaro toolchain.

### Download the toolchain from Linaro

For 32-bit targets such as NXP i.MX6, i.MX6UL, and i.MX7, the toolchain you use depends on the version of kernel and bootloader, as well as the target processor architecture:

| Toolchain              |     U-boot                                   |           Linux Kernel        |
|  ------------------ |-------------------------------- |-------------------------|
| GCC-4.6.2_201112  | U-boot 2013.10, U-boot 2014.10  |  3.0.35, 3.10.53 |
|  GCC-5.1_201508   |  U-boot 2015.04, U-boot 2017.03  | 3.14.52, 4.1.15, 4.9.88 |

Currently, for 64-bit ARM targets such as PICO-IMX8M, we are using the following Linaro GCC toolchain, which can be downloaded from here:

[gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu.tar.xz](https://releases.linaro.org/components/toolchain/binaries/6.4-2017.11/aarch64-linux-gnu/gcc-linaro-6.4.1-2017.11-x86_64_aarch64-linux-gnu.tar.xz)

### Install the toolchain
We normally install the toolchain into the /opt directory on our Linux host:
```bash
tar xvf [toolchain-file.tar.xz] -C /opt/
```
### Export the environment
When you are building a binary for target CPU architectures other than that of your host, you are cross-compiling. To do that with GCC, you should set up certain environment variables that are used by the other tools to ensure that you are calling the correct toolchain executables when building. For kernel and u-boot, you should also set the ARCH variable to the appropriate processor architecture.
```bash
export PATH=$PATH:/opt/[toolchain directory]/bin
```
For ARM64 (64-bit ARM) targets such as i.MX8M:
```bash
export ARCH=arm64
export CROSS_COMPILE=/opt/[toolchain directory]/bin/aarch64-linux-gnu-
```
For 32-bit ARM targets such as i.MX6 and i.MX7:
```bash
export ARCH=arm
export CROSS_COMPILE=/opt/[toolchain directory]/bin/arm-linux-gnueabihf-
```
:::info
You will need to do this in every Linux terminal in which you intend to build for your target.
:::
