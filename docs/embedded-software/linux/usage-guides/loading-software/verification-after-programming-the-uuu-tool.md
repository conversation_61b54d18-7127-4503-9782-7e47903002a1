---
title: Verification after programming the 'uuu' tool
description: Verification after programming the 'uuu' tool
sidebar_position: 3
---
## Introduction
This article will walk you through the process of using **uuu** tool to CRC verify a flashed storage device.

:::info
`uuu` tool must be using version 1.5.x or later.
:::

## Prepare in advance
### Make sure that the installed UUU supports CRC validation
You can use command "uuu -H" on your host with more details.
```bash
  FB[-t timeout]:\Fastboot: android fastboot protocol (timeout in ms).
              getvar
              ucmd <any uboot command>
              acmd <any never returned uboot command, like booti, reboot>
              flash [-raw2sparse [{-no-bmap|-bmap <bmap_filename}]] <partition> <filename>
              download -f <filename>
              crc -f <filename> [-format "mmc read $loadaddr"] [-blksz 512] [-each 0x4000000]
                                [-seek 0] [-skip 0] [-nostop]
                                  each          CRC size each loop
                                  seek          skip bytes from storage
                                  skip          skip bytes from -f
                                  nostop        continue check even if found mismatch
```

### Generate a new flash.bin file
Since TechNexion's u-boot does not have the CRC32 command enabled by default, it must be enabled. Download the source code to make changes and cross-compile it. For this, please refer to the following article.
[Customizing U-boot](https://developer.technexion.com/docs/customizing-u-boot)

:::info
`u-boot` recommends [tn-imx_v2022.04_5.15.71_2.2.0-next](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2022.04_5.15.71_2.2.0-next) branch or the latest version.
:::

Before cross-compiling, delete this line in the defconfig file you want to modify.
```text
# CONFIG_CMD_CRC32 is not set
```

And insert this line.

```text
CONFIG_CRC32_VERIFY=y
```

![image.png](//img/image%2875%29.png)

![image.png](//img/image%2878%29.png)

**For ARM64(e.g. i.MX8 and i.MX9)**
After the compilation is complete, a new flash.bin file needs to be generated. Use a script(install_uboot_imx8.sh) in uboot to generate new flash.bin or build with yocto.

**For EDM-G-IMX8MP with WB/WIZARD**
```bash
./install_uboot_imx8.sh -b imx8mp-edm-g.dtb -d /dev/null
```

## Use uuu shell mode to verify the image under Linux

1. While the target board is powered off, set the boot jumpers to boot from serial download. Then, USB Type-C peripheral cable to the board, and the other end to the host PC. If the target board is powered using another source than USB-OTG (via a Type C connector), then connect power to the unit.

2. Execute `uuu` to start verifying process.

```bash
$ sudo ./uuu -s flash.bin
uuu (Universal Update Utility) for nxp imx chips -- libuuu_1.5.109-79-g48806e5

Build in config:
  Pctl     Chip        Vid     Pid     BcdVersion  Serial_No
  ==================================================
  SDPS:    MX8QXP      0x1fc9  0x012f  [0x0002..0xffff]
  SDPS:    MX8QM       0x1fc9  0x0129  [0x0002..0xffff]
  SDPS:    MX8DXL      0x1fc9  0x0147
  SDPS:    MX28        0x15a2  0x004f
  SDPS:    MX815       0x1fc9  0x013e
  SDPS:    MX865       0x1fc9  0x0146
  SDPS:    MX8ULP      0x1fc9  0x014a
  SDPS:    MX8ULP      0x1fc9  0x014b
  SDPS:    MX93        0x1fc9  0x014e
  SDPS:    MX93        0x1fc9  0x0159
  SDPS:    MX95        0x1fc9  0x015d
  SDP:     MX7D        0x15a2  0x0076
  SDP:     MX6Q        0x15a2  0x0054
  SDP:     MX6D        0x15a2  0x0061
  SDP:     MX6SL       0x15a2  0x0063
  SDP:     MX6SX       0x15a2  0x0071
  SDP:     MX6UL       0x15a2  0x007d
  SDP:     MX6ULL      0x15a2  0x0080
  SDP:     MX6SLL      0x1fc9  0x0128
  SDP:     MX7ULP      0x1fc9  0x0126
  SDP:     MXRT106X    0x1fc9  0x0135
  SDP:     MX8MM       0x1fc9  0x0134
  SDP:     MX8MQ       0x1fc9  0x012b
  SDPU:    SPL         0x0525  0xb4a4  [0x0000..0x04ff]
  SDPV:    SPL1        0x0525  0xb4a4  [0x0500..0x9998]
  SDPV:    SPL1        0x1fc9  0x0151  [0x0500..0x9998]
  SDPU:    SPL         0x0525  0xb4a4  [0x9999..0x9999]
  SDPU:    SPL         0x3016  0x1001  [0x0000..0x04ff]
  SDPV:    SPL1        0x3016  0x1001  [0x0500..0x9998]
  FBK:             0x066f  0x9afe
  FBK:             0x066f  0x9bff
  FBK:             0x1fc9  0x0153
  FB:          0x0525  0xa4a5
  FB:          0x18d1  0x0d02
  FB:          0x3016  0x0001
  FB:          0x1fc9  0x0152
  FB:          0x0483  0x0afb

New USB Device Attached at 4:1-15185800D8DBA4A5
New USB Device Attached at 3:2-15185800D8DBA4A5
3:2-15185800D8DBA4A5>Start Cmd:SDPS: boot -f "flash.bin"
100%3:2-15185800D8DBA4A5>Okay (2.319s)
3:2-15185800D8DBA4A5>Start Cmd:SDPS: done
3:2-15185800D8DBA4A5>Okay (0s)
Please input command:
U>
```

:::info
`flash.bin` is the version with the CRC32 command enabled.
:::

3. Then enter the following command to perform CRC verification.
> FB: ucmd setenv fastboot_dev mmc
> FB: ucmd setenv mmcdev ```${emmc_dev}```
> FB: ucmd mmc dev ```${emmc_dev}```
> FB: crc -f imx-image-full-edm-g-imx8mp.rootfs.wic.[bz2|zst]/*

:::info
.[bz2|zst] Just select one of the compressed files.
`imx-image-full-edm-g-imx8mp.rootfs.wic.[bz2|zst]` is the image that has been flashed in. It supports ZST and BZ2 compressed files, so you don't need to unzip them beforehand.
:::

```bash
U>FB: ucmd setenv fastboot_dev mmc
>Start Cmd:FB: ucmd setenv fastboot_dev mmc
New USB Device Attached at 4:1
4:1>Okay (0.023s)
Okay
U>FB: ucmd setenv mmcdev ${emmc_dev}
4:1>Start Cmd:FB: ucmd setenv mmcdev ${emmc_dev}
New USB Device Attached at 4:1
4:1>Okay (0.077s)
Okay
U>FB: ucmd mmc dev ${emmc_dev}
4:1>Start Cmd:FB: ucmd mmc dev ${emmc_dev}
New USB Device Attached at 4:1
4:1>Okay (0.047s)
Okay
U>FB: crc -f imx-image-full-edm-g-imx8mp.rootfs.wic.zst/*
4:1>Start Cmd:FB: crc -f image/imx-image-full-edm-g-imx8mp.rootfs.wic.zst/*
New USB Device Attached at 4:1
100%4:1>Okay (106.6s)
Okay
U>
```

## Use uuu extra script to flash and verify the image under Linux
The extra scripting methods integrate flashing and validation steps.

:::info
Currently, BMAP and CRC cannot be used at the same time.
:::

### Prepare the files to flash
Copy the flash.bin to the uuu path.

:::info
`flash.bin` is the same version with the CRC32 command enabled.
:::

Copy xxx.rootfs.wic.[bz2|zst] and xxx.rootfs.wic.bmap from the image file created by Yocto to the uuu path.

:::info
`.[bz2|zst]` Just select one of the compressed files.
:::

### Generate an extra script file
Create a script called `uuu.auto`. It reads as follows:
```
uuu_version 1.5.109
# This command will be run when i.MX8MM, i.MX8MQ, i.MX8MP
SDP: boot -f flash.bin
# This command will be run when ROM support stream mode
# i.MX8QXP, i.MX8QM
SDPS: boot -f flash.bin
# These commands will be run when use SPL and will be skipped if no spl
# SDPU will be deprecated. please use SDPV instead of SDPU
# {
SDPU: delay 1000
SDPU: write -f flash.bin -offset 0x57c00
SDPU: jump
# }
# These commands will be run when use SPL and will be skipped if no spl
# if (SPL support SDPV)
# {
SDPV: delay 1000
SDPV: write -f flash.bin -skipspl
SDPV: jump
# }
FB: ucmd setenv fastboot_dev mmc
FB: ucmd setenv mmcdev ${emmc_dev}
FB: ucmd mmc dev ${emmc_dev}
FB: flash -raw2sparse all imx-image-full-edm-g-imx8mp.rootfs.wic.zst/*
FB: crc -f imx-image-full-edm-g-imx8mp.rootfs.wic.zst/*
FB: ucmd mmc partconf ${emmc_dev} 0 0 0
FB: done
```

:::info
It supports ZST and BZ2 compressed files, so you don't need to unzip them beforehand. The filenames in the extra script are hard-coded. Therefore, the file name of the file to be flashed should correspond to the script. The extra script and the flash file should also be placed in the same path, otherwise the full path must be specified in the script.
:::

### uuu command with an extra script file
```
sudo ./uuu -d uuu.auto
```
<br />

**Results in console logs**
![image.png](//img/image%2879%29.png)