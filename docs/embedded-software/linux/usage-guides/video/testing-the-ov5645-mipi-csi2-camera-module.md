---
title: Testing the OV5645 MIPI CSI2 camera module
description: Testing the OV5645 MIPI CSI2 camera module
---
## Camera preview:

### For i.MX8MQ:
```bash
gst-launch-1.0 v4l2src io-mode=3 device=/dev/video0 ! video/x-raw,width=1280,height=720 ! waylandsink
```
or
```bash
gst-launch-1.0 v4l2src io-mode=3 device=/dev/video0 ! video/x-raw,width=1280,height=720 ! kmssink
```

### For i.MX8MM:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=1920,height=1080 ! glimagesink
```
or
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=1920,height=1080 ! waylandsink window-width=1280 window-height=720
```

## Camera Preview on development kits with 5″ MIPI DSI display
:::info
PICO-PI-IMX8M-MINI, FLEX-PI-IMX8M-MINI, AXON-PI-IMX8M-MINI, or with AXON-WIZARD and PICO-WIZARD boards with 5" MIPI Display Adapter.
:::

If you are using our i.MX8M-MINI modules (PICO, FLEX, AXON) with our 5″ MIPI-DSI panel with Yocto 2.5 with Wayland/Weston, here are some commands that are useful for showing a camera preview on the display at different resolutions.

#### Changing Weston screen orientation to landscape mode

On the 5″ MIPI-DSI panel, the default orientation is portrait mode (720×1280). This is fine for some applications, but makes viewing some normal aspect video a little tricky. For the preview commands below, you may need to change the screen orientation in Weston to landscape mode (1280×720.You can do this by editing the file `/etc/xdg/weston/weston.ini`Make sure that these lines are located in it:
`[output]name=DSI-1`
`transform=90`
Then reboot the unit.

### Preview Pipelines

#### 640×480:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=640,height=480,framerate=30/1 ! waylandsink window-width=1280 window-height=720
```

#### 1280×720:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=1280,height=720,framerate=30/1 ! waylandsink window-width=1280 window-height=720
```

#### 1920×1080:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=1920,height=1080,framerate=30/1 ! waylandsink window-width=1280 window-height=720
```

#### 2592×1944:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=2592,height=1944,framerate=15/1 ! waylandsink window-width=1280 window-height=720
```

For the max resolution, the maximum frame rate you can use is 15 frames per second (framerate = 15/1). On the other resolutions, due to a limitation in the OV5645 camera driver, only 30 fps is supported.Note:The parameter `width=1280 window-height=720` for `waylandsink` corresponds to the display resolution. It has to use Yocto with `xwayland backend`. On a `wayland-only backend` it can't display properly. It only shows green on display. This is a known issue in Yocto 2.5.

### Increasing the Preview Framerate
Through some experimentation we've noticed that the frame rate of the pipeline can run a little slower than the ideal framerate. This is likely due to some overhead with moving data around in memory. What we've found is that by adding the NXP plugin imxvideoconvert_g2d into the pipeline, we can achieve frame rates that are close to the ideal framerate, at the expense of a little CPU overhead.

#### 640×480:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=640,height=480,framerate=30/1 ! `**`imxvideoconvert_g2d`**` ! waylandsink window-width=1280 window-height=720
```

#### 1280×720:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=1280,height=720,framerate=30/1 ! `**`imxvideoconvert_g2d`**` ! waylandsink window-width=1280 window-height=720
```

#### 1920×1080:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=1920,height=1080,framerate=30/1 ! `**`imxvideoconvert_g2d`**` ! waylandsink window-width=1280 window-height=720
```

#### 2592×1944:
```bash
gst-launch-1.0 v4l2src device=/dev/video0 ! video/x-raw,width=2592,height=1944,framerate=15/1 ! `**`imxvideoconvert_g2d`**` ! waylandsink window-width=1280 window-height=720
```

## Capturing and compressing H.264 video with i.MX8M Mini-based modules

#### 640×480:
```bash
gst-launch-1.0 v4l2src -e device=/dev/video0 ! video/x-raw,width=640,height=480,framerate=30/1 ! vpuenc_h264 ! h264parse ! mp4mux ! filesink location=test-640x480.mp4
```

#### 1280×720:
```bash
gst-launch-1.0 v4l2src -e device=/dev/video0 ! video/x-raw,width=1280,height=720,framerate=30/1 ! vpuenc_h264 ! h264parse ! mp4mux ! filesink location=test-1280x720.mp4
```

#### 1920×1080:
```bash
gst-launch-1.0 v4l2src -e device=/dev/video0 ! video/x-raw,width=1920,height=1080,framerate=30/1 ! vpuenc_h264 ! h264parse ! mp4mux ! filesink location=test-1920x1080.mp4
```

The "-e" argument informs the pipeline that an EOS should be generated when the program is terminated. This gracefully terminates the pipeline and saves the resulting file.Note: The vpuenc_h264 element not capable of encoding greater than 1920×1080 resolution video, so this is why we aren't showing a pipeline for the full 5MP resolution.
