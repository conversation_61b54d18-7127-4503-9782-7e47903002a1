---
title: Use Flexbuild to Build a Debian Docker Image with HW Acceleration
description: Use Flexbuild to Build a Debian Docker Image with HW Acceleration
---
## Introduction
This guide demonstrates how to create a Docker image with hardware acceleration capabilities using Flexbuild for NXP platforms. The resulting Docker container can leverage GPU and ML acceleration from the host system.

## Prerequisites

- **Docker**: Ensure Docker is installed on your development system. Follow the [official Docker installation guide](https://docs.docker.com/get-docker/) for your operating system.
- **Flexbuild**: A build system for creating customized Linux distributions for NXP platforms. For detailed setup instructions, refer to the [TechNexion documentation](/docs/embedded-software/linux/debian/usages-guides/how-to-build-and-deploy-debian-using-flexbuild).
- **Storage**: At least 15GB of free disk space for the build process.
- **System**: A Linux-based development host with necessary packages installed.

## Option 1: Use TechNexion's pre-built image on the Docker Hub

TechNexion provides ready-to-use Docker images with hardware acceleration support. This is the quickest way to get started.

### Step 1: Pull the pre-built image

```bash
# Pull the latest TechNexion Debian image with hardware acceleration
docker pull technexion/tn-image-debian-container:bookworm_24.12
```

### Step 2: Run the container with hardware access

```bash
docker run -it \
  --name technexion-debian \
  -v /run/user:/run/user \
  -v /usr/share/fonts:/usr/share/fonts \
  --device /dev:/dev \
  technexion/tn-image-debian-container:bookworm_24.12 bash
```

## Option 2: Build your own Docker image using Flexbuild

If you need a customized Docker image, follow these steps to build it yourself.

### Step 1: Set up the build environment

```bash
# Navigate to the Flexbuild directory
cd tn_debian_flexbuild

# Source the environment setup script
. setup.env
```

### Step 2: Build the customized Debian rootfs

This sequence creates a Debian root filesystem that includes NXP's Graphics Acceleration Library and Machine Learning Library:

```bash
# Build the base rootfs
bld rfs -p IMX -f tn_technexion.yml

# Build required applications
bld apps -p IMX -f tn_technexion.yml

# Merge applications into the rootfs
bld merge-apps -p IMX -f tn_technexion.yml

# Package the rootfs for distribution
bld packrfs -p IMX -f tn_technexion.yml
```

### Step 3: Create a Dockerfile

Navigate to the images directory:

```bash
cd build_lsdk2412/images
```

Create a Dockerfile with the following content:

```dockerfile
FROM scratch
ADD rootfs_lsdk2412_debian_desktop_arm64.tar.zst /
ENV XDG_RUNTIME_DIR=/run/user/0
ENV WAYLAND_DISPLAY=wayland-1
CMD ["/usr/bin/bash"]
```

### Step 4: Build the Docker image

Build the Docker image with the proper architecture specification:

```bash
docker buildx build --platform linux/arm64 -t debian-nxp-hw-accel:latest .
```

### Step 5: Save the Docker image as a tar file

```bash
docker save debian-nxp-hw-accel:latest -o debian-nxp-hw-accel.tar
```

### Step 6: Deploy to the target board

Transfer the tar file to your NXP-based target board using either USB drive, SSH/SCP, or any other file transfer method:

```bash
# Example using scp
scp debian-nxp-hw-accel.tar user@target-board:/home/<USER>/
```

On the target board, load the Docker image:

```bash
docker load -i debian-nxp-hw-accel.tar
```

### Step 7: Run the Docker container with hardware access

```bash
docker run -it \
  --name debian-hw-accel \
  -v /run/user:/run/user \
  -v /usr/share/fonts:/usr/share/fonts \
  --device /dev:/dev \
  debian-nxp-hw-accel:latest bash
```

## Testing Hardware Acceleration

After launching the container, you can verify hardware acceleration is working:

```bash
# Run glmark2 to test GPU acceleration
glmark2-es2-wayland

# Run other GPU or ML demos as needed
```

## Suggestion
:::info
Host yocto system is best to use `tn-image-docker` or `tn-image-podman` target image. These are the yocto images after shrinking. With docker image, the host doesn't need to have that complete system.
:::

```bash
bitbake tn-image-docker

or

bitbake tn-image-podman
```

## Troubleshooting

- If GPU acceleration isn't working, verify that device permissions are correctly configured
- For "Failed to open device" errors, check that the GPU driver is loaded in the host system
- When encountering network issues during apt operations inside the container, the retry configuration should help

## Additional Notes

- This Docker image retains hardware acceleration capabilities of the host system
- The container shares the host's device nodes through the `--device /dev:/dev` parameter
- For production deployments, consider restricting device access to only required hardware components
