---
title: Testing Hardwired Audio with Linux
description: Testing Hardwired Audio with Linux
---
## Introduction
This article takes you through the steps to test audio using the audio codec and headset jack on various TechNexion baseboards include that of PICO-PI, PICO-WIZARD, AXON-PI, AXON-WIZARD, and Wandboard EDM-G. These baseboards make use of an integrated headphone and microphone jack. This is best tested with combo stereo headset and mic used in standard mobile handsets.We have tested using the following headsets:

[https://www.jbl.com/earbuds/JBL+T110.html](https://www.jbl.com/earbuds/JBL+T110.html)

[https://www.cyberacoustics.com/ac-204-stereo-headset-w/-single-plug-and-y-adapter](https://www.cyberacoustics.com/ac-204-stereo-headset-w/-single-plug-and-y-adapter)

Apple combo headphones for mobile handsets **do not work** with the codecs on these baseboards.

### Gather information on the Audio Devices
``` bash
# cat /proc/asound/cards
 0 [audiohdmi      ]: audio-hdmi - audio-hdmi
                      audio-hdmi
 1 [wm8960audio    ]: wm8960-audio - wm8960-audio
                      wm8960-audio
 ```
 You can also get a list of device names:
 ```bash
 # aplay -L
null
    Discard all samples (playback) or generate zero samples (capture)
pulse
    PulseAudio Sound Server
sysdefault:CARD=audiohdmi
    audio-hdmi,
    Default Audio Device
sysdefault:CARD=wm8960audio
    wm8960-audio,
    Default Audio Device
```

### Step 1 -- Audio playback test

To play files, issue the commands:
```bash
# aplay /usr/share/sounds/alsa/Front_Left.wav
```
for the right channel:
```bash
# aplay /usr/share/sounds/alsa/Front_Right.wav
```
Select the device you want to play through using the -D argument:
```bash
# aplay -D hw:1 /usr/share/sounds/alsa/Front_Right.wav
```
or, use the speaker test application to perform a simple test:
```bash
# speaker-test -t wav -c 2 -D hw:0
```
Note that if you want to send data out of specific card (you may have multiple cards), you can change the `hw:` argument to select the card number.
```bash
# speaker-test -t wav -c 2 -D hw:1
```
The filenames can be anything as long as they can test the right and left channels. If you don't have a .wav audio file to use for testing, you should be able to easily find one on the net, such as this one:

[http://www.kozco.com/tech/LRMonoPhase4.wav](http://www.kozco.com/tech/LRMonoPhase4.wav)

If you have your unit connected to a local network, you can easily download this file:
```bash
# wget http://www.kozco.com/tech/LRMonoPhase4.wav
```
Then you can play it:
```bash
# aplay LRMonoPhase4.wav
```
### Step 2 -- Test audio recording using a microphone

Set the microphone as the capture device:In the terminal prompt:
```bash
# amixer sset 'Capture Mux' 'MIC_IN'
# amixer sset 'Capture' 100
```
Set the mic volume to 2 (67%). We found that on most headsets, the gain is too high and the recorded sound is quite distorted.
```bash
# amixer sset 'Mic' 2
```
Connect the microphone and use the command:
```bash
# arecord -d 5 -vv -fdat /tmp/test.wav
```
This will record for 5 seconds. You can also use Ctrl+C to stop recording.Verify that the recording works by playing back the sound by:
```bash
# aplay /tmp/test.wav
```