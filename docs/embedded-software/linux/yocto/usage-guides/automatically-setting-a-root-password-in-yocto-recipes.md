---
title: Automatically Setting a Root Password in Yocto Recipes
description: Automatically Setting a Root Password in Yocto Recipes
---
## Introduction
This article discusses how to set a root password within Yocto metadata so that images are built with a root password included. We show how to do this with both a cleartext password as well as encrypted version of the password.

In local.conf, or in your image recipe, you need to add the following line:

```python
inherit extrausers
```
This includes the extrausers bbclass, which brings in a set of utilities that are run during the image build process to modify the users configuration.

Next, you add some program calls into a special variable named  ```EXTRA_USERS_PARAMS```.  These program calls are made to the users settings in sequence. To set the root password, you need to add a call to ```usermod```.

### Setting a Clear Text Root Password
Here is how to set a root password that is in clear text (not encrypted) in the configuration. This might be fine if you can restrict access to the metadata to trusted people only.

To ```EXTRA_USERS_PARAMS``` you add a call to ```usermod``` with the following options:

```bash
usermod -P <cleartext password> root
```
For example, if we were to set the password to ```technexion``` for the root user, we would do the following:
```python
EXTRA_USERS_PARAMS = " \
    usermod -P technexion root \
"
```
Putting this together:
```python
inherit extrausers

EXTRA_USERS_PARAMS = " \
    usermod -P technexion root \
"
```
You can add many commands to the ```EXTRA_USERS_PARAMS``` variable. You can even add additional users to your image by adding a call to ```useradd``` to ```EXTRA_USERS_PARAMS```. Keep in mind that you need to add a semicolon to separate between the commands. Here, we add a user named ```scorpion``` without a password:
```python
inherit extrausers

EXTRA_USERS_PARAMS = " \
    usermod -P technexion root; \
    useradd -p '' scorpion \
"
```
:::(Warning) (Warning)
When setting the password in cleartext, make sure to use an uppercase P (```-P```) option in ```usermod``` when setting the root password. This tells the ```usermod``` command that the password is in cleartext.
:::
### Setting an Encrypted Version of the Password
You can also set the password using an an encrypted version of the password in the metadata. This can be a more secure way of setting the password.

#### Generate the password using ```mkpasswd```
First, you need to generate the password. This can be done using the ```mkpasswd``` command as follows:
```bash
mkpasswd -m sha-512 <your-clear-password> -s "seed-between-8-and-16-chars-long"
 ```
 :::(Info) (Note)
In Ubuntu, ```mkpasswd``` is part of the ```whois``` package:
```bash
sudo apt install whois
```
:::
So, to generate the a password using ```technexion``` in cleartext and a seed of ```11223344```:
 ```bash
mkpasswd -m sha-512 technexion -s "11223344"
```
This results in the following output:
```
$6$11223344$JKoE0Gu2kAO1TM0ItsAnbfropz/fKdIEUQs6Z.ik9WBMUc.Mdt5u46plMon05riuZIl2qisG.nswnlhzoQqNt/
 ```
This output becomes the password argument for the usermod command, but with a few changes. The password option for encrypted passwords makes use of the lowercase p (```-p```). Also, all special characters that could be interpreted by a shell script need to be escaped. Thus:
```python
EXTRA_USERS_PARAMS = " \
    usermod -p '\$6\$11223344\$JKoE0Gu2kAO1TM0ItsAnbfropz/fKdIEUQs6Z.ik9WBMUc.Mdt5u46plMon05riuZIl2qisG.nswnlhzoQqNt/' root; \
    useradd -p '' scorpion \
"
```

:::warning
When setting the password encrypted, make sure to use an lowercase p (```-p```) option in ```usermod``` when setting the root password. This tells the ```usermod``` command that the password is in cleartext.

Do not forget to enclose the password in single quotes (' ').
:::

## Video Tutorial
In the following video, we go through all of the above steps, as well as the effects of this on these commands on the target's ```/etc/shadow``` file:

[![Video](https://img.youtube.com/vi/xHrdpzbxIUs/0.jpg)](https://www.youtube.com/watch?v=xHrdpzbxIUs)

## Example Code
Example code for this can be found in the ```meta-scorpion``` project on Github.
[https://github.com/TechNexion-customization/meta-scorpion](https://github.com/TechNexion-customization/meta-scorpion)

