---
title: How to Build Yocto with a Custom Kernel
description: Learn how to build images with your custom kernel
---

1. Follow the steps to build yocto (as described in this page: [tn-imx-yocto-manifest](https://github.com/TechNexion/tn-imx-yocto-manifest)) all the way up to configuring yocto build, before "bitbake" command to start building yocto (The line containing DISTRO=XXX MACHINE=YYY source tn-setup-release.sh .... ). This will setup the build environment and configure the Yocto build system.

2. Execute the following command to preserve the current working directory:
```bash
pushd $(pwd)
```

3. Issue the following command:
```bash
bitbake -c unpack virtual/kernel
mkdir ../local_repo/
cp -r tmp/work-shared/${MACHINE}/kernel-source ../local_repo/
```
-> where is the parameter "MACHINE" used when configuring the build parameters.

4. Type in the following command:
```bash
cd ../local_repo/kernel-source
```
and now you may start modifying the kernel source code.

5. When the modification of the kernel is done, be sure to commit it (but you don't have to push the commit to the remote git branch). Get the hash/id of the commit.

6. Now go to /sources/meta-tn-imx-bsp/recipes-kernel/linux/ and then modify following lines in the linux-tn-src_[version].inc or linux-tn-imx_[version].bb as follows:

```
KERNEL_SRC = “git://${BSPDIR}/local_repo/kernel-source;protocol=file”
SRCREV = “Commit ID”
```
:  where the `Commit ID` is the id for your previous commit. If you created your own branch to work on, then you will need to modify the `SRCBRANCH` parameter as well to point to the new branch.

7. Execute the command "popd" to return to the building/working directory of Yocto.

8. Issue the relevant bitbake command to commence the build process.

For example, to build a complete image:
```
bitbake imx-image-full
```

To build just the kernel, you can also run:
```
bitbake virtual/kernel
```
