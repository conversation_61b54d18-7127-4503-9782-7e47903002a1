---
title: Enabling Auto-login in Yocto
description: Enabling Auto-login in Yocto
---

## Introduction
In some use cases, it is necessary to provide access to a terminal (console) without requiring the user to login or provide a password. Particularly useful in development, where the act of logging in can take some time, this can be helpful even in some real-world applications.  For example, an end-user can login to view certain parameters and collect diagnostic information using a serial console port or a video terminal.

The objective of this article is to illustrate how to enable this in Yocto-generate distributions.

### Mechanism: `getty` and `systemd`
Terminal access into a Linux system is provided by an application named "getty", which is short for "get tty".  When the system detects a connection, the purpose of getty is to prompt the user for a login and password.

In embedded Linux machines, an instance of getty is launched on selected terminals at startup. These can be video consoles (on display), or on serial ports. This is different than for other terminal sessions that are started on-demand. For example, incoming secure shell connections require an instance of getty to authenticate the user as the sessions are started.

By default, Yocto distributions use `systemd` to start userspace applications at boot. The configuration of `systemd` is controlled using *unit configuration files*.  Each process launched by `systemd` utilizes a configuration file. The configuration files dictate how a process is launched. By modifying the arguments contained within the `systemd` unit configuration file for the `getty` instances launched at startup, we can enable auto-login.
:::(Info) (Hardware used in this example)
For this example, the hardware used is the EDM-G-IMX8M-PLUS, as that is what the author had handy at the time. Any hardware can be used, but the serial terminal device numbers and other configurations, such as BAUD rate, might be different.
:::
Taking a look at the default unit configuration files for `getty` for serial console and video console for the **EDM-G-IMX8M-PLUS**. The default unit configuration files are located in /lib/systemd/system:

Examining the file for the serial console (`/lib/systemd/system/serial-getty@.service`), and viewing the `[Service]` section, you will see the `ExecStart=` line. `ExecStart` defines the command line used when starting up the instance.

```text
...
[Service]
Environment="TERM=xterm"
ExecStart=-/sbin/agetty -8 -L %I 115200 $TERM
Type=idle
Restart=always
...
```

By adding `--autologin [username]` to the command, getty will start the terminal session as the user selected by `[username]`. For example, this can be *any* user, even root. For example:
```text
ExecStart=-/sbin/agetty --autologin root -8 -L %I 115200 $TERM
```

In our example, we have a normal user named `technexion` in our Yocto Project Customization series, thus:
```text
ExecStart=-/sbin/agetty --autologin technexion -8 -L %I 115200 $TERM
```

This will enable the serial console autologin as the normal user `technexion`.

#### Video Console
The normal video console (the command line presented on a video display if not running a graphical UI) can also be configured for autologin. The `systemd` unit configuration file is different, as the arguments to the call to `/sbin/agetty` are different for virtual terminals.

In `/lib/systemd/system/getty@.service`, find the `ExecStart=` line and add `--autologin [username]` to it. Below we add same the `technexion` user as above.
```text
ExecStart=-/sbin/agetty --autologin technexion -o '-p -- \\u' --noclear %I $TERM
```

After making the above changes, rebooting the system should provide an command prompt in your chosen shell.
```shell
TechNexion i.MX Release Distro 5.4-zeus edm-g-imx8mp ttymxc1

edm-g-imx8mp login: technexion (automatic login)
edm-g-imx8mp:~$ whoami
technexion
```

As an aside, you can choose to automatically login as the root user in the video console (or virtual terminal). This is a very bad idea to do in production, but it might be useful in development. A '-f' argument is needed to be passed to the login program like this:
```text
ExecStart=-/sbin/agetty --autologin root -o '-f -p -- \\u' --noclear %I $TERM
```

This argument causes the login program to skip authentication.

### Building this into your distribution with Yocto
In order to enable automatic login into your custom image with Yocto, you will need to modify the `systemd` configuration files in the root filesystem (rootfs). One way to do this is to modify the files after the rootfs has been generated. This simplifies the amount of modification required for the recipes. In fact, one can add this to just the image recipe, or even to the `local.conf` file.  By adding this to the image recipe, the autologin function is only enabled for that specific image. If added to local.conf, all images will have autologin enabled.

The OpenEmbedded build system defines a variable named `ROOTFS_POSTPROCESS_COMMAND` which contains a list of functions to be executed after the rootfs has been built.  By adding a function to modify the the `systemd` unit configuration files to `ROOTFS_POSTPROCESS_COMMAND`, the OE build system makes required changes.

Here is the example code:
```python
# Remove debugging tweaks. These allow the root user to be passwordless.
IMAGE_FEATURES_remove += " \
    debug-tweaks \
"

# Add more users
inherit extrausers

# Set the password for the root user, and create a new user nambed 'technexion`
EXTRA_USERS_PARAMS = " \
    usermod -P rootpasswd root; \
    useradd -p '' technexion \
"

# Define a variable to hold the list of systemd unit config files to be modified.
# Modify the serial console config and the video console config files.
TN_LOCAL_GETTY ?= " \
     ${IMAGE_ROOTFS}${systemd_system_unitdir}/serial-getty@.service \
     ${IMAGE_ROOTFS}${systemd_system_unitdir}/getty@.service \
"
# Define a function that modifies the systemd unit config files with the autologin arguments
local_autologin () {
    sed -i -e 's/^\(ExecStart *=.*getty \)/\1--autologin technexion /' ${TN_LOCAL_GETTY}
}

# Add the function so that it is executed after the rootfs has been generated
ROOTFS_POSTPROCESS_COMMAND += "local_autologin; "
```

To explain, this first ensures that the root user has a password defined, and establishes a new user named `technexion`. It then defines the files that need to be modified in `TN_LOCAL_GETTY`. Note: `TN_LOCAL_GETTY` can be any name. It then defines the function `local_autologin` which runs `sed` (Stream Editor) to search for the line in the files defined by `TN_LOCAL_GETTY` for `ExecStart` and inserts `--autologin technexion` into the line, That function is then *appended* to the variable `ROOTFS_POSTPROCESS_COMMAND` using the `+=` operator. This function executes and modifies the `systemd` unit configuration files after the rootfs has been generated, but before the final image is created.

After adding this to an image recipe, rebuild the image, load the image into the system, and it should autologin automatically as the user `technexion`.
