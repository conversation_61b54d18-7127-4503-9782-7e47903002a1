---
title: Test Bluetooth in Yocto-based Linux Distributions
description: Learn how to test Bluetooth in Linux distributions build with Yocto
---
## Introduction
This article discusses some ways to test Bluetooth radios in TechNexion Yocto-based distributions.

## Yocto 2.5 and 3.0
Beginning with Yocto 2.5 and 3.0 distributions, systemd will automatically start up the Bluetooth interface. You can check the status of the service as follows:

```bash
# systemctl status serial-qcabtfw@*
    ● <EMAIL> - QCA Bluetooth firmware service on ttymxc0
       Loaded: loaded (/lib/systemd/system/serial-qcabtfw@.service; indirect; vendo>
       Active: active (exited) since Tue 2019-07-02 05:30:49 UTC; 3h 33min ago
      Process: 3167 ExecStart=/usr/bin/hciattach -t 30 /dev/ttymxc0 qca 3000000 flo>
     Main PID: 3167 (code=exited, status=0/SUCCESS)
        Tasks: 1 (limit: 1623)
       CGroup: /system.slice/system-serial\x2dqcabtfw.slice/serial-qcabtfw@ttymxc0.>
               mq3174 /usr/bin/hciattach -t 30 /dev/ttymxc0 qca 3000000 flow -

    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: get_vs_hci_event: Response type   >
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: read_vs_hci_event: Wait for HCI-Ve>
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: rome_set_baudrate_req: Received HC>
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: rome_set_baudrate_req
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: rome_hci_reset_req: HCI RESET
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: rome_hci_reset_req: HCI CMD: 0x1 0>
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: ## userial_vendor_set_baud: 14
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: HCI Reset is done
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: Setting TTY to N_HCI line discipli>
    Jul 02 05:30:50 pico-imx8mm hciattach[3167]: Device setup complete
```

Check if interface `hci0` device node exists:
```bash
~# hciconfig -a
hci0:   Type: Primary  Bus: UART
        BD Address: 00:1F:7B:31:1F:1B  ACL MTU: 1024:7  SCO MTU: 60:8
        UP RUNNING
        RX bytes:713 acl:0 sco:0 events:42 errors:0
        TX bytes:1015 acl:0 sco:0 commands:42 errors:0
        Features: 0xff 0xfe 0x8f 0xfe 0xd8 0x3f 0x5b 0x87
        Packet type: DM1 DM3 DM5 DH1 DH3 DH5 HV1 HV2 HV3
        Link policy: RSWITCH HOLD SNIFF
        Link mode: SLAVE ACCEPT
        Name: 'pico-imx8mm'
        Class: 0x200000
        Service Classes: Audio
        Device Class: Miscellaneous,
        HCI Version: 5.0 (0x9)  Revision: 0x0
        LMP Version: 5.0 (0x9)  Subversion: 0x25a
        Manufacturer: Qualcomm (29)
```

Scan the local environment for Bluetooth devices:
```bash
~# hcitool -i hci0 scan
Scanning ...
        20:74:CF:27:82:56       TREKZ Titanium by AfterShokz
```

### Check Bluetooth boot messages
```bash
# dmesg | grep Bluetooth
[    0.089295] Bluetooth: Core ver 2.22
[    0.089322] Bluetooth: HCI device and connection manager initialized
[    0.089329] Bluetooth: HCI socket layer initialized
[    0.089337] Bluetooth: L2CAP socket layer initialized
[    0.089350] Bluetooth: SCO socket layer initialized
[    1.383864] Bluetooth: HCI UART driver ver 2.3
[    1.388323] Bluetooth: HCI UART protocol H4 registered
[    1.393471] Bluetooth: HCI UART protocol BCSP registered
[    1.398809] Bluetooth: HCI UART protocol LL registered
[    1.403953] Bluetooth: HCI UART protocol ATH3K registered
[    1.409375] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    1.415733] Bluetooth: HCI UART protocol Broadcom registered
[    1.421417] Bluetooth: HCI UART protocol QCA registered
[    1.953694] Bluetooth: RFCOMM TTY layer initialized
[    1.958590] Bluetooth: RFCOMM socket layer initialized
[    1.963753] Bluetooth: RFCOMM ver 1.11
[    1.967511] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    1.972828] Bluetooth: BNEP filters: protocol multicast
[    1.978075] Bluetooth: BNEP socket layer initialized
[    1.983046] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[    1.988973] Bluetooth: HIDP socket layer initialized
```

### Identify the Device
```bash
# hciconfig -a
hci0:   Type: Primary  Bus: UART
        BD Address: 00:1F:7B:31:EE:4B  ACL MTU: 1024:7  SCO MTU: 60:8
        UP RUNNING
        RX bytes:777 acl:0 sco:0 events:51 errors:0
        TX bytes:2269 acl:0 sco:0 commands:51 errors:0
        Features: 0xff 0xfe 0x8f 0xfe 0xd8 0x3f 0x5b 0x87
        Packet type: DM1 DM3 DM5 DH1 DH3 DH5 HV1 HV2 HV3
        Link policy: RSWITCH HOLD SNIFF
        Link mode: SLAVE ACCEPT
        Name: 'edm-g-imx8mp'
        Class: 0x2c0000
        Service Classes: Rendering, Capturing, Audio
        Device Class: Miscellaneous,
        HCI Version: 5.0 (0x9)  Revision: 0x0
        LMP Version: 5.0 (0x9)  Subversion: 0x25a
        Manufacturer: Qualcomm (29)
```

Run `hcitool` to get the Bluetooth MAC address:
```bash
# hcitool dev
Devices:
        hci0    00:1F:7B:31:EE:4B
```

## Pairing a device
You can pair a device using `bluetoothctl`:
```bash
# bluetoothctl
[bluetooth]# agent on
Agent registered
[bluetooth]# agent off
Agent unregistered
[bluetooth]# pairable on
Changing pairable on succeeded
```

Ensure that your device (the device you wish to connect) is also pairable (e.g. by pressing a button, setting it on in a settings menu).
```bash
[bluetooth]# scan on
```

A number of pairable BT devices may then be scanned. Copy the MAC address of the device to which you wish to pair.
```bash
[bluetooth]# scan off
[bluetooth]# pair <MAC address of device>
```

At this point you may be prompted to approve pairing of the device. After doing so, you can then trust the device:
```bash
[bluetooth]# trust <MAC address of device>
```

Connect to the paired device:
```bash
[bluetooth]# connect <MAC address of device>
```

For example, here is the process to connect a Creative T30 Wireless speaker:
```bash
[bluetooth]# scan on
Discovery started
[CHG] Controller 00:1F:7B:31:EE:4B Discovering: yes
[NEW] Device 27:83:92:E7:A9:3E 27-83-92-E7-A9-3E
[NEW] Device 44:92:A2:00:2B:72 44-92-A2-00-2B-72
[CHG] Device 60:F8:1D:C1:D6:B3 RSSI: -51
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Key: 0x004c
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Value:
  0c 0e 08 37 6c 93 5e b0 a3 61 83 07 28 fa 36 6f  ...7l.^..a..(.6o
[NEW] Device 4E:62:E1:5F:95:7E 4E-62-E1-5F-95-7E
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Key: 0x004c
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Value:
  10 05 49 1c f6 e5 2e                             ..I....
[NEW] Device 68:64:4B:26:91:2C 68-64-4B-26-91-2C
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Key: 0x004c
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Value:
  0c 0e 08 37 6c 93 5e b0 a3 61 83 07 28 fa 36 6f  ...7l.^..a..(.6o
[NEW] Device D0:D2:B0:A0:8A:D6 D0-D2-B0-A0-8A-D6
[NEW] Device 2C:AB:D4:A2:B0:9C 2C-AB-D4-A2-B0-9C
[CHG] Device 4E:62:E1:5F:95:7E RSSI: -72
[NEW] Device 00:02:3C:49:FE:6D Creative T30 Wireless
```

```bash
[Creative T30 Wireless]# scan off
[CHG] Device 00:02:3C:49:FE:6D TxPower is nil
[CHG] Device 00:02:3C:49:FE:6D RSSI is nil
[CHG] Device 2C:AB:D4:A2:B0:9C RSSI is nil
[CHG] Device D0:D2:B0:A0:8A:D6 TxPower is nil
[CHG] Device D0:D2:B0:A0:8A:D6 RSSI is nil
[CHG] Device 68:64:4B:26:91:2C RSSI is nil
[CHG] Device 4E:62:E1:5F:95:7E TxPower is nil
[CHG] Device 4E:62:E1:5F:95:7E RSSI is nil
[CHG] Device 60:F8:1D:C1:D6:B3 RSSI is nil
[CHG] Device 44:92:A2:00:2B:72 TxPower is nil
[CHG] Device 44:92:A2:00:2B:72 RSSI is nil
[CHG] Device 27:83:92:E7:A9:3E RSSI is nil
[CHG] Controller 00:1F:7B:31:EE:4B Discovering: no
Discovery stopped
```

```bash
[bluetooth]# pair 00:02:3C:49:FE:6D
Attempting to pair with 00:02:3C:49:FE:6D
[CHG] Device 00:02:3C:49:FE:6D Connected: yes
[CHG] Device 00:02:3C:49:FE:6D UUIDs: 0000110b-0000-1000-8000-00805f9b34fb
[CHG] Device 00:02:3C:49:FE:6D UUIDs: 0000110c-0000-1000-8000-00805f9b34fb
[CHG] Device 00:02:3C:49:FE:6D UUIDs: 0000110e-0000-1000-8000-00805f9b34fb
[CHG] Device 00:02:3C:49:FE:6D ServicesResolved: yes
[CHG] Device 00:02:3C:49:FE:6D Paired: yes
Pairing successful
[CHG] Device 00:02:3C:49:FE:6D ServicesResolved: no
[CHG] Device 00:02:3C:49:FE:6D Connected: no
```

```bash
[bluetooth]# trust 00:02:3C:49:FE:6D
[CHG] Device 00:02:3C:49:FE:6D Trusted: yes
Changing 00:02:3C:49:FE:6D trust succeeded
```

```bash
[bluetooth]# connect 00:02:3C:49:FE:6D
Attempting to connect to 00:02:3C:49:FE:6D
[CHG] Device 00:02:3C:49:FE:6D Connected: yes
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Key: 0x004c
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Value:
  0c 0e 08 3a 6c 47 9c 59 cb 52 2a b7 6f 7d 12 dd  ...:lG.Y.R*.o}..
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Key: 0x004c
[CHG] Device 60:F8:1D:C1:D6:B3 ManufacturerData Value:
  10 05 49 1c f6 e5 2e                             ..I....
[CHG] Device 4E:62:E1:5F:95:7E RSSI: -56
[CHG] Device 44:92:A2:00:2B:72 ManufacturerData Key: 0x004c
[CHG] Device 44:92:A2:00:2B:72 ManufacturerData Value:
  10 07 13 1f ad 66 62 81 28                       .....fb.(
Connection successful
[CHG] Device 00:02:3C:49:FE:6D ServicesResolved: yes
[Creative T30 Wireless]# quit
```

For additional information regarding playing audio to attached Bluetooth speakers, please see [Playing Audio to Bluetooth Speaker using Pulseaudio and Bluez (Yocto)](playing-audio-to-bluetooth-speaker-using-pulseaudio-and-bluez-yocto).

