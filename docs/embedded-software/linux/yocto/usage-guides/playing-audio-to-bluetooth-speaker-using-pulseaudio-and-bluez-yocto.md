---
title: Playing Audio to Bluetooth Speaker using Pulseaud<PERSON> and <PERSON><PERSON> (Yocto)
description: Learn how to play audio to a Bluetooth Speaker using Pulseaudio and Bluez
---
## Introduction

This article takes you through the steps of connecting your product to a Bluetooth speaker and using that to play audio. This is based on working with Linux kernel 4.14.98 and the latest version of the QCACLD driver and firmware with Yocto version Sumo and later. This uses Pulseaudio and Bluez5.

### Steps to using a BT speaker:

Download a sample .wav file to test playback:
```text
wget https://www2.cs.uic.edu/~i101/SoundFiles/CantinaBand3.wav
```

Start pulseaudio service. Note -- there is room for improvement here. Pulseaudio needs to start on system startup.
```text
pulseaudio --start --log-target=syslog
```

Ensure that the speaker is in pairing mode. This is going to be dependent on the type of speaker you have. In the case of this article, we used a Creative T30 wireless speaker.

Run bluetoothctl to pair and connect with the speaker (Note: that pulseaudio service must be already running to connect)
```bash
# bluetoothctl
[bluetooth]# scan on
[NEW] Device 00:02:3C:49:FE:6D Creative T30 Wireless
[bluetooth]# pair 00:02:3C:49:FE:6D
Attempting to pair with 00:02:3C:49:FE:6D
[CHG] Device 00:02:3C:49:FE:6D Connected: yes
[CHG] Device 00:02:3C:49:FE:6D UUIDs: 0000110b-0000-1000-8000-00805f9b34fb
[CHG] Device 00:02:3C:49:FE:6D UUIDs: 0000110c-0000-1000-8000-00805f9b34fb
[CHG] Device 00:02:3C:49:FE:6D UUIDs: 0000110e-0000-1000-8000-00805f9b34fb
[CHG] Device 00:02:3C:49:FE:6D ServicesResolved: yes
[CHG] Device 00:02:3C:49:FE:6D Paired: yes
Pairing successful
[CHG] Device 00:02:3C:49:FE:6D ServicesResolved: no
[CHG] Device 00:02:3C:49:FE:6D Connected: no
[bluetooth]# connect 00:02:3C:49:FE:6D
Attempting to connect to 00:02:3C:49:FE:6D
[CHG] Device 00:02:3C:49:FE:6D Connected: yes
Connection successful
[CHG] Device 00:02:3C:49:FE:6D ServicesResolved: yes
[Creative T30 Wireless]# quit
```

Play the audio file:
```bash
paplay CantinaBand3.wav
```

You can also use Gstreamer and the "pulsesink" element to play audio:
```bash
gst-launch-1.0 filesrc location=CantinaBand3.wav ! wavparse ! pulsesink
```
You should hear the audio file playing on your speaker.

### Troubleshooting

If you don't hear audio from the speaker, then you can run 'paplay' in verbose mode. This will show you the audio device (audio sink) being used for playback.
```bash
# paplay -vvv CantinaBand3.wav
Opening a playback stream with sample specification 's16le 1ch 22050Hz' and channel map 'mono'.
Connection established.
Stream successfully created.
Buffer metrics: maxlength=4194304, tlength=88200, prebuf=87320, minreq=882
Using sample spec 's16le 1ch 22050Hz', channel map 'mono'.
Connected to device bluez_sink.00_02_3C_49_FE_6D.a2dp_sink (index: 1, suspended: no).
Stream started.
Playback stream drained.: 93502 usec.
Draining connection to server.
```