---
title: Tips for Resolving Dependency Issues in Yocto
description: Tips for Resolving Dependency Issues in Yocto
---
## Introduction
A customer of ours recently asked about some problems they were seeing when running an application on a Pico module. They were using Yocto. Many Linux applications, especially Qt applications, require other packages (files, and libraries) to be installed on the target system in order to run. These might not be installed by default in the commonly built Yocto image recipes like core-image-base and fsl-image-qt5.Yocto can be a little tricky to resolve dependencies, because you have to know a few pieces of information.

1.  What the dependencies or what dependencies are missing
2.  What packages are available
3.  How to add the packages that resolve the dependencies

Here we will try to help you with a few tips on resolving dependencies in Yocto.Understand what the dependencies are and what is missingFirst, if you need to know what packages your application requires. That is usually fairly easy to know, because when you attempt to run the application, it will provide you with some error messages that give you a hint. In the case of our customer, he was running a Qt application and it was complaining that it needed QtQuick.Controls, QtQuick.Controls.Styles, and QtQuick.Extras. He was starting using a build of fsl-image-qt5.

### Get a list of available packages

In Yocto, the bitbake command can be used to list the packages that are available in your metadata:
```bash
bitbake -s
```
You can pipe that through the grep command to filter out packages with a specific name. In this case, I was looking for packages pertaining to Qt, so I filter with "qt" using grep:
```bash
$ bitbake -s | grep qt
fsl-image-qt5 :1.0-r0
fsl-image-qt5-validation-imx :1.0-r0
gstreamer1.0-plugins-good-qt :1.14.0.imx-r0
imx-qtapplications :1.2.2-aarch64-r0
libconnman-qt5 :1.2.7+gitAUTOINC+ad7fef1c35-r0
maliit-framework-qt5 :0.99.0+gitAUTOINC+60b1b10de1-r0
maliit-plugins-qt5 :0.99.0+gitAUTOINC+c6a3485926-r0
meta-toolchain-qt5 :1.0-r0
nativesdk-packagegroup-qt5-toolchain-host :1.0-r0
nativesdk-qt3d :5.10.1+gitAUTOINC+31f424bb81-r0
nativesdk-qtbase :5.10.1+gitAUTOINC+6c6ace9d23-r0
nativesdk-qtdeclarative :5.10.1+gitAUTOINC+3e91062877-r0
nativesdk-qtremoteobjects :5.10.1+gitAUTOINC+61d2de26db-r0
nativesdk-qtscxml :5.10.1+gitAUTOINC+d9b2f8c56d-r0
nativesdk-qttools :5.10.1+gitAUTOINC+03ebf1c3d6-r0
nativesdk-qtwayland :5.10.1+gitAUTOINC+db36bc0d9c-r0
nativesdk-qtxmlpatterns :5.10.1+gitAUTOINC+7c4113d6e6-r0
packagegroup-qt5-demos :1.0-r0
packagegroup-qt5-qtcreator-debug :1.0-r0
packagegroup-qt5-toolchain-target :1.0-r0
packagegroup-qt5-webengine :1.0-r0
paho-mqtt-c :1.2.0-r0
python-paho-mqtt :1.3.1-r0
python3-paho-mqtt :1.3.1-r0
qt3d :5.10.1+gitAUTOINC+31f424bb81-r0
qt3d-native :5.10.1+gitAUTOINC+31f424bb81-r0
qt5-creator :3.5.1-r0
qt5-demo-extrafiles :1.0-r0
qt5-opengles2-test :1.0.4+gitrAUTOINC+9383905070-r0
qt5-plugin-generic-vboxtouch :1.1.4+gitrAUTOINC+3f2bdb5a1d-r0
qt5everywheredemo :1.0-r0
qt5ledscreen :1.0-r0
qt5nmapcarousedemo :1.0-r0
qt5nmapper :1.0-r0
qtbase :5.10.1+gitAUTOINC+6c6ace9d23-r0
qtbase-native :5.10.1+gitAUTOINC+6c6ace9d23-r0
qtcanvas3d :5.10.1+gitAUTOINC+0eecd96a7b-r0
qtcharts :5.10.1+gitAUTOINC+9a4f99f518-r0
qtchooser :git-r0
qtconnectivity :5.10.1+gitAUTOINC+2331b915d2-r0
qtdatavis3d :5.10.1+gitAUTOINC+397e501474-r0
qtdeclarative :5.10.1+gitAUTOINC+3e91062877-r0
qtdeclarative-native :5.10.1+gitAUTOINC+3e91062877-r0
qtenginio :5.10.1+gitAUTOINC+23603a3b08-r0
qtgamepad :5.10.1+gitAUTOINC+e6f366618d-r0
qtgraphicaleffects :5.10.1+gitAUTOINC+a38c4da7b5-r0
qtimageformats :5.10.1+gitAUTOINC+892d4a0df7-r0
qtknx :5.10.1+gitAUTOINC+29c34e8f07-r0
qtlocation :5.10.1+gitAUTOINC+ec00ff89be_8c1be4ec01-r0
qtmqtt :5.10.1+gitAUTOINC+2c3c2a41c5-r0
qtmultimedia :5.10.1+gitAUTOINC+038716bb1f-r0
qtnetworkauth :5.10.1+gitAUTOINC+8f4f2e0cae-r0
qtpurchasing :5.10.1+gitAUTOINC+d925245573-r0
qtquick1 :5.10.1+gitAUTOINC+b2476dcd53-r0
qtquickcontrols :5.10.1+gitAUTOINC+09ebb25aa6-r0
qtquickcontrols2 :5.10.1+gitAUTOINC+e693ad2c4a-r0
qtremoteobjects :5.10.1+gitAUTOINC+61d2de26db-r0
qtremoteobjects-native :5.10.1+gitAUTOINC+61d2de26db-r0
qtscript :5.10.1+gitAUTOINC+76bcf9b526-r0
qtscxml :5.10.1+gitAUTOINC+d9b2f8c56d-r0
qtscxml-native :5.10.1+gitAUTOINC+d9b2f8c56d-r0
qtsensors :5.10.1+gitAUTOINC+d5a94c5595-r0
qtserialbus :5.10.1+gitAUTOINC+d5d35d64d0-r0
qtserialport :5.10.1+gitAUTOINC+fa688ac5c3-r0
qtsmarthome :1.0-r0
qtsvg :5.10.1+gitAUTOINC+af35137016-r0
qtsystems :5.10.1+gitAUTOINC+66e45676f5-r0
qttools :5.10.1+gitAUTOINC+03ebf1c3d6-r0
qttools-native :5.10.1+gitAUTOINC+03ebf1c3d6-r0
qttranslations :5.10.1+gitAUTOINC+c3486e072e-r0
qtvirtualkeyboard :5.10.1+gitAUTOINC+85d35bb122-r0
qtwayland :5.10.1+gitAUTOINC+db36bc0d9c-r0
qtwayland-native :5.10.1+gitAUTOINC+db36bc0d9c-r0
qtwebchannel :5.10.1+gitAUTOINC+7540a85c82-r0
qtwebengine :5.10.1+gitAUTOINC+9dc8dff7a8_c858cc7609-r0
qtwebkit :5.10.1+gitAUTOINC+bd0657f98a-r0
qtwebkit-examples :5.10.1+gitAUTOINC+a24c780b60-r0
qtwebsockets :5.10.1+gitAUTOINC+2160c268ec-r0
qtwebview :5.10.1+gitAUTOINC+7445cd2861-r0
qtx11extras :5.10.1+gitAUTOINC+a751298bc3-r0
qtxmlpatterns :5.10.1+gitAUTOINC+7c4113d6e6-r0
qtxmlpatterns-native :5.10.1+gitAUTOINC+7c4113d6e6-r0
qwt-qt5 :6.1.3-r0
```

### Get a list of packages that are being built and installed in your image

Understanding which packages that will be built and installed in your image is also important. You can use a rather long bitbake command to do this as well.
```bash
bitbake -g <img name> && cat pn-buildlist | grep -ve "native" | sort | uniq
```
In the case of this customer, he was building fsl-image-qt5, so the command is:
```bash
$ bitbake -g fsl-image-qt5 && cat pn-buildlist | grep -ve "native" | sort | uniq
```
This will generate a rather long list of packages, and I want to only look at the qt packages, so I grep again for "qt":
```bash
bitbake -g fsl-image-qt5 && cat pn-buildlist | grep -ve "native" | sort | uniq | grep qt
```

From this, I get the following output:
```
gstreamer1.0-plugins-good-qt
imx-qtapplications
packagegroup-qt5-demos
qt3d
qt5-demo-extrafiles
qt5everywheredemo
qt5ledscreen
qt5nmapcarousedemo
qt5nmapper
qtbase
qtdeclarative
qtgraphicaleffects
qtmultimedia
qtquickcontrols
qtsvg
qtwayland
qtx11extras
qtxmlpatterns
```

### Adding the packages to your image to resolve the dependencies

Adding the packages that resolve your dependencies is a bit of a guessing game, but based on this customer's issue, he was looking for QtQuick.Controls, QtQuick.Controls.Styles, and QtQuick.Extras to be installed. So, from the list of possible packages, I guessed that he needed to add `qtquick1` and maybe `qtquickcontrols2`. To do this, you can add them to an image recipe file, or you can easily add them using your build/conf/local.conf file.

Adding the dependent packages to be built and installed in local.conf
```text
CORE_IMAGE_EXTRA_INSTALL_append = " \
    qtquick1 \
    qtquickcontrols2 \
"
```
Once those are added, you can re-run the bitbake command to discover what additional packages would be added to your image:
```bash
$ bitbake -g fsl-image-qt5 && cat pn-buildlist | grep -ve "native" | sort | uniq | grep qt
gstreamer1.0-plugins-good-qt
imx-qtapplications
packagegroup-qt5-demos
qt3d
qt5-demo-extrafiles
qt5everywheredemo
qt5ledscreen
qt5nmapcarousedemo
qt5nmapper
qtbase
qtdeclarative
qtgraphicaleffects
qtlocation
qtmultimedia
qtquick1
qtquickcontrols
qtquickcontrols2
qtscript
qtsensors
qtsvg
qtwayland
qtwebchannel
qtwebkit
qtwebsockets
qtx11extras
qtxmlpatterns
```
In the case of this customer, adding these packages resolved the issues he was having when running his application. Your case may be different, but you can use the same techniques to resolve the problem.
