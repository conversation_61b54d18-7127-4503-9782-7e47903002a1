---
title: Yocto 5.0 (Scarthgap) 2024Q4 Release Notes
sidebar_position: 38
---

## Yocto 5.0 (Scarthgap) 2024Q4 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2024.04_6.6.36_2.1.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2024.04_6.6.36_2.1.0-stable) |4e59a2b59c9bff8cb2fb8835b4e73154bcafe19d|
|Kernel| [tn-imx_6.6.36_2.1.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.6.36_2.1.0-stable)|c0cf1971a2f65f3f65a5c214784d06735aa8b817|
|TN Yocto BSP| [scarthgap_6.6.36-2.1.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/scarthgap_6.6.36-2.1.0-stable)|4d2580b6747f7f581c9ec5d44f4f7b21162d490c|
- [**Build Yocto from source code**](https://github.com/TechNexion/tn-imx-yocto-manifest)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)

---
### Supported Platforms in this Release

#### ARM64 SOM

<table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
  {/* Form factor */}
  <tbody>
    <tr>
      <td style={{ backgroundColor: "#F9F801" }} align="left">
        <b>Form factor</b>
      </td>
      <td style={{ backgroundColor: "#96F8C8" }} align="center" colSpan={4}>
        <b>EDM-G</b>
      </td>
      <td style={{ backgroundColor: "#01F8C8" }} align="center" colSpan={1}>
        <b>AXON</b>
      </td>
    </tr>
    {/* Product */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        <b>Product</b>
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        EDM-G-IMX8MP
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        EDM-G-IMX8MM
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={1}>
        AXON-IMX93
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        DRAM Variants
      </td>
      <td align="center" colSpan={2}>
        1/2/4/6/8 GB
      </td>
      <td align="center" colSpan={2}>
        1/2/4/8 GB
      </td>
      <td align="center" colSpan={1}>
        2 GB
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Wireless
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center" colSpan={1}>
        IW416(SDIO)
        <br />
        BT 5.2
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Baseboard
      </td>
      <td align="center">EDM-G-WB</td>
      <td align="center">EDM-G-WIZARD</td>
      <td align="center">EDM-G-WB</td>
      <td align="center">EDM-G-WIZARD</td>
      <td align="center">AXON-WB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Atheros/Realtek phy
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Realtek</td>
    </tr>
    {/* MIPI Panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>MIPI Panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel - ILI9881C
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* LVDS panel support (Native LVDS or MIPI to LVDS) */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>LVDS panel support (Native LVDS or MIPI to LVDS)</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - vl101-12880YL-C01
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - vl101-12880YL-C13
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        15.6 inch panel - vl15613676
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        15.6 inch panel - vl156192108
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">No</td>
      <td align="center">No</td>
      <td align="center">No</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        21.5 inch panel - vl215192108
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">No</td>
    </tr>
    {/* VizionPanel support (Native MIPI to FPD-LINK) */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>LVDS panel support (Native LVDS or MIPI to LVDS)</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 5-inch Parallel TTL panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 7-inch Parallel TTL panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 8-inch Parallel TTL panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* HDMI */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>HDMI</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Native HDMI
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        MIPI to HDMI
        <br />
        (via ADV7535)
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Dual HDMI
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* Parallel TTL panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Parallel TTL panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        7 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        8 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
    </tr>
    {/* Peripherals */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Peripherals</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        NFC Reader - PN7150 - CLIX
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        NFC Reader - PN7150 - I2C
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Vizionlink-HDMI
        <br />
        (HDMI input/capture)
      </td>
      <td align="center">
        Yes
        <br />
        (Dual/Single)
      </td>
      <td align="center">
        Yes
        <br />
        (Dual/Single)
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVS-AR Camera
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLI-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLS3-AR Camera
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    {/* Software support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Software support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VizionSDK/VizionViewer
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        FreeRTOS(MCUXpresso v2.10)
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        U-boot splash screen
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Parallel TTL panel</td>
    </tr>
  </tbody>
</table>

---
### Camera Module
- i.mx8 supports the following camera modules
    - TEVS-AR series
    - VLS3 -AR series
- i.mx9 supports the following camera modules
    - TEVS-AR series
    - VLS3 -AR series
<br/>
---
### Prebuilt Images Available for Download

Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20241014.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20241014.zip) |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_yocto-5.0-qt6_qca9377_lvds-1280x800_20241014.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-5.0-qt6_qca9377_lvds-1280x800_20241014.zip) |
|AXON-IMX93| [axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20241014.zip](https://download.technexion.com/demo_software/AXON/IMX9/axon-imx93/archived/axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20241014.zip) |