---
title: Yocto 3.0 (Zeus) 2021Q1 Release Notes
sidebar_position: 50
---
## Yocto 3.0 (Zeus) 2021Q1 Release Notes
### Supported Platforms in this Release

#### ARM64 Platforms
![YP3_2021Q1_64bit_platform_support.png](//img/YP3_2021Q1_64bit_platform_support.png)

#### ARM32 Platforms
![YP30_2021Q1_32bit_platform_support.png](//img/YP30_2021Q1_32bit_platform_support.png)

#### Explanation:
1. **CAM-OV5645 and TEVI-OV5640:**
For i.mx8, in u-boot, the camera is detected by probing for an EEPROM of a connected TEVI camera to tell the difference of CAM-OV5640 and TEVI-OV5640 then load device tree overlay accordingly. In i.MX6 and i.MX7, this release does not yet support device tree overlays. We plan to also support device overlay to support different kinds of panels and cameras. Eventually, both CAM-OV5645 and TEVI-OV5640 will be supported in ARM32 and ARM64 products.
2. **Broadcom WIFI on older ARM32 platforms:**
We are working to provide an option to support BRCMFMAC so that customers with older ARM32-based platforms with Broadcom wireless can more easily upgrade to the latest support releases. However, we have some additional work to do here. Stay tuned.
3. **Github:**
The default branch is switched to “zeus_5.4.70-2.3.0” and create release tag for u-boot/linux/yocto bsp meta layer.
[https://github.com/technexion/](https://github.com/technexion/)

#### To-Do:
1. **U-boot splash screen for i.mx8:**
Because u-boot 2020.04 changes to use device model for display driver, we have to look for a new way to switch to different panels and add MIPI-DSI panel driver in u-boot.
2. **FreeRTOS:**
MCUExpress v2.9 will be the target version to be supported for cortex-M4/M7 core.

#### Prebuilt Images Available for Download
Prebuilt images are avialable for download via TechNexion's server.

| ARM32: |
|---|---
| PICO-IMX6 | [pico-imx6_pico-pi_yocto-3.0-qt5_qca9377_lcd-800x480_20210429.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_pico-pi_yocto-3.0-qt5_qca9377_lcd-800x480_20210429.zip)
| PICO-IMX7 | [pico-imx7_pico-pi_yocto-3.0-qt5_qca9377_lcd-800x480_20210429.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pico-pi_yocto-3.0-qt5_qca9377_lcd-800x480_20210429.zip)
| PICO-IMX6UL | [pico-imx6ul_pico-pi_yocto-3.0-qt5_qca9377_lcd-800x480_20210429.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pico-pi_yocto-3.0-qt5_qca9377_lcd-800x480_20210429.zip)
| EDM-IMX6 | [hedm-imx6_edm-fairy_yocto-3.0-qt5_qca9377_lvds7_20210429.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_edm-fairy_yocto-3.0-qt5_qca9377_lvds7_20210429.zip)

| ARM64: |
|---|---
| PICO-IMX8MM | [hpico-imx8mm_pico-pi-imx8m_yocto-3.0-qt5_qca9377_mipi5-1280x720_20210429.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-imx8m_yocto-3.0-qt5_qca9377_mipi5-1280x720_20210429.zip)
| PICO-IMX8MQ | [pico-imx8m_pico-pi-imx8m_yocto-3.0-qt5_qca9377_mipi5-1280x720_20210429.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8m_pico-pi-imx8m_yocto-3.0-qt5_qca9377_mipi5-1280x720_20210429.zip)
| EDM-G-IMX8MP | [edm-g-imx8mp_edm-g-wb_yocto-3.0-qt5_qca9377_HDMI-1920x1080_20210429.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-3.0-qt5_qca9377_HDMI-1920x1080_20210429.zip)
| EDM-G-IMX8MM | [edm-g-imx8mm_edm-g-wb_yocto-3.0-qt5_qca9377_lvds-1280x800_20210429.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-3.0-qt5_qca9377_lvds-1280x800_20210429.zip)
| EDM-G-IMX8MN | [edm-g-imx8mn_edm-g-wb_yocto-3.0-qt5_qca9377_lvds-1280x800_20210429.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mn/archived/edm-g-imx8mn_edm-g-wb_yocto-3.0-qt5_qca9377_lvds-1280x800_20210429.zip)
| EDM-IMX8MQ |[edm-imx8mq_edm-wizard-imx8m_yocto-3.0-qt5_qca9377_mipi5-1280x720_20210429.zip](https://download.technexion.com/demo_software/EDM/IMX8/edm-imx8mq/archived/edm-imx8mq_edm-wizard-imx8m_yocto-3.0-qt5_qca9377_mipi5-1280x720_20210429.zip)
