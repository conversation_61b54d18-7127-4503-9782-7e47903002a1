---
title: Yocto 5.0 (Scarthgap) 2025Q2 Release Notes
sidebar_position: 36
---

# Yocto 5.0 (Scarthgap) 2025Q2 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2024.04_6.6.52_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2024.04_6.6.52_2.2.0-stable)  |9aaf88cfe4234d6c40d3aade29da8e046d566988|
|<PERSON><PERSON>| [tn-imx_6.6.52_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.6.52_2.2.0-stable) |3c6660b7ae5b03ba4303a7d58b3c723a8d03881c|
|Yocto BSP| [scarthgap_6.6.52-2.2.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/scarthgap_6.6.52-2.2.0-stable) |8583ba1eef45b01ecd7259baf779e6de284ca5f0|
- [**Build Yocto from source code**](https://github.com/TechNexion/tn-imx-yocto-manifest)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)

---
### Supported Platforms in this Release

<!--
All HTML tables converted from Doc360 must use JSX syntax.
The following website is helpful: https://transform.tools/html-to-jsx
-->

#### ARM64 SOM
<table className="tn-table tn-table-color">
    <thead>
        <tr>
            <td>Form factor</td>
            <td colSpan={4} align="center">EDM-G</td>
            <td colSpan={2} align="center">PICO</td>
            <td colSpan={3} align="center">AXON</td>
            <td colSpan={2} align="center">EDM</td>
        </tr>
        <tr>
            <td>Product</td>
            <td colSpan={2} align="center">EDM-G-IMX8MP</td>
            <td colSpan={2} align="center">EDM-G-IMX8MM</td>
            <td colSpan={2} align="center">PICO-IMX8MM</td>
            <td colSpan={1} align="center">AXON-IMX8MP</td>
            <td colSpan={1} align="center">AXON-IMX93</td>
            <td colSpan={1} align="center">AXON-IMX91</td>
            <td colSpan={1} align="center">EDM-IMX93</td>
            <td colSpan={1} align="center">EDM-IMX91</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>DRAM Variants</td>
            <td colSpan={2}>1/2/4/6/8 GB</td>
            <td colSpan={2}>1/2/4/8 GB</td>
            <td colSpan={2}>1/2/4 GB</td>
            <td>2/4/8 GB</td>
            <td>2 GB</td>
            <td>2 GB</td>
            <td>2 GB</td>
            <td>1 GB</td>
        </tr>
        <tr>
            <td>Wireless</td>
            <td colSpan={2}>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td colSpan={2}>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td colSpan={2}>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>IW416(SDIO)<br/>BT 5.2</td>
            <td>IW416(SDIO)<br/>BT 5.2</td>
            <td>IW416(SDIO)<br/>BT 5.2</td>
            <td>IW416(SDIO)<br/>BT 5.2</td>
        </tr>
        <tr>
            <td>Baseboard</td>
            <td>EDM-G-WB</td><td>EDM-G-WIZARD</td>
            <td>EDM-G-WB</td><td>EDM-G-WIZARD</td>
            <td>WIZARD</td><td>PI</td>
            <td>WIZARD</td>
            <td>AXON-WB</td>
            <td>AXON-WB</td>
            <td>EDM-WB</td>
            <td>EDM-WB</td>
        </tr>
        <tr>
            <td>Atheros/Realtek phy</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td>
            <td>Realtek</td>
            <td>Realtek</td>
            <td>Realtek</td>
            <td>Realtek</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>MIPI Panel support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>5 inch panel - ILI9881C</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>LVDS panel support (Native LVDS or MIPI to LVDS)</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>10.1 inch panel - vl101-12880YL-C01</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>10.1 inch panel - vl101-12880YL-C13</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>15.6 inch panel - vl15613676</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>15.6 inch panel - vl156192108</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>21.5 inch panel - vl215192108</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>HDMI</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>Native HDMI</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>MIPI to HDMI<br/>(via ADV7535)</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>Dual HDMI</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Parallel TTL panel support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>5/7 inch panel</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>8 inch panel</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Peripherals</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>Vizionlink-HDMI<br/>(HDMI input/capture)</td>
            <td>Yes<br/>(Dual/Single)</td><td>Yes<br/>(Dual/Single)</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes<br/>(Single)</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>TEVS-AR Camera</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VLS3-AR Camera</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Software support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>VizionSDK/VizionViewer</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>FreeRTOS(MCUXpresso v2.x)</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>U-boot splash screen</td>
            <td>NA</td><td>NA</td>
            <td>Yes<br/>(10" LVDS)</td><td>Yes<br/>(10" LVDS)</td>
            <td>Yes<br/>(5" MIPI)</td><td>Yes<br/>(5" MIPI)</td>
            <td>NA</td>
            <td>Parallel TTL panel</td>
            <td>Parallel TTL panel</td>
            <td>Parallel TTL panel</td>
            <td>Parallel TTL panel</td>
        </tr>
    </tbody>
</table>

#### ARM32 SOM
<table className="tn-table tn-table-color">
    <thead>
        <tr>
            <td>Form factor</td>
            <td colSpan={3} align="center">PICO</td>
            <td colSpan={1} align="center">EDM</td>
        </tr>
        <tr>
            <td>Product</td>
            <td>PICO-IMX6</td>
            <td>PICO-IMX7D</td>
            <td>PICO-IMX6UL<br/>PICO-IMX6ULL</td>
            <td>EDM-IMX6</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>SOC variants</td>
            <td>i.mx6Solo/DL/Quad (No QP)</td>
            <td>i.mx7D</td>
            <td>i.mx6UL 528MHz<br/>i.mx6ULL 900MHz</td>
            <td>i.mx6Solo/DL/Quad (No QP)</td>
        </tr>
        <tr>
            <td>DRAM Variants</td>
            <td>Solo: 512MB<br/>DL/Quad: 1GB</td>
            <td>512MB<br/>1GB<br/>2GB</td>
            <td>256MB<br/>512MB</td>
            <td>Solo: 512MB<br/>DL: 1GB<br/>Quad: 2GB</td>
        </tr>
        <tr>
            <td>Wireless</td>
            <td>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>QCA9377-3(SDIO)<br/>BT 5.0</td>
        </tr>
        <tr>
            <td>Baseboard</td>
            <td>Nymph</td>
            <td>PI-GL</td>
            <td>PI-FL</td>
            <td>Fairy</td>
        </tr>
        <tr>
            <td>Atheros/Realtek phy</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Atheros</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>LVDS Panel support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>7 inch panel</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Parallel TTL panel support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>5/7 inch panel</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Peripherals</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>TEVI-OV5640</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Software support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>FreeRTOS(MCUXpresso v2.x)</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>U-boot splash screen</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
    </tbody>
</table>

#### HMI
<table className="tn-table tn-table-color">
    <thead>
        <tr>
            <td>Product</td>
            <td colSpan={1} align="center">TEP0500-IMX7<br/>TEP0700-IMX7</td>
            <td colSpan={1} align="center">TC-0700<br/>TC-0710</td>
            <td colSpan={1} align="center">TC-1010</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>SOC variants</td>
            <td>i.mx7D</td>
            <td>i.mx6Solo/DL/Quad</td>
            <td>i.mx6Solo/DL/Quad</td>
        </tr>
        <tr>
            <td>DRAM Variants</td>
            <td>512MB<br/>1GB</td>
            <td>Solo: 512MB<br/>DL: 1GB<br/>Quad: 2GB</td>
            <td>Solo: 512MB<br/>DL: 1GB<br/>Quad: 2GB</td>
        </tr>
        <tr>
            <td>Wireless(Optional)</td>
            <td>QCA9377-5(PCIE)<br/>BT 5.0</td>
            <td>QCA9377-5(PCIE)<br/>BT 5.0</td>
            <td>QCA9377-5(PCIE)<br/>BT 5.0</td>
        </tr>
        <tr>
            <td>Atheros/Realtek phy</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Peripherals</b></td>
            <td colSpan={3} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>Vizionlink-HDMI<br/>(HDMI input / capture)</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>TEVI-OV5640</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>TEVS-AR Camera</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VLI-OV5640</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VLI-AR Camera</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VLS3-AR Camera</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Software support</b></td>
            <td colSpan={3} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>VizionSDK/VizionViewer</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>FreeRTOS(MCUXpresso v2.x)</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>U-boot splash screen</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
    </tbody>
</table>

---
### [Camera Module](https://www.technexion.com/products/embedded-vision/image-sensors)
- i.mx8 supports the following camera modules
    - [TechNexion TEVS-AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
    - [TechNexion VLS3 -AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
- i.mx93 supports the following camera modules
    - [TechNexion TEVS-AR series](https://developer.technexion.com/docs/imx93-tevs-camera-usage-guide)
    - [TechNexion VLS3 -AR series](https://developer.technexion.com/docs/imx93-tevs-camera-usage-guide)
- i.mx95 supports the following camera modules
    - TechNexion TEVS-AR series
---
### [VizionSDK](https://developer.technexion.com/docs/vizionsdk-overview)
- VizionSDK v25.04.1
- VizionViewer v25.04.1
- vizion-ctl v25.04.1
- pyVizionSDK
- pyvizion-ctl
---
### Prebuilt Images Available for Download

Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20250402.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20250402.zip)  |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_yocto-5.0-qt6_qca9377_lvds-1280x800_20250402.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-5.0-qt6_qca9377_lvds-1280x800_20250402.zip)  |
|PICO-IMX8MM| [pico-imx8mm_pico-pi-8m_yocto-5.0-qt6_qca9377_mipi5-1280x720_20250402.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-5.0-qt6_qca9377_mipi5-1280x720_20250402.zip)  |
|AXON-IMX8MP| [axon-imx8mp_axon-wizard_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20250402.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20250402.zip)  |
|AXON-IMX93| [axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20250402.zip](https://download.technexion.com/demo_software/AXON/IMX9/axon-imx93/archived/axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20250402.zip)  |
|EDM-IMX93| [edm-imx93_edm-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20250402.zip](https://download.technexion.com/demo_software/EDM/IMX9/edm-imx93/archived/edm-imx93_edm-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20250402.zip)  |
|AXON-IMX91| [axon-imx91_axon-wb_yocto-5.0-qt6_iw416_lcd-800x480_20250402.zip](https://download.technexion.com/demo_software/AXON/IMX9/axon-imx93/archived/axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20250402.zip)  |
|EDM-IMX91| [edm-imx91_edm-wb_yocto-5.0-qt6_iw416_lcd-800x480_20250402.zip](https://download.technexion.com/demo_software/EDM/IMX9/edm-imx93/archived/edm-imx93_edm-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20250402.zip)  |

| ARM32 |  |
| --- | --- |
|PICO-IMX7| [pico-imx7_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20250402.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20250402.zip)  |
|PICO-IMX6| [pico-imx6_pico-nymph_yocto-5.0-qt6_qca9377_hdmi_20250402.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_pico-nymph_yocto-5.0-qt6_qca9377_hdmi_20250402.zip)  |
|PICO-IMX6UL| [pico-imx6ul_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20250402.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20250402.zip)  |
|EDM-IMX6| [edm-imx6_edm-fairy_yocto-5.0-qt6_qca9377_hdmi_20250402.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_edm-fairy_yocto-5.0-qt6_qca9377_hdmi_20250402.zip)  |

| HMI |  |
| --- | --- |
| TEP-0500-IMX7 TEP-0700-IMX7 | [tep1-imx7_yocto-5.0-qt6_lcd-800x600_20250402.zip](https://download.technexion.com/demo_software/TEP/IMX7/tep0500-imx7d/archived/tep1-imx7_yocto-5.0-qt6_lcd-800x600_20250402.zip)  |
| TC-0700 | [tc0700_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20250402.zip](https://download.technexion.com/demo_software/TC/tc-0700/archived/tc0700_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20250402.zip)  |
| TC-1010 | [tc1010_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20250402.zip](https://download.technexion.com/demo_software/TC/tc-1010/archived/tc1010_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20250402.zip)  |

| EVK |  |
| --- | --- |
|IMX8MP-LPDDR4-EVK| [imx8mp-lpddr4-evk_yocto-5.0-qt6_20250402.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-LPDDR4/imx8mp-lpddr4-evk_yocto-5.0-qt6_20250402.zip)  |
|IMX8MP-DDR4-EVK| [imx8mp-ddr4-evk_yocto-5.0-qt6_20250402.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-DDR4/imx8mp-ddr4-evk_yocto-5.0-qt6_20250402.zip)  |
|IMX93-11X11-LPDDR4X-EVK| [imx93-11x11-lpddr4x-evk_yocto-5.0-qt6_20250402.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX93-LPDDR4X/imx93-11x11-lpddr4x-evk_yocto-5.0-qt6_20250402.zip)  |
|IMX95-19X19-LPDDR5-EVK| [imx95-19x19-lpddr5-evk_yocto-5.0-qt6_20250402.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX95-LPDDR5/imx95-19x19-lpddr5-evk_yocto-5.0-qt6_20250402.zip)  |