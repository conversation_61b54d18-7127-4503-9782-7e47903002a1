---
title: Yocto 3.0 (Zeus) 2021Q4 Release Notes
sidebar_position: 49
---
## Yocto 3.0 (Zeus) 2021Q4 Release Notes

#### Links for source repositories:

u-boot: [tn-imx_v2020.04_5.4.70_2.3.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2020.04_5.4.70_2.3.0-stable)

kernel: [tn-imx_5.4.70_2.3.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_5.4.70_2.3.0-stable)

Yocto: [zeus_5.4.y-stable](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/zeus_5.4.y-stable)

[Build steps](https://developer.technexion.com/docs/building-an-image-with-yocto)

### Supported Platforms in this Release
#### ARM64 SOM
![YP3_2021Q4_64bit_platform_support.png](//img/YP3_2021Q4_64bit_platform_support%282%29.png)

#### ARM32 SOM
![YP3_2021Q4_32bit_platform_support.png](//img/YP3_2021Q4_32bit_platform_support.png)

#### HMI
![YP3_2021Q4_HMI_support.png](//img/YP3_2021Q4_HMI_support.png)

#### BOX PC
![YP3_2021Q4_BOX_support.png](//img/YP3_2021Q4_BOX_support.png)


#### Explanation:
1. **CAM-OV5645 and TEVI-OV5640:**
For i.mx8, in u-boot, the camera is detected by probing for an EEPROM of a connected TEVI camera to tell the difference of CAM-OV5640 and TEVI-OV5640 then load device tree overlay accordingly. In i.MX6 and i.MX7, this release does not yet support device tree overlays. We plan to also support device overlay to support different kinds of panels and cameras. Eventually, both CAM-OV5645 and TEVI-OV5640 will be supported in ARM32 and ARM64 products..

3. **Github:**
The default branch is switched to “zeus_5.4.70-2.3.0” and create release tag for u-boot/linux/yocto bsp meta layer.
[https://github.com/technexion/](https://github.com/technexion/)

#### To-Do:
1. **U-boot splash screen for i.mx8:**
Because u-boot 2020.04 changes to use device model for display driver, we have to look for a new way to switch to different panels and add MIPI-DSI panel driver in u-boot.
2. **FreeRTOS:**
MCUExpress v2.9 will be the target version to be supported for cortex-M4/M7 core.

#### Prebuilt Images Available for Download
Prebuilt images will soon be avialable for download via TechNexion's server.

| ARM64|  |
| --- | --- |
| EDM-G-IMX8MP | [edm-g-imx8mp_wb_yocto-3.0_xwayland_qca_20211018131459.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wb_yocto-3.0_xwayland_qca_20211018131459.zip) |
| EDM-G-IMX8MM | [edm-g-imx8mm_wb_yocto-3.0_xwayland_qca_20211018141540.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wb_yocto-3.0_xwayland_qca_20211018141540.zip) |
| EDM-G-IMX8MN | [edm-g-imx8mn_wb_yocto-3.0_xwayland_qca_20211018144525.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mn/archived/edm-g-imx8mn_wb_yocto-3.0_xwayland_qca_20211018144525.zip) |
| PICO-IMX8MM | [pico-imx8mm_pi_yocto-3.0_xwayland_qca_20211018151521.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pi_yocto-3.0_xwayland_qca_20211018151521.zip) |
| PICO-IMX8MQ | [pico-imx8mq_pi_yocto-3.0_xwayland_qca_20211018161403.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8mq_pi_yocto-3.0_xwayland_qca_20211018161403.zip) |
| EDM-IMX8MQ | [edm-imx8mq_wizard_yocto-3.0_xwayland_qca_20211018164444.zip](https://download.technexion.com/demo_software/EDM/IMX8/edm-imx8mq/archived/edm-imx8mq_wizard_yocto-3.0_xwayland_qca_20211018164444.zip) |
| FLEX-IMX8MM | [flex-imx8mm_pi_yocto-3.0_xwayland_qca_20211018154428.zip](https://download.technexion.com/demo_software/FLEX/IMX8/flex-imx8mm/archived/flex-imx8mm_pi_yocto-3.0_xwayland_qca_20211018154428.zip) |

| ARM32|  |
| --- | --- |
| PICO-IMX6 | [pico-imx6_nymph_yocto-3.0_x11_qca_20211018171211.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_nymph_yocto-3.0_x11_qca_20211018171211.zip)
| PICO-IMX7 | [pico-imx7_pi_yocto-3.0_x11_qca_20211018184459.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pi_yocto-3.0_x11_qca_20211018184459.zip) |
| PICO-IMX6UL/ULL | [pico-imx6ul_pi_yocto-3.0_x11_qca_20211018192610.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pi_yocto-3.0_x11_qca_20211018192610.zip)  |
| EDM-IMX6 | [edm-imx6_fairy_yocto-3.0_x11_qca_20211018173600.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_fairy_yocto-3.0_x11_qca_20211018173600.zip)  |

| HMI|  |
| --- | --- |
| TC-0700/TC-0710 | [tc0700_yocto-3.0_xwayland_qca_20211021084439.zip](https://download.technexion.com/demo_software/TC/tc-0700/archived/tc0700_yocto-3.0_xwayland_qca_20211021084439.zip)  |
|TEP-1010-IMX6/TEP-1560-IMX6 | [tep1010-imx6/archived/tek-imx6_tek_yocto-3.0_x11_ath-pci_20211018175920.zip](https://download.technexion.com/demo_software/TEP/IMX6/tep1010-imx6/archived/tek-imx6_tek_yocto-3.0_x11_ath-pci_20211018175920.zip)  |
| TEP-0500-IMX7/TEP-0700-IMX7 | [tep1-imx7_yocto-3.0_x11_ath-pci_20211018190512.zip](https://download.technexion.com/demo_software/TEP/IMX7/tep0500-imx7d/archived/tep1-imx7_yocto-3.0_x11_ath-pci_20211018190512.zip)  |

| BOX PC|  |
| --- | --- |
| TEK3-IMX6 | [tek-imx6_tek_yocto-3.0_x11_ath-pci_20211018175920.zip](https://download.technexion.com/demo_software/TEK/IMX6/tek3-imx6/archived/tek-imx6_tek_yocto-3.0_x11_ath-pci_20211018175920.zip)  |