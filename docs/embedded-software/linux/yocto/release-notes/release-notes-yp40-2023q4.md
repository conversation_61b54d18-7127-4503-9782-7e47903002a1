---
title: Yocto 4.0 (Kirkstone) 2023Q4 Release Notes
sidebar_position: 42
---
## Yocto 4.0 (Kirkstone) 2023Q4 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2022.04_5.15.71_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2022.04_5.15.71_2.2.0-stable) |c1372194a74ab47a6f65530db541eac02be5e8e7|
|Kernel| [tn-imx_5.15.71_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_5.15.71_2.2.0-stable)|6bcae12379e7e51144b99a4f36c5f27b0ae521a6|
|TN Yocto BSP| [kirkstone_5.15.71-2.2.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/kirkstone_5.15.71-2.2.0-stable)|4ca8d1ae1de3a4da8c4b20fd845725349d4792e6|

- [**Build Yocto from source code**](https://github.com/TechNexion/tn-imx-yocto-manifest)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)

---
### Supported Platforms in this Release
#### ARM64 SOM
<>
  <table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
    {/* Form factor */}
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#F9F801" }} align="left">
          <b>Form factor</b>
        </td>
        <td style={{ backgroundColor: "#96F8C8" }} align="center" colSpan={4}>
          <b>EDM-G</b>
        </td>
        <td style={{ backgroundColor: "#F9C7F9" }} align="center" colSpan={4}>
          <b>PICO</b>
        </td>
        <td style={{ backgroundColor: "#01F8C8" }} align="center">
          <b>AXON</b>
        </td>
      </tr>
      {/* Product */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          EDM-G-IMX8MP
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          EDM-G-IMX8MM
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          PICO-IMX8MQ
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          PICO-IMX8MM
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center">
          AXON-IMX8MP
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center" colSpan={2}>
          1/2/4/8 GB
        </td>
        <td align="center" colSpan={2}>
          1/2/4/8 GB
        </td>
        <td align="center" colSpan={2}>
          1/2/4 GB
        </td>
        <td align="center" colSpan={2}>
          1/2/4 GB
        </td>
        <td align="center">2/4/8 GB</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Baseboard
        </td>
        <td align="center">EDM-G-WB</td>
        <td align="center">EDM-G-WIZARD</td>
        <td align="center">EDM-G-WB</td>
        <td align="center">EDM-G-WIZARD</td>
        <td align="center">WIZARD</td>
        <td align="center">PI</td>
        <td align="center">WIZARD</td>
        <td align="center">PI</td>
        <td align="center">WIZARD</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Atheros/Realtek phy
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Atheros only</td>
        <td align="center">Yes</td>
        <td align="center">Atheros only</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
      {/* MIPI Panel support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>MIPI Panel support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          5 inch panel - ILI9881C
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          10.1 inch panel - g101uan02
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      {/* LVDS panel support (Native LVDS or MIPI to LVDS) */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>LVDS panel support (Native LVDS or MIPI to LVDS)</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          10.1 inch panel - vl101-12880YL-C01
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          10.1 inch panel - vl101-12880YL-C13
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          15.6 inch panel - vl15613676
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          21.5 inch panel - vl215192108
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      {/* HDMI */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>HDMI</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Native HDMI
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          MIPI to HDMI
          <br />
          (via ADV7535)
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Dual HDMI
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      {/* Peripherals */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Peripherals</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          NFC Reader - PN7150 - CLIX
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          NFC Reader - PN7150 - I2C
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink-HDMI
          <br />
          (HDMI input/capture)
        </td>
        <td align="center">
          Yes
          <br />
          (Dual/Single)
        </td>
        <td align="center">
          Yes
          <br />
          (Dual/Single)
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (Single)
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-OV5640
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0144
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0234
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0521
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0522
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0821
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0822
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR1335
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">
          Yes
          <br />
          (PICO-PI rev.D)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-OV5640
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-AR0144
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-AR0234
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-AR0521
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-AR0821
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-AR0822
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink +<br />
          TEVI-AR1335
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">
          Yes
          <br />
          (CS1 only)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
      </tr>
      {/* Software support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.10)
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Only HDMI</td>
        <td align="center">Only HDMI</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
    </tbody>
  </table>
  #### ARM32 SOM
  <table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#F9F801" }} align="left">
          <b>Form factor</b>
        </td>
        <td style={{ backgroundColor: "#F9C7F9" }} align="center" colSpan={3}>
          <b>PICO</b>
        </td>
        <td style={{ backgroundColor: "#F9F801" }} align="center" colSpan={2}>
          <b>EDM</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center">
          PICO-IMX6
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center">
          PICO-IMX7D
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center">
          PICO-IMX6UL
          <br />
          PICO-IMX6ULL
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center">
          EDM-IMX6
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center">
          WANDBOARD
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          SOC variants
        </td>
        <td align="center">i.mx6Solo/DL/Quad (No QP)</td>
        <td align="center">i.mx7D</td>
        <td align="center">
          i.mx6UL 528MHz
          <br />
          I.mx6ULL 900MHz
        </td>
        <td align="center">i.mx6Solo/DL/Quad (No QP)</td>
        <td align="center">i.mx6Solo/DL/Quad</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center">
          Solo: 512MB
          <br />
          DL/Quad: 1GB
        </td>
        <td align="center">
          512MB
          <br />
          1GB
        </td>
        <td align="center">
          256MB
          <br />
          512MB
        </td>
        <td align="center">
          Solo: 512MB
          <br />
          DL: 1GB
          <br />
          Quad: 2GB
        </td>
        <td align="center">
          Solo: 512MB
          <br />
          DL: 1GB
          <br />
          Quad: 2GB
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless
        </td>
        <td align="center">
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Baseboard
        </td>
        <td align="center">Nymph</td>
        <td align="center">PI-GL</td>
        <td align="center">PI-FL</td>
        <td align="center">Fairy</td>
        <td align="center">WANDBOARD</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Atheros/Realtek phy
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Atheros</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
      {/* LVDS panel support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>LVDS Panel support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          7 inch panel
        </td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
      </tr>
      {/* Parallel TTL panel support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Parallel TTL panel support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          5 inch panel
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          7 inch panel
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      {/* Peripherals */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Peripherals</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-OV5640
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          CAM-OV5645
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      {/* Software support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.9)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
    </tbody>
  </table>
  #### HMI
  <table border={0} style={{ fontSize: 12 }}>
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#96F8C8" }} align="center">
          TEP0500-IMX7
          <br />
          TEP0700-IMX7
        </td>
        <td style={{ backgroundColor: "#F9C7F9" }} align="center">
          TC-0700
          <br />
          TC-0710
        </td>
        <td style={{ backgroundColor: "#01F8C8" }} align="center">
          TC-1010
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Type</b>
        </td>
        <td align="center">HMI</td>
        <td align="center">HMI</td>
        <td align="center">HMI</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          SOC variants
        </td>
        <td align="center">i.mx7D</td>
        <td align="center">i.mx6Solo/DL/Quad</td>
        <td align="center">i.mx6Solo/DL/Quad</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center">
          512MB
          <br />
          1GB
        </td>
        <td align="center">
          Solo: 512MB
          <br />
          DL: 1GB
          <br />
          Quad: 2GB
        </td>
        <td align="center">
          Solo: 512MB
          <br />
          DL: 1GB
          <br />
          Quad: 2GB
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless(Optional)
        </td>
        <td align="center">
          QCA9377-5(PCIE)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-5(PCIE)
          <br />
          BT 5.0
        </td>
        <td align="center">
          QCA9377-5(PCIE)
          <br />
          BT 5.0
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Atheros/Realtek phy
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
      {/* Software support  */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.9)
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
      </tr>
    </tbody>
  </table>
  #### BOX PC
  <table border={0} style={{ fontSize: 12 }}>
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#96F8C8" }} align="center">
          TEK-IMX8MP
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Type</b>
        </td>
        <td align="center">BOX</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          SOC variants
        </td>
        <td align="center">i.mx8m Plus</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center">2/4/8 GB</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless(Optional)
        </td>
        <td align="center">
          QCA9377-5(PCIE)
          <br />
          BT 5.0
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Realtek phy
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Native HDMI
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Peripherals */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Peripherals</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink-HDMI
          <br />
          (HDMI input/capture)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-OV5640
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0144
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0234
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0521
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0821
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR0822
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR1335
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Software support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.9)
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">NA</td>
      </tr>
    </tbody>
  </table>
</>

---
### Camera Module
- i.mx8 supports the following camera modules
    - TEVI-OV5640
    - TEVI-AR0144]
    - TEVI-AR0234
    - TEVI-AR0521/0522
    - TEVI-AR0821/0822
    - TEVI-AR1335
    - TEVI-AR CAM on NXP 8MPLUSLPD4-EVK board
- i.mx6/7 supports the following camera modules
    - TEVI-OV5640
<br/>


---
### To-Do
1. **U-boot splash screen for i.mx8**:
    - Because u-boot 2022.04 changes to use device model for display driver, we have to look for a new way to switch to different panels and add MIPI-DSI panel driver in u-boot.
<br/>


---
### Prebuilt Images Available for Download
Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_yocto-4.0-qt6_qca9377_hdmi-1920x1080_20231128.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-4.0-qt6_qca9377_hdmi-1920x1080_20231128.zip) |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_yocto-4.0-qt6_qca9377_lvds-1280x800_20231128.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-4.0-qt6_qca9377_lvds-1280x800_20231128.zip) |
|PICO-IMX8MQ| [pico-imx8mq_pico-pi-8m_yocto-4.0-qt6_qca9377_mipi5-1280x720_20231128.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8mq_pico-pi-8m_yocto-4.0-qt6_qca9377_mipi5-1280x720_20231128.zip) |
|PICO-IMX8MM| [pico-imx8mm_pico-pi-8m_yocto-4.0-qt6_qca9377_mipi5-1280x720_20231128.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-4.0-qt6_qca9377_mipi5-1280x720_20231128.zip) |
|AXON-IMX8MP| [axon-imx8mp_axon-wizard_yocto-4.0-qt6_qca9377_hdmi-1920x1080_20231128.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_yocto-4.0-qt6_qca9377_hdmi-1920x1080_20231128.zip) |


| ARM32 |  |
| --- | --- |
|PICO-IMX7| [pico-imx7_pico-pi_yocto-4.0-qt6_qca9377_lcd-800x480_20231128.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pico-pi_yocto-4.0-qt6_qca9377_lcd-800x480_20231128.zip) |
|PICO-IMX6| [pico-imx6_pico-nymph_yocto-4.0-qt6_qca9377_hdmi_20231128.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_pico-nymph_yocto-4.0-qt6_qca9377_hdmi_20231128.zip) |
|PICO-IMX6UL| [pico-imx6ul_pico-pi_yocto-4.0-qt6_qca9377_lcd-800x480_20231128.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pico-pi_yocto-4.0-qt6_qca9377_lcd-800x480_20231128.zip) |
|EDM-IMX6| [edm-imx6_edm-fairy_yocto-4.0-qt6_qca9377_hdmi_20231128.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_edm-fairy_yocto-4.0-qt6_qca9377_hdmi_20231128.zip) |
|WANDBOARD-IMX6| [wandboard-imx6_yocto-4.0-qt6_qca9377_20231128.zip](https://download.technexion.com/demo_software/WANDBOARD/wandboard-imx6/archived/wandboard-imx6_yocto-4.0-qt6_qca9377_20231128.zip) |

| HMI |  |
| --- | --- |
| TEP-0500-IMX7<br/>TEP-0700-IMX7 |  [tep1-imx7_yocto-4.0-qt6_lcd-800x600_20231128.zip](https://download.technexion.com/demo_software/TEP/IMX7/tep0500-imx7d/archived/tep1-imx7_yocto-4.0-qt6_lcd-800x600_20231128.zip) |
| TC-0700<br/>TC-0710 | [tc0700_edm1-cf-imx6_yocto-4.0-qt6_qca9377_20231128.zip](https://download.technexion.com/demo_software/TC/tc-0700/archived/tc0700_edm1-cf-imx6_yocto-4.0-qt6_qca9377_20231128.zip) |
| TC-1010 | [tc1010_edm1-cf-imx6_yocto-4.0-qt6_qca9377_20231128.zip](https://download.technexion.com/demo_software/TC/tc-1010/archived/tc1010_edm1-cf-imx6_yocto-4.0-qt6_qca9377_20231128.zip) |

| BOX PC |  |
| --- | --- |
|TEK-IMX8MP| [tek3-imx8mp_yocto-4.0-qt6_hdmi_20231128.zip](https://download.technexion.com/demo_software/TEK/IMX8/tek-imx8mp/archived/tek3-imx8mp_yocto-4.0-qt6_hdmi_20231128.zip) |

| EVK |  |
| --- | --- |
|IMX8MP-LPDDR4-EVK| [imx8mp-lpddr4-evk_yocto-4.0-qt6_20231128.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-LPDDR4/archived/imx8mp-lpddr4-evk_yocto-4.0-qt6_20231128.zip) |
|IMX8MP-DDR4-EVK| [imx8mp-ddr4-evk_yocto-4.0-qt6_20231128.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-DDR4/archived/imx8mp-ddr4-evk_yocto-4.0-qt6_20231128.zip) |