---
title: Yocto 3.3 (Hardknott) 2022Q2 Release Notes
sidebar_position: 47
---
## Yocto 3.3 (Hardknott) 2022Q2 Release Notes

#### Links for source repositories:

u-boot: [tn-imx_v2021.04_5.10.72_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2021.04_5.10.72_2.2.0-stable)

kernel: [tn-imx_5.10.72_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_5.10.72_2.2.0-stable)

Yocto: [hardknott_5.10.72-2.2.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/hardknott_5.10.72-2.2.0-stable)

[Build steps](https://github.com/TechNexion/tn-imx-yocto-manifest)

### Supported Platforms in this Release
#### ARM64 SOM

| **Form factor** | **EDM-G** | | | | | | **PICO** | | | | **AXON** |
|------|:----:|:----:|:----:|:----:|:----:|:----:|:----:|:----:|:----:|:----:|:----:|
| **Product** | EDM-G-IMX8MP | | EDM-G-IMX8MM | | EDM-G-IMX8MN | | PICO-IMX8MQ | | PICO-IMX8MM | | AXON-IMX8MP |
| DRAM Variants | 1/2/4/8 GB | | 1/2/4/8 GB | | 2GB | | 1/2/4 GB | | 1/2/4 GB | | 2/4/8 GB |
| Wireless | QCA9377-3(SDIO)/BT 5.0 | | QCA9377-3(SDIO)/BT 5.0 | | QCA9377-3(SDIO)/BT 5.0 | | QCA9377-3(SDIO)/BT 5.0 | | QCA9377-3(SDIO)/BT 5.0 | | QCA9377-3(SDIO)/BT 5.0 |
| Baseboard | EDM-G-WB | EDM-G-WIZARD | EDM-G-WB | EDM-G-WIZARD | EDM-G-WG | EDM-G-WIZARD | WIZARD | PI | WIZARD | PI | WIZARD |
| Atheros/Realtek phy | Yes | Yes | Yes | Yes | Atheros only | Atheros only | Atheros only | Yes | Atheros only | Yes | Yes |
| **MIPI Panel support** | | | | | | | | | | | |
| 5 inch panel - ILI9881C | NA | Yes | NA | NA | NA | NA | Yes | Yes | Yes | Yes | Yes |
| 10.1 inch panel - g101uan02 | NA | Yes | NA | Yes | NA | Yes | Yes | NA | Yes | NA | Yes |
| **LVDS panel support<br/>(Native LVDS or MIPI to LVDS)** | | | | | | | | | | | |
| 10.1 inch panel - vl101-12880YL-C13 | Yes | Yes | Yes | Yes | Yes | Yes | NA | NA | Yes | NA | Yes |
| 15.6 inch panel - vl15613676 | Yes | Yes | Yes | Yes | Yes | Yes | NA | NA | Yes | NA | Yes |
| 21.5 inch panel - vl215192108 | Yes | Yes | Yes | Yes | Yes | Yes | NA | NA | NA | NA | Yes |
| **HDMI** | | | | | | | | | | | |
| Native HDMI | Yes | Yes | NA | NA | NA | NA | Yes | Yes | NA | NA | Yes |
| MIPI to HDMI<br/>(via ADV7535) | NA | Yes | NA | NA | NA | NA | Yes | NA | Yes | NA | Yes |
| Dual HDMI | NA | NA | NA | NA | NA | NA | NA | NA | NA | NA | NA |
| **Peripherals** | | | | | | | | | | | |
| NFC Reader - PN7150 - CLIX | NA | NA | NA | NA | NA | NA | Yes | Yes | Yes | Yes | NA |
| NFC Reader - PN7150 - I2C | Yes | Yes | Yes | Yes | Yes | Yes | NA | NA | NA | NA | NA |
| VoiceHAT | NA | NA | NA | NA | NA | NA | Yes | Yes | Yes | Yes | NA |
| Vizionlink-HDMI<br/>(HDMI input/capture) | Yes (Dual/Single) | Yes (Dual/Single) | Yes | Yes | NA | NA | NA | NA | NA | NA | Yes (Single) |
| CAM-OV7251 | NA | NA | NA | NA | NA | NA | NA | NA | NA | NA | NA |
| CAM-OV5645 | NA | NA | NA | NA | NA | NA | Yes | Yes<br/>(PICO-PI rev.B1) | Yes | Yes<br/>(PICO-PI rev.B1) | NA |
| TEVI-OV5640 | Yes | Yes | Yes | Yes | Yes | Yes | Yes | Yes<br/>(PICO-PI rev.D1) | Yes | Yes<br/>(PICO-PI rev.D1) | Yes |
| TEVI-AR0144 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| TEVI-AR0234 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| TEVI-AR0521 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| TEVI-AR0522 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| TEVI-AR0821 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| TEVI-AR1335 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| TEVI-AR1820 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| Vizionlink +<br/>TEVI-OV5640 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| Vizionlink +<br/>TEVI-AR0144 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| Vizionlink +<br/>TEVI-AR0234 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| Vizionlink +<br/>TEVI-AR0521 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| Vizionlink +<br/>TEVI-AR0821 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| Vizionlink +<br/>TEVI-AR1335 | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | Yes |
| **Software support** | | | | | | | | | | | |
| FreeRTOS(MCUXpresso v2.10) | Yes | Yes | NA | NA | NA | NA | NA | NA | NA | NA | NA |
| U-boot splash screen | NA | NA | NA | NA | NA | NA | Only HDMI | Only HDMI | Only 5 inch MIPI | Only 5 inch MIPI | NA |


#### ARM32 SOM

| **Form factor** | **PICO** | | | **EDM** | |
|------|:----:|:----:|:----:|:----:|:----:|
| **Product** | PICO-IMX6 | PICO-IMX7D | PICO-IMX6UL/IMX6ULL | EDM-IMX6 | WANDBOARD |
| SOC variants | i.mx6Solo/DL/Quad (No QP) | i.mx7D | i.mx6UL 528MHz/I.mx6ULL 900MHz | i.mx6Solo/DL/Quad (No QP) | i.mx6Solo/DL/Quad |
| DRAM Variants | (Solo)512MB/(DL/Quad)1GB | 512MB/1GB | 256MB/512MB | (Solo)512MB/(DL)1GB/(Quad)2GB | (Solo)512MB/(DL)1GB/(Quad)2GB |
| Wireless | QCA9377-3(SDIO)/BT 5.0 | QCA9377-3(SDIO)/BT 5.0 | QCA9377-3(SDIO)/BT 5.0 | QCA9377-3(SDIO)/BT 5.0 | QCA9377-3(SDIO)/BT 5.0 |
| Baseboard | Nymph | PI-GL | PI-FL | Fairy | WANDBOARD |
| Atheros/Realtek phy | Atheros | Atheros | Atheros | Yes | Yes |
| **LVDS Panel support** | | | | | |
| 7 inch panel | Yes | NA | NA | Yes | NA |
| **Parallel TTL panel support** | | | | | |
| 5 inch panel | NA | Yes | Yes | NA | NA |
| 7 inch panel | Yes | Yes | Yes | NA | NA |
| **Peripherals** | | | | | |
| VoiceHAT | NA | NA | NA | NA | NA |
| TEVI-OV5640 | Yes | Yes | NA | Yes | NA |
| CAM-OV5645 | NA | NA | NA | NA | NA |
| **Software support** | | | | | |
| FreeRTOS(MCUXpresso v2.9) | NA | NA | NA | NA | NA |
| U-boot splash screen | NA | Yes | Yes | NA | Yes |


#### HMI

| **Product** | **TEP0500-IMX7/TEP0700-IMX7** | **TEP1010-IMX6/TEP1560-IMX6** | **TC-0700/TC-0710** |
|------|:----:|:----:|:-----:|
| Type | HMI | HMI | HMI |
| SOC variants | i.mx7D | i.mx6 | i.mx6Solo/DL/Quad |
| DRAM Variants | 512MB/1GB | 512MB/1GB | (Solo)512MB/(DL)1GB/(Quad)2GB |
| Wireless(Optional) | QCA9377-5(PCIE)/BT 5.0 | QCA9377-5(PCIE)/BT 5.0 | QCA9377-5(PCIE)/BT 5.0 |
| Atheros/Realtek phy | Yes | Atheros | Yes |
| **Software support** | | | |
| FreeRTOS(MCUXpresso v2.9) | NA | NA | NA |
| U-boot splash screen | Yes | NA | NA |


#### BOX PC

| **Product** | **TEK3-IMX6** |
|------|:----:|
| Type | BOX |
| SOC variants | i.mx6Solo/DL/Quad |
| DRAM Variants | (Solo)512MB/(DL)1GB/(Quad)2GB |
| Wireless(Optional) | QCA9377-5(PCIE)/BT 5.0 |
| Atheros/Realtek phy | Atheros |
| **Software support** | |
| FreeRTOS(MCUXpresso v2.9) | NA |
| U-boot splash screen | NA |


#### Explanation:
1. **CAM-OV5645 and TEVI-OV5640:**
For i.mx8, in u-boot, the camera is detected by probing for an EEPROM of a connected TEVI camera to tell the difference of CAM-OV5640 and TEVI-OV5640 then load device tree overlay accordingly. In i.MX6 and i.MX7, this release does not yet support device tree overlays. We plan to also support device overlay to support different kinds of panels and cameras. Eventually, both CAM-OV5645 and TEVI-OV5640 will be supported in ARM32 and ARM64 products.

2. **Github:**
The default branch is switched to “hardknott_5.10.72-2.2.0” and create release tag for u-boot/linux/yocto bsp meta layer.
[https://github.com/technexion/](https://github.com/technexion/)

3. **New feature:**
    Software Migration for products with Realtek PHY:
    EDM-G-IMX8MP
    EDM-G-IMX8MM
    PICO-IMX8MQ-PI
    PICO-IMX8MM-PI
    AXON-IMX8MP
    EDM-IMX6
    TEP1-IMX7
    [https://developer.technexion.com/docs/software-migration-for-products-with-realtek-phy](https://developer.technexion.com/docs/software-migration-for-products-with-realtek-phy)

    New camera sensors for edm-g-imx8mp
    TEVI-AR0821
    TEVI-AR1335

    New vizionlink support for TEVI-AR series on edm-g-imx8mp
    TEVI-AR0821
    TEVI-AR1335

    TEVI-AR series and vizionlink support on 8MPLUSLPD4-EVK
    [https://developer.technexion.com/docs/tevi-ar-cam-on-nxp-8mpluslpd4-evk-board
](https://developer.technexion.com/docs/tevi-ar-cam-on-nxp-8mpluslpd4-evk-board
)


#### To-Do:
1. **U-boot splash screen for i.mx8:**
Because u-boot 2021.04 changes to use device model for display driver, we have to look for a new way to switch to different panels and add MIPI-DSI panel driver in u-boot.
2. **FreeRTOS:**
MCUExpress v2.11 will be the target version to be supported for cortex-M4/M7 core.

#### Prebuilt Images Available for Download
Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
| EDM-G-IMX8MP |  [edm-g-imx8mp_edm-g-wb_yocto-3.3-qt5_qca9377_hdmi-1920x1080_20221202.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-3.3-qt5_qca9377_hdmi-1920x1080_20221202.zip)   |
| EDM-G-IMX8MM |  [edm-g-imx8mm_edm-g-wb_yocto-3.3-qt5_qca9377_lvds-1280x800_20221202.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-3.3-qt5_qca9377_lvds-1280x800_20221202.zip)   |
| EDM-G-IMX8MN |  [edm-g-imx8mn_edm-g-wb_yocto-3.3-qt5_qca9377_lvds-1280x800_20221202.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mn/archived/edm-g-imx8mn_edm-g-wb_yocto-3.3-qt5_qca9377_lvds-1280x800_20221202.zip)   |
| PICO-IMX8MM |  [pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-3.3-qt5_qca9377_mipi5-1280x720_20221202.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-3.3-qt5_qca9377_mipi5-1280x720_20221202.zip)   |
| PICO-IMX8MQ |  [pico-imx8mq/archived/pico-imx8mq_pico-pi-8m_yocto-3.3-qt5_qca9377_mipi5-1280x720_20221202.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8mq_pico-pi-8m_yocto-3.3-qt5_qca9377_mipi5-1280x720_20221202.zip)   |
| AXON-IMX8MP |  [axon-imx8mp_axon-wizard_yocto-3.3-qt5_qca9377_hdmi-1920x1080_20221202.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_yocto-3.3-qt5_qca9377_hdmi-1920x1080_20221202.zip)   |

| ARM32 |  |
| --- | --- |
| PICO-IMX6 |  [pico-imx6_pico-nymph_yocto-3.3-qt5_qca9377_hdmi_20230202.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_pico-nymph_yocto-3.3-qt5_qca9377_hdmi_20230202.zip)   |
| PICO-IMX7 |  [pico-imx7_pico-pi_yocto-3.3-qt5_qca9377_lcd-800x480_20230202.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pico-pi_yocto-3.3-qt5_qca9377_lcd-800x480_20230202.zip)   |
| PICO-IMX6UL/ULL |  [pico-imx6ul_pico-pi_yocto-3.3-qt5_qca9377_lcd-800x480_20221202.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pico-pi_yocto-3.3-qt5_qca9377_lcd-800x480_20221202.zip)   |
| EDM-IMX6 |  [edm-imx6_edm-fairy_yocto-3.3-qt5_qca9377_hdmi_20221202.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_edm-fairy_yocto-3.3-qt5_qca9377_hdmi_20221202.zip)   |
| WANDBOARD |  [wandboard-imx6_yocto-3.3-qt5_qca9377_20221202.zip](https://download.technexion.com/demo_software/WANDBOARD/wandboard-imx6/archived/wandboard-imx6_yocto-3.3-qt5_qca9377_20221202.zip)   |

| HMI |  |
| --- | --- |
| TEP-0500-IMX7/TEP-0700-IMX7 |  [tep1-imx7_yocto-3.3-qt5_lcd-800x600_20221202.zip](https://download.technexion.com/demo_software/TEP/IMX7/tep0500-imx7d/archived/tep1-imx7_yocto-3.3-qt5_lcd-800x600_20221202.zip)   |
| TEP-1010-IMX6/TWP-1010-IMX6 |  [tep1010-imx6_yocto-3.3-qt5_lvds-1280x800_20221202.zip](https://download.technexion.com/demo_software/TEP/IMX6/tep1010-imx6/archived/tep1010-imx6_yocto-3.3-qt5_lvds-1280x800_20221202.zip)   |
| TEP-1560-IMX6/TWP-1560-IMX6 |  [tep1560-imx6_yocto-3.3-qt5_lvds-1368x768_20221202.zip](https://download.technexion.com/demo_software/TEP/IMX6/tep1560-imx6/archived/tep1560-imx6_yocto-3.3-qt5_lvds-1368x768_20221202.zip)   |
| TC-0700/TC-0710 |  [tc0700_edm1-cf-imx6_yocto-3.3-qt5_qca9377_20221202.zip](https://download.technexion.com/demo_software/TC/tc-0700/archived/tc0700_edm1-cf-imx6_yocto-3.3-qt5_qca9377_20221202.zip)   |

| BOX PC |  |
| --- | --- |
| TEK3-IMX6 |  [tek3-imx6_yocto-3.3-qt5_hdmi_20221202.zip](https://download.technexion.com/demo_software/TEK/IMX6/tek3-imx6/archived/tek3-imx6_yocto-3.3-qt5_hdmi_20221202.zip)   |
