---
title: Yocto 4.2 (<PERSON><PERSON><PERSON>) 2024Q2 Release Notes
sidebar_position: 40
---
## Yocto 4.2 (<PERSON><PERSON><PERSON>) 2024Q2 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2023.04_6.1.55_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2023.04_6.1.55_2.2.0-stable) |7c7dd0dc2b04e8d8c5077d14b736f80580c0fc61|
|Kernel| [tn-imx_6.1.55_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.1.55_2.2.0-stable)|af6b1a093228948f61dc43a3dc246e45325bac55|
|TN Yocto BSP| [mickledore_6.1.55-2.2.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/mickledore_6.1.55-2.2.0-stable)|d48959a54199c313db99663d322ec0f51e481c0d|
- [**Build Yocto from source code**](https://github.com/TechNexion/tn-imx-yocto-manifest)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)

---
### Supported Platforms in this Release
#### ARM64 SOM

<>
  <table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
    {/* Form factor */}
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#F9F801" }} align="left">
          <b>Form factor</b>
        </td>
        <td style={{ backgroundColor: "#96F8C8" }} align="center" colSpan={4}>
          <b>EDM-G</b>
        </td>
        <td style={{ backgroundColor: "#F9C7F9" }} align="center" colSpan={2}>
          <b>PICO</b>
        </td>
        <td style={{ backgroundColor: "#01F8C8" }} align="center" colSpan={4}>
          <b>AXON</b>
        </td>
      </tr>
      {/* Product */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          EDM-G-IMX8MP
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          EDM-G-IMX8MM
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          PICO-IMX8MM
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          AXON-IMX8MP
        </td>
        <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
          AXON-IMX93
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center" colSpan={2}>
          1/2/4/6/8 GB
        </td>
        <td align="center" colSpan={2}>
          1/2/4/8 GB
        </td>
        <td align="center" colSpan={2}>
          1/2/4 GB
        </td>
        <td align="center" colSpan={2}>
          2/4/8 GB
        </td>
        <td align="center">2 GB</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center" colSpan={2}>
          QCA9377-3(SDIO)
          <br />
          BT 5.0
        </td>
        <td align="center">
          IW416(SDIO)
          <br />
          BT 5.2
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Baseboard
        </td>
        <td align="center">EDM-G-WB</td>
        <td align="center">EDM-G-WIZARD</td>
        <td align="center">EDM-G-WB</td>
        <td align="center">EDM-G-WIZARD</td>
        <td align="center">WIZARD</td>
        <td align="center">PI</td>
        <td align="center" colSpan={2}>
          WIZARD
        </td>
        <td align="center">AXON-WB</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Atheros/Realtek phy
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Atheros only</td>
        <td align="center">Yes</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">Realtek</td>
      </tr>
      {/* MIPI Panel support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>MIPI Panel support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          5 inch panel - ILI9881C
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          10.1 inch panel - g101uan02
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      {/* LVDS panel support (Native LVDS or MIPI to LVDS) */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>LVDS panel support (Native LVDS or MIPI to LVDS)</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          10.1 inch panel - vl101-12880YL-C01
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          10.1 inch panel - vl101-12880YL-C13
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          15.6 inch panel - vl15613676
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          21.5 inch panel - vl215192108
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      {/* HDMI */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>HDMI</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Native HDMI
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          MIPI to HDMI
          <br />
          (via ADV7535)
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Dual HDMI
        </td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      {/* Parallel TTL panel support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Parallel TTL panel support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          5 inch panel
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          7 inch panel
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          8 inch panel
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Peripherals */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Peripherals</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          NFC Reader - PN7150 - CLIX
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          NFC Reader - PN7150 - I2C
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink-HDMI
          <br />
          (HDMI input/capture)
        </td>
        <td align="center">
          Yes
          <br />
          (Dual/Single)
        </td>
        <td align="center">
          Yes
          <br />
          (Dual/Single)
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          Yes
          <br />
          (Single)
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-OV5640
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR Camera
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVS-AR Camera
        </td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLI-OV5640
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLI-AR Camera
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLS3-AR Camera
        </td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      {/* Software support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VizionSDK/VizionViewer
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center" colSpan={2}>
          Yes
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.10)
        </td>
        <td align="center">Yes</td>
        <td align="center">Yes</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center">NA</td>
        <td align="center" colSpan={2}>
          NA
        </td>
        <td align="center">NA</td>
      </tr>
    </tbody>
  </table>
  #### HMI
  <table border={0} style={{ fontSize: 12 }}>
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#96F8C8" }} align="center">
          TEP-IMX8MP
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Type</b>
        </td>
        <td align="center">HMI</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          SOC variants
        </td>
        <td align="center">i.mx8m Plus</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center">2/4/8GB</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless(Optional)
        </td>
        <td align="center">
          QCA9377-5(PCIE)
          <br />
          BT 5.0
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Atheros/Realtek phy
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* VIZIONPANEL support (MIPI to LVDS)  */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>VIZIONPANEL support (MIPI to LVDS)izionpanel </b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Peripherals */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Peripherals</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink-HDMI (HDMI input / capture)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-OV5640
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR Camera
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVS-AR Camera
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLI-OV5640
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLI-AR Camera
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLS3-AR Camera
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Software support  */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VizionSDK/VizionViewer
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.9)
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">Yes</td>
      </tr>
    </tbody>
  </table>
  #### BOX PC
  <table border={0} style={{ fontSize: 12 }}>
    <tbody>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Product</b>
        </td>
        <td style={{ backgroundColor: "#96F8C8" }} align="center">
          TEK-IMX8MP
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          <b>Type</b>
        </td>
        <td align="center">BOX</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          SOC variants
        </td>
        <td align="center">i.mx8m Plus</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          DRAM Variants
        </td>
        <td align="center">2/4/8 GB</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Wireless(Optional)
        </td>
        <td align="center">
          QCA9377-5(PCIE)
          <br />
          BT 5.0
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Realtek phy
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Native HDMI
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Peripherals */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
          <b>Peripherals</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          Vizionlink-HDMI (HDMI input / capture)
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-OV5640
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVI-AR Camera
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          TEVS-AR Camera
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLI-OV5640
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLI-AR Camera
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VLS3-AR Camera
        </td>
        <td align="center">Yes</td>
      </tr>
      {/* Software support */}
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
          <b>Software support</b>
        </td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          VizionSDK/VizionViewer
        </td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          FreeRTOS(MCUXpresso v2.9)
        </td>
        <td align="center">NA</td>
      </tr>
      <tr>
        <td style={{ backgroundColor: "#FAE197" }} align="left">
          U-boot splash screen
        </td>
        <td align="center">NA</td>
      </tr>
    </tbody>
  </table>
</>


---
### Camera Module
- i.mx8 supports the following camera modules
    - Only support TEVS-AR series
<br/>
---
### Prebuilt Images Available for Download

Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_yocto-4.2-qt6_qca9377_hdmi-1920x1080_20240417.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-4.2-qt6_qca9377_hdmi-1920x1080_20240417.zip) |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_yocto-4.2-qt6_qca9377_lvds-1280x800_20240417.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-4.2-qt6_qca9377_lvds-1280x800_20240417.zip) |
|PICO-IMX8MM| [pico-imx8mm_pico-pi-8m_yocto-4.2-qt6_qca9377_mipi5-1280x720_20240417.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-4.2-qt6_qca9377_mipi5-1280x720_20240417.zip) |
|AXON-IMX8MP| [axon-imx8mp_axon-wizard_yocto-4.2-qt6_qca9377_hdmi-1920x1080_20240417.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_yocto-4.2-qt6_qca9377_hdmi-1920x1080_20240417.zip) |
|AXON-IMX93| [axon-imx93_axon-wb_yocto-4.2-qt6_iw416_lvds-1280x800_20240418.zip](https://download.technexion.com/demo_software/AXON/IMX9/axon-imx93/archived/axon-imx93_axon-wb_yocto-4.2-qt6_iw416_lvds-1280x800_20240418.zip) |


| HMI |  |
| --- | --- |
|TEP-IMX8MP| [tep-imx8mp_yocto-4.2-qt6_hdmi_20240417.zip](https://download.technexion.com/demo_software/TEP/IMX8/tep-imx8mp/archived/tep-imx8mp_yocto-4.2-qt6_hdmi_20240417.zip) |

| BOX PC |  |
| --- | --- |
|TEK-IMX8MP| [tek-imx8mp_yocto-4.2-qt6_hdmi_20240417.zip](https://download.technexion.com/demo_software/TEK/IMX8/tek-imx8mp/archived/tek-imx8mp_yocto-4.2-qt6_hdmi_20240417.zip) |

| EVK |  |
| --- | --- |
|IMX8MP-LPDDR4-EVK| [imx8mp-lpddr4-evk_yocto-4.2-qt6_20240417.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-LPDDR4/archived/imx8mp-lpddr4-evk_yocto-4.2-qt6_20240417.zip) |
|IMX8MP-DDR4-EVK| [imx8mp-ddr4-evk_yocto-4.2-qt6_20240417.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-DDR4/archived/imx8mp-ddr4-evk_yocto-4.2-qt6_20240417.zip) |