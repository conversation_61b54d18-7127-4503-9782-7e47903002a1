---
title: Yocto 3.3 (Hardknott) 2022Q1 Release Notes
sidebar_position: 48
---
## Yocto 3.3 (<PERSON>k<PERSON>t) 2022Q1 Release Notes

#### Links for source repositories:

u-boot: [tn-imx_v2021.04_5.10.72_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2021.04_5.10.72_2.2.0-stable)

kernel: [tn-imx_5.10.72_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_5.10.72_2.2.0-stable)

Yocto: [hardknott_5.10.72-2.2.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/hardknott_5.10.72-2.2.0-stable)

[Build steps](https://github.com/TechNexion/tn-imx-yocto-manifest)

### Supported Platforms in this Release
#### ARM64 SOM

![YP3_2022Q1_64bit_platform_support_v2.png](//img/YP3_2022Q1_64bit_platform_support_v2.png)

#### ARM32 SOM
![YP3_2022Q1_32bit_platform_support.png](//img/YP3_2022Q1_32bit_platform_support.png)

#### HMI
![YP3_2022Q1_HMI_support.png](//img/YP3_2022Q1_HMI_support.png)

#### Explanation:
1. **CAM-OV5645 and TEVI-OV5640:**
For i.mx8, in u-boot, the camera is detected by probing for an EEPROM of a connected TEVI camera to tell the difference of CAM-OV5640 and TEVI-OV5640 then load device tree overlay accordingly. In i.MX6 and i.MX7, this release does not yet support device tree overlays. We plan to also support device overlay to support different kinds of panels and cameras. Eventually, both CAM-OV5645 and TEVI-OV5640 will be supported in ARM32 and ARM64 products..

3. **Github:**
The default branch is switched to “hardknott_5.10.72-2.2.0” and create release tag for u-boot/linux/yocto bsp meta layer.
[https://github.com/technexion/](https://github.com/technexion/)

#### To-Do:
1. **U-boot splash screen for i.mx8:**
Because u-boot 2021.04 changes to use device model for display driver, we have to look for a new way to switch to different panels and add MIPI-DSI panel driver in u-boot.
2. **FreeRTOS:**
MCUExpress v2.10 will be the target version to be supported for cortex-M4/M7 core.

#### Prebuilt Images Available for Download
Prebuilt images will soon be avialable for download via TechNexion's server.

| ARM64|  |
| --- | --- |
| EDM-G-IMX8MP |  [edm-g-imx8mp_wb_yocto-3.3_xwayland_qca_20220208141322.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wb_yocto-3.3_xwayland_qca_20220208141322.zip)   |
| EDM-G-IMX8MM |  [edm-g-imx8mm_wb_yocto-3.3_xwayland_qca_20220208145126.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wb_yocto-3.3_xwayland_qca_20220208145126.zip)   |
| EDM-G-IMX8MN |  [edm-g-imx8mn_wb_yocto-3.3_xwayland_qca_20220208152725.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mn/archived/edm-g-imx8mn_wb_yocto-3.3_xwayland_qca_20220208152725.zip)   |
| PICO-IMX8MM |  [pico-imx8mm_pi_yocto-3.3_xwayland_qca_20220208160445.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pi_yocto-3.3_xwayland_qca_20220208160445.zip)   |
| PICO-IMX8MQ |  [pico-imx8mq_pi_yocto-3.3_xwayland_qca_20220208164022.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8mq_pi_yocto-3.3_xwayland_qca_20220208164022.zip)   |
| AXON-IMX8MP |  [axon-imx8mp_wizard_yocto-3.3_xwayland_qca_20220208133509.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_wizard_yocto-3.3_xwayland_qca_20220208133509.zip)   |

| ARM32|  |
| --- | --- |
| PICO-IMX6 |  [pico-imx6_nymph_yocto-3.3_wayland_qca_20220208180806.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_nymph_yocto-3.3_wayland_qca_20220208180806.zip)   |
| PICO-IMX7 |  [pico-imx7_pi_yocto-3.3_wayland_qca_20220208171747.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pi_yocto-3.3_wayland_qca_20220208171747.zip)   |
| PICO-IMX6UL/ULL |  [pico-imx6ul_pi_yocto-3.3_wayland_qca_20220208190817.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pi_yocto-3.3_wayland_qca_20220208190817.zip)   |
| EDM-IMX6 |  [edm-imx6_fairy_yocto-3.3_wayland_qca_20220208183807.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_fairy_yocto-3.3_wayland_qca_20220208183807.zip)   |

| HMI|  |
| --- | --- |
| TEP-0500-IMX7/TEP-0700-IMX7 |  [tep1-imx7_yocto-3.3_wayland_ath-pci_20220208174210.zip](https://download.technexion.com/demo_software/TEP/IMX7/tep0500-imx7d/archived/tep1-imx7_yocto-3.3_wayland_ath-pci_20220208174210.zip)   |
