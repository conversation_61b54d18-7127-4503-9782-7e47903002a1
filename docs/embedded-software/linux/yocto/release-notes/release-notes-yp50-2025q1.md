---
title: Yocto 5.0 (Scarthgap) 2025Q1 Release Notes
sidebar_position: 37
---

<!-- Note: Heading 1 here is what becomes the category name -->

# Yocto 5.0 (Scarthgap) 2025Q1 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2024.04_6.6.52_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2024.04_6.6.52_2.2.0-stable)  |153e8e48579c3f0c15a16a3d2649354868ba7912|
|Kernel| [tn-imx_6.6.52_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.6.52_2.2.0-stable) |c8aa74583f0a56a2237e047a07f9508d0be6fc1f|
|TN Yocto BSP| [scarthgap_6.6.52-2.2.0-stable](https://github.com/TechNexion/meta-tn-imx-bsp/tree/scarthgap_6.6.52-2.2.0-stable) |610a8a78db47dc1f100f8a3e5228ad990de61fd4|
- [**Build Yocto from source code**](https://github.com/TechNexion/tn-imx-yocto-manifest)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)
---
### Supported Platforms in this Release
#### ARM64 SOM

<table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
  {/* Form factor */}
  <tbody>
    <tr>
      <td style={{ backgroundColor: "#F9F801" }} align="left">
        <b>Form factor</b>
      </td>
      <td style={{ backgroundColor: "#96F8C8" }} align="center" colSpan={4}>
        <b>EDM-G</b>
      </td>
      <td style={{ backgroundColor: "#F9C7F9" }} align="center" colSpan={2}>
        <b>PICO</b>
      </td>
      <td style={{ backgroundColor: "#01F8C8" }} align="center" colSpan={2}>
        <b>AXON</b>
      </td>
      <td style={{ backgroundColor: "#F9C7F9" }} align="center" colSpan={1}>
        <b>EDM</b>
      </td>
    </tr>
    {/* Product */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        <b>Product</b>
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        EDM-G-IMX8MP
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        EDM-G-IMX8MM
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        PICO-IMX8MM
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={1}>
        AXON-IMX8MP
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={1}>
        AXON-IMX93
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={1}>
        EDM-IMX93
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        DRAM Variants
      </td>
      <td align="center" colSpan={2}>
        1/2/4/6/8 GB
      </td>
      <td align="center" colSpan={2}>
        1/2/4/8 GB
      </td>
      <td align="center" colSpan={2}>
        1/2/4 GB
      </td>
      <td align="center">2/4/8 GB</td>
      <td align="center">2 GB</td>
      <td align="center">2 GB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Wireless
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center">
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center">
        IW416(SDIO)
        <br />
        BT 5.2
      </td>
      <td align="center">
        IW416(SDIO)
        <br />
        BT 5.2
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Baseboard
      </td>
      <td align="center">EDM-G-WB</td>
      <td align="center">EDM-G-WIZARD</td>
      <td align="center">EDM-G-WB</td>
      <td align="center">EDM-G-WIZARD</td>
      <td align="center">WIZARD</td>
      <td align="center">PI</td>
      <td align="center">WIZARD</td>
      <td align="center">AXON-WB</td>
      <td align="center">EDM-WB</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Atheros/Realtek phy
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Atheros only</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Realtek</td>
      <td align="center">Realtek</td>
    </tr>
    {/* MIPI Panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>MIPI Panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel - ILI9881C
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* LVDS panel support (Native LVDS or MIPI to LVDS) */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>LVDS panel support (Native LVDS or MIPI to LVDS)</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - vl101-12880YL-C01
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - vl101-12880YL-C13
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        15.6 inch panel - vl15613676
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        15.6 inch panel - vl156192108
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        21.5 inch panel - vl215192108
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* HDMI */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>HDMI</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Native HDMI
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        MIPI to HDMI
        <br />
        (via ADV7535)
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Dual HDMI
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* Parallel TTL panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Parallel TTL panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        7 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        8 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    {/* Peripherals */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Peripherals</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        NFC Reader - PN7150 - CLIX
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        NFC Reader - PN7150 - I2C
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Vizionlink-HDMI
        <br />
        (HDMI input/capture)
      </td>
      <td align="center">
        Yes
        <br />
        (Dual/Single)
      </td>
      <td align="center">
        Yes
        <br />
        (Dual/Single)
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">
        Yes
        <br />
        (Single)
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVS-AR Camera
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLS3-AR Camera
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    {/* Software support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Software support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VizionSDK/VizionViewer
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        FreeRTOS(MCUXpresso v2.10)
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        U-boot splash screen
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes(10" LVDS)</td>
      <td align="center">Yes(10" LVDS)</td>
      <td align="center">Yes(5" MIPI)</td>
      <td align="center">Yes(5" MIPI)</td>
      <td align="center">NA</td>
      <td align="center">Parallel TTL panel</td>
      <td align="center">Parallel TTL panel</td>
    </tr>
  </tbody>
</table>

#### ARM32 SOM
<table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
  <tbody>
    <tr>
      <td style={{ backgroundColor: "#F9F801" }} align="left">
        <b>Form factor</b>
      </td>
      <td style={{ backgroundColor: "#F9C7F9" }} align="center" colSpan={3}>
        <b>PICO</b>
      </td>
      <td style={{ backgroundColor: "#F9F801" }} align="center" colSpan={1}>
        <b>EDM</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        <b>Product</b>
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center">
        PICO-IMX6
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center">
        PICO-IMX7D
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center">
        PICO-IMX6UL
        <br />
        PICO-IMX6ULL
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center">
        EDM-IMX6
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        SOC variants
      </td>
      <td align="center">i.mx6Solo/DL/Quad (No QP)</td>
      <td align="center">i.mx7D</td>
      <td align="center">
        i.mx6UL 528MHz
        <br />
        I.mx6ULL 900MHz
      </td>
      <td align="center">i.mx6Solo/DL/Quad (No QP)</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        DRAM Variants
      </td>
      <td align="center">
        Solo: 512MB
        <br />
        DL/Quad: 1GB
      </td>
      <td align="center">
        512MB
        <br />
        1GB
        <br />
        2GB
      </td>
      <td align="center">
        256MB
        <br />
        512MB
      </td>
      <td align="center">
        Solo: 512MB
        <br />
        DL: 1GB
        <br />
        Quad: 2GB
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Wireless
      </td>
      <td align="center">
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center">
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center">
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center">
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Baseboard
      </td>
      <td align="center">Nymph</td>
      <td align="center">PI-GL</td>
      <td align="center">PI-FL</td>
      <td align="center">Fairy</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Atheros/Realtek phy
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Atheros</td>
      <td align="center">Yes</td>
    </tr>
    {/* LVDS panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
        <b>LVDS Panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        7 inch panel
      </td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
    </tr>
    {/* Parallel TTL panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
        <b>Parallel TTL panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        7 inch panel
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
    </tr>
    {/* Peripherals */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
        <b>Peripherals</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* Software support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={6}>
        <b>Software support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        FreeRTOS(MCUXpresso v2.9)
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        U-boot splash screen
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
  </tbody>
</table>

#### HMI
<table border={0} style={{ fontSize: 12 }}>
  <tbody>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        <b>Product</b>
      </td>
      <td style={{ backgroundColor: "#96F8C8" }} align="center">
        TC-0700
        <br />
        TC-0710
      </td>
      <td style={{ backgroundColor: "#F9C7F9" }} align="center">
        TC-1010
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        <b>Type</b>
      </td>
      <td align="center">HMI</td>
      <td align="center">HMI</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        SOC variants
      </td>
      <td align="center">i.mx6Solo/DL/Quad</td>
      <td align="center">i.mx6Solo/DL/Quad</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        DRAM Variants
      </td>
      <td align="center">
        Solo: 512MB
        <br />
        DL: 1GB
        <br />
        Quad: 2GB
      </td>
      <td align="center">
        Solo: 512MB
        <br />
        DL: 1GB
        <br />
        Quad: 2GB
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Wireless(Optional)
      </td>
      <td align="center">
        QCA9377-5(PCIE)
        <br />
        BT 5.0
      </td>
      <td align="center">
        QCA9377-5(PCIE)
        <br />
        BT 5.0
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Atheros/Realtek phy
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    {/* Peripherals */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={3}>
        <b>Peripherals</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Vizionlink-HDMI (HDMI input / capture)
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVS-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLI-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLS3-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* Software support  */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={3}>
        <b>Software support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VizionSDK/VizionViewer
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        FreeRTOS(MCUXpresso v2.9)
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        U-boot splash screen
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
  </tbody>
</table>

---
### [Camera Module](https://www.technexion.com/products/embedded-vision/image-sensors)
- i.mx8 supports the following camera modules
    - [TechNexion TEVS-AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
    - [TechNexion VLS3 -AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
- i.mx93 supports the following camera modules
    - [TechNexion TEVS-AR series](https://developer.technexion.com/docs/imx93-tevs-camera-usage-guide)
    - [TechNexion VLS3 -AR series](https://developer.technexion.com/docs/imx93-tevs-camera-usage-guide)
- i.mx95 supports the following camera modules
    - TechNexion TEVS-AR series
---
### Prebuilt Images Available for Download

Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20241230.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20241230.zip)  |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_yocto-5.0-qt6_qca9377_lvds-1280x800_20241230.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_yocto-5.0-qt6_qca9377_lvds-1280x800_20241230.zip)  |
|PICO-IMX8MM| [pico-imx8mm_pico-pi-8m_yocto-5.0-qt6_qca9377_mipi5-1280x720_20241230.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_yocto-5.0-qt6_qca9377_mipi5-1280x720_20241230.zip)  |
|AXON-IMX8MP| [axon-imx8mp_axon-wizard_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20241230.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_yocto-5.0-qt6_qca9377_hdmi-1920x1080_20241230.zip)  |
|AXON-IMX93| [axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20241230.zip](https://download.technexion.com/demo_software/AXON/IMX9/axon-imx93/archived/axon-imx93_axon-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20241230.zip)  |
|EDM-IMX93| [edm-imx93_edm-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20241230.zip](https://download.technexion.com/demo_software/EDM/IMX9/edm-imx93/archived/edm-imx93_edm-wb_yocto-5.0-qt6_iw416_lvds-1280x800_20241230.zip)  |

| ARM32 |  |
| --- | --- |
|PICO-IMX7| [pico-imx7_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20241230.zip](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20241230.zip)  |
|PICO-IMX6| [pico-imx6_pico-nymph_yocto-5.0-qt6_qca9377_hdmi_20241230.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_pico-nymph_yocto-5.0-qt6_qca9377_hdmi_20241230.zip)  |
|PICO-IMX6UL| [pico-imx6ul_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20241230.zip](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ul_pico-pi_yocto-5.0-qt6_qca9377_lcd-800x480_20241230.zip)  |
|EDM-IMX6| [edm-imx6_edm-fairy_yocto-5.0-qt6_qca9377_hdmi_20241230.zip](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm-imx6_edm-fairy_yocto-5.0-qt6_qca9377_hdmi_20241230.zip)  |

| HMI |  |
| --- | --- |
| TC-0700 | [tc0700_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20241230.zip](https://download.technexion.com/demo_software/TC/tc-0700/archived/tc0700_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20241230.zip)  |
| TC-1010 | [tc1010_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20241230.zip](https://download.technexion.com/demo_software/TC/tc-1010/archived/tc1010_edm1-cf-imx6_yocto-5.0-qt6_qca9377_20241230.zip)  |

| EVK |  |
| --- | --- |
|IMX8MP-LPDDR4-EVK| [imx8mp-lpddr4-evk_yocto-5.0-qt6_20241230.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-LPDDR4/archived/imx8mp-lpddr4-evk_yocto-5.0-qt6_20241230.zip)  |
|IMX8MP-DDR4-EVK| [imx8mp-ddr4-evk_yocto-5.0-qt6_20241230.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX8MP-DDR4/archived/imx8mp-ddr4-evk_yocto-5.0-qt6_20241230.zip)  |
|IMX93-11X11-LPDDR4X-EVK| [imx93-11x11-lpddr4x-evk_yocto-5.0-qt6_20241230.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX93-LPDDR4X/archived/imx93-11x11-lpddr4x-evk_yocto-5.0-qt6_20241230.zip)  |
|IMX95-19X19-LPDDR5-EVK| [imx95-19x19-lpddr5-evk_yocto-5.0-qt6_20241230.zip](https://download.technexion.com/demo_software/EVK/NXP/IMX95-LPDDR5/archived/imx95-19x19-lpddr5-evk_yocto-5.0-qt6_20241230.zip)  |