---
title: Building an Image in Docker Container
description: Building an Image in Docker Container
---

## Introduction
Set up the build environment using container methods.

### Set up build environment on host PC:
The build environment is tested under Ubuntu 22.04.

#### Install required package:
```shell
sudo apt install docker.io
```

#### Download TechNexion's dockerfile:
```shell
wget https://raw.githubusercontent.com/TechNexion/meta-tn-imx-bsp/kirkstone_5.15.71-2.2.0-stable/tools/container/dockerfile
```

#### Build the docker container:
```shell
sudo docker build -t tn-develop-ubuntu .
```

#### Check the built docker container:
```shell
$ sudo docker images
REPOSITORY          TAG       IMAGE ID       CREATED         SIZE
tn-develop-ubuntu   latest    b438b5e2e8fb   5 minutes ago   2.15GB
ubuntu              20.04     88bd68917189   8 weeks ago     72.8MB
```

### Use docker container:
Use this docker container to start building Yocto image.

#### Create a new docker container:
```shell
$ sudo docker run -it -u jenkins -v ${directory_in_host_machine}:${directory_in_docker} tn-develop-ubuntu bash
(-v: use to bind volume to the directory in host machine to a directory in docker)
(password: jenkins)

To run a command as administrator (user "root"), use "sudo <command>".
See "man sudo_root" for details.

jenkins@818cacdead74:/$ cd
jenkins@818cacdead74:~$
```

:::info
On Ubuntu 22 container the seccomp protection feature of docker has to be disabled when creating the build container by adding the parameter `--security-opt seccomp=unconfined` after the `-it` parameter in the command line above.
:::

#### Re-entry is required when exiting:
```shell
$ sudo docker start <your container id>
$ sudo docker attach <your container id>
```

e.g.
```shell
$ sudo docker ps -a
CONTAINER ID   IMAGE               COMMAND   CREATED          STATUS                      PORTS     NAMES
818cacdead74   tn-develop-ubuntu   "bash"    45 minutes ago   Exited (0) 19 seconds ago             hopeful_villanir

$ sudo docker start 818cacdead74
$ sudo docker attach 818cacdead74
```
