---
title: Building Your First Image
description: Build your first image with Yoct<PERSON> for TechNexion products using our metadata and setup scripts
sidebar_position: 1
---

:::info [Updated for Yocto 5.2 (<PERSON><PERSON><PERSON><PERSON>)]
The following steps are updated for Yocto Project version 5.2 (Walnascar).
:::

## Introduction
This article provides step-by-step instructions for building a Linux image with TechNexion's Yocto BSP, which supports the i.MX series of processors.

### System Requirements
Building an image from source with Yocto requires a host with the following:
* Host: Ubuntu 22.04
* 8GB/16GB RAM (more is better)
* 4 Core processor
* 200 GB storage
* Swap space: 16GB
  * If less memory is used, then some additional swap space may be needed. Inadequate memory may result in slow builds and/or random build errors.
* Network
  * The host must be connected to a network and have access to the Internet so that all source code and tools may be downloaded.

#### Set up build environment on host PC:

The build environment is tested under Ubuntu 18.04 and Ubuntu 20.04.

#### Install required packages:

First update the host package list
```bash
sudo apt-get update
```

Next, install the necessary packages:

```bash
sudo apt-get install gawk wget git git-core diffstat unzip texinfo gcc-multilib build-essential \
chrpath socat cpio python python3 python3-pip python3-pexpect \
python3-git python3-jinja2 libegl1-mesa pylint3 rsync bc bison \
xz-utils debianutils iputils-ping libsdl1.2-dev xterm \
language-pack-en coreutils texi2html file docbook-utils \
python-pysqlite2 help2man desktop-file-utils \
libgl1-mesa-dev libglu1-mesa-dev mercurial autoconf automake \
groff curl lzop asciidoc u-boot-tools libreoffice-writer \
sshpass ssh-askpass zip xz-utils zstd liblz4-tool kpartx vim screen
```

### Install the Repo Tool
There are many respositories required. Google's `repo` tool is used to manage this.
```bash
mkdir ~/bin
curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
chmod a+x ~/bin/repo
PATH=${PATH}:~/bin
```
### Download the BSP source
```bash
PATH=${PATH}:~/bin
mkdir edm_yocto
cd edm_yocto
```
Next, initialize the repositories based on the release you wish to build:

| Release | Command |
|---|---
|Yocto Walnascar 5.2 | `repo init -u https://github.com/TechNexion/tn-imx-yocto-manifest.git -b walnascar_6.12.y-stable -m imx-6.12.20-2.0.0.xml`

:::warning
The content of this article pertains to Yocto 5.2 (Walnascar) and we intend to keep this current for future revisions.

For earlier revisions, please review the README in the project manifest here:
[https://github.com/TechNexion/tn-imx-yocto-manifest/#readme](https://github.com/TechNexion/tn-imx-yocto-manifest/#readme)

Use the dropdown menu to select the target branch.
![select-yocto-version-github-dropdown.png](//img/yocto-manifest-dropdown-walnascar-20250915.jpg)
:::

### Fetch the source code:
```bash
repo sync -j$(nproc)
```
### Create build environment
There are several options regarding the build environment that you can use.

#### Prepare WIFI/BT firmware

You may optionally choose to add WIFI/BT firmware to the build. To do  this, add the argument "WIFI_FIRMWARE=y" in during BSP setup. Please refer to following for an explanation of other BSP initialization options.

#### Configurations for setup script

`MACHINE` is the target of build. It usually corresponds to the name of SOM or SBC.
For more information, please check the file under `“sources/meta-tn-imx-bsp/conf/machine”`.

`DISTRO` identifies the Yocto distribution and is the new way to configure for any backends.

`“-b”` specify the build directory.

`DISPLAY` is the disply type. This option only works on i.MX6 (i.MX6 Solo/Dual Lite/Dual/Quad) SOMs. Does not work for products based on i.MX6UL/ULL, i.MX6SX, and i.MX7.

`BASEBOARD` is the baseboard type.

`WIFI_MODULE` This parameter specifies the type of WLAN module on board.

`WIFI_FIRMWARE` is to choose to add WLAN firmware files in target rootfs or not.

#### Build configurations for supported hardware

The following is a list of valid options for `MACHINE` based on the hardware.

|Parameter | Available options | Description
|---|---|---
| `MACHINE` | axon-imx93 | Compatible with TechNexion + `AXON-IMX93 (i.MX93)`
| | edm-imx93 | Compatible with TechNexion + `EDM-IMX93 (i.MX93)`
| | pico-imx93 | Compatible with TechNexion + `PICO-IMX93 (i.MX93)`
| | axon-imx91 | Compatible with TechNexion + `AXON-IMX91 (i.MX91)`
| | edm-imx91 | Compatible with TechNexion + `EDM-IMX91 (i.MX91)`
| | imx93-11x11-lpddr4x-evk | Compatible with NXP + `IMX93-11X11-LPDDR4X-EVK (i.MX93)`
| | imx95-19x19-lpddr5-evk | Compatible with NXP + `IMX95-LPDDR5-EVK (i.MX95)`
| | edm-g-imx8mm | Compatible with TechNexion + `EDM-G-IMX8MM (i.MX8M Mini)`
| | edm-g-imx8mp | Compatible with TechNexion + `EDM-G-IMX8MP (i.MX8M Plus)`
| | pico-imx8mm | Compatible with TechNexion + `PICO-IMX8MM (i.MX8M Mini)`
| | axon-imx8mp | Compatible with TechNexion + `AXON-IMX8MP (i.MX8M Plus)`
| | imx8mp-lpddr4-evk | Compatible with NXP + `IMX8MP-LPDDR4-EVK (i.MX8M Plus)`
| | imx8mp-ddr4-evk | Compatible with NXP + `IMX8MP-DDR4-EVK (i.MX8M Plus)`
| | pico-imx7 | Compatible with TechNexion + `PICO-IMX7 (i.MX7 Dual)`
| | edm-imx6 | Compatible with TechNexion + `EDM-IMX6 (i.MX6 Solo/Duallite/Quad/QuadPlus)`
| | pico-imx6 | Compatible with TechNexion + `PICO-IMX6 (i.MX6 Solo/Duallite/Quad)`
| | pico-imx6ul | Compatible with TechNexion + `PICO-IMX6UL/PICO-IMX6ULL (i.MX6 UltraLite/ULL)`

`DISTRO` The following is a list of valid options for `DISTRO`.

:::info
The X11 and Framebuffer distros are only supported for i.MX 6 and i.MX 7. i.MX 8 should use xwayland only.
XWayland is the default distro for all i.MX families
:::

| Parameter | Available options | Description
|---|---|---
| `DISTRO` | fsl-imx-xwayland | Wayland graphics and X11. X11 applications using EGL are not supported
| | fsl-imx-wayland | Wayland weston graphics
| | fsl-imx-fb | Frame Buffer graphics - no X11 or Wayland. Frame Buffer is not supported on i.MX8.

The following is a list of valid options for other parameters based on the hardware.
| Parameter | Available Options | Description
|---|---|---
| `BASEBOARD` | pi, nymph, dwarf, hobbit | Compatible with TechNexion PICO-IMX6/PICO-IMX7 (i.MX6 Solo/DL/Quad/UL/ULL)(i.MX7 Dual).
| | fairy, tc0700, tc1000 |Compatible with TechNexion EDM-IMX6 (i.MX6 Solo/DL/Quad/Quad Plus).
| | pi-8m | Compatible with TechNexion PICO-IMX8MM (i.MX8M Mini).
| | wizard |Compatible with TechNexion PICO-IMX8MM (i.MX8M Mini), EDM-G-IMX8MP, EDM-G-IMX8MM
| | wb | Compatible with TechNexion EDM-G-IMX8MP/EDM-G-IMX8MM/EDM-G-IMX8MN (i.MX8M Plus)(i.MX8M Mini).
| `WIFI_MODULE`  | `nxp`, `qca`, `ath-pci` | Choose what kind of WLAN is on board. This specifies the 'wifi_module' variable in uEnv.txt
| `WIFI_FIRMWARE` | `y`, `all` | 'y' option is dependent on the 'WIFI_MODULE' setting.<br/> If you specify 'WIFI_MODULE' as 'qca', then only the 'qca' firmware package will be added to the Yocto build. <br/> 'all' option will add the 'nxp', 'qca', and 'ath-pci' firmware packages to the Yocto build. <br/> Please refer to section "Prepare WIFI/BT firmware" to ensure you already put firmware files in the right place.

The following is a list of valid options for the `DISPLAY` parameter.
:::note
1. The "DISPLAY" parameter only works on i.MX6/i.MX8M
2. It specifies the 'displayinfo' variable in uEnv.txt
:::

| Parameter | Available Options | Description
|---|---|---
|`DISPLAY` | lvds7 | On i.MX6, this specifies the 7 inch 1024x600 LVDS panel
| |lvds10 | On i.MX6, this specifies the 10 inch 1280x800 LVDS panel
| |lvds15   |On i.MX6, 15 inch 1366x768 LVDS panel
| |hdmi1080p |On i.MX6,  1920x1080 HDMI output
| | lcd |On i.MX6,  5 inch/7 inch 800x480 TTL parallel LCD panel
| |lvds7_hdmi720p | On i.MX6,  Dual display output to both 7 inch LVDS and HDMI
|| custom |On i.MX6, Reserved for custom panels
|| mipi5 | On i.MX8M, MIPI-DSI 5 inch panel(with ILI9881 controller)
||hdmi |On i.MX8M, HDMI monitor (the resolution is decided by EDID)

#### `-b` : Specify the build directory name

| Usage:| |
|---|---
|   -b `<build dir>`  |  Assign the name of build directory

#### Build OS image for TechNexion target platform

:::warning
Do not add the argument 'WIFI_FIRMWARE=y' if you have not placed the firmware files in "sources/meta-tn-imx-bsp/recipes-kernel/linux-firmware/files". It would result in build failure.
Please refer to section "Prepare WIFI/BT firmware". 
:::

#### Choosing Yocto target image

The following is a list of target image options:

|Available Options | Description
|---|---
|imx-image-core |core image with basic graphics and no multimedia
|imx-image-multimedia |image with multimedia and graphics
|imx-image-full |image with multimedia and machine learning and Qt

### Example BSP Setup and Build Commands for Supported Targets
Build Yocto for TechNexion target platform.

#### For AXON-IMX93
*Yocto Xwayland image*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=axon-imx93 source tn-setup-release.sh -b build-xwayland-axon-imx93
bitbake imx-image-full
```

#### For EDM-IMX93
*Yocto Xwayland image*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-imx93 source tn-setup-release.sh -b build-xwayland-edm-imx93
bitbake imx-image-full
```

#### For PICO-IMX93
*Yocto Xwayland image*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=pico-imx93 source tn-setup-release.sh -b build-xwayland-pico-imx93
bitbake imx-image-full
```

#### For AXON-IMX91
*Yocto Xwayland image*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=axon-imx91 source tn-setup-release.sh -b build-xwayland-axon-imx91
bitbake imx-image-full
```

#### For EDM-IMX91
*Yocto Xwayland image*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-imx91 source tn-setup-release.sh -b build-xwayland-edm-imx91
bitbake imx-image-full
```

#### For EDM-G-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-g-imx8mm source tn-setup-release.sh -b build-xwayland-edm-g-imx8mm
bitbake imx-image-full
```

#### For EDM-G-IMX8MP
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-g-imx8mp source tn-setup-release.sh -b build-xwayland-edm-g-imx8mp
bitbake imx-image-full
```

#### For PICO-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=pico-imx8mm source tn-setup-release.sh -b build-xwayland-pico-imx8mm
bitbake imx-image-full
```

:::info
**DISTRO: DISTRO can be replaced to "fsl-imx-wayland"**
:::

### For NXP EVK with TechNexion TEVS and VLS camera support
#### IMX8MP LPDDR4 EVK +
*Yocto Xwayland image*
```bash
DISTRO=fsl-imx-xwayland MACHINE=imx8mp-lpddr4-evk source tn-setup-release.sh -b build-xwayland-imx8mp-lpddr4-evk
bitbake imx-image-full
```

#### IMX8MP DDR4 EVK +
*Yocto Xwayland image*
```bash
DISTRO=fsl-imx-xwayland MACHINE=imx8mp-ddr4-evk source tn-setup-release.sh -b build-xwayland-imx8mp-ddr4-evk
bitbake imx-image-full
```

#### IMX93 11x11 LPDDR4X EVK +
*Yocto Xwayland image*
```bash
DISTRO=fsl-imx-xwayland MACHINE=imx93-11x11-lpddr4x-evk source tn-setup-release.sh -b build-xwayland-imx93-11x11-lpddr4x-evk
bitbake imx-image-full
```

#### IMX95 19x19 LPDDR5 EVK +
*Yocto Xwayland image*
```bash
DISTRO=fsl-imx-xwayland MACHINE=imx95-19x19-lpddr5-evk source tn-setup-release.sh -b build-xwayland-imx95-19x19-lpddr5-evk
bitbake imx-image-full
```

#### For PICO-IMX7
*PI baseboard, QT5 with X11 image for 7 inch/5 inch TTL-LCD panel:*

For PICO-IMX7 with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=pico-imx7 BASEBOARD=pi source tn-setup-release.sh -b build-x11-pico-imx7
bitbake imx-image-full
```

#### For EDM-IMX6
*FAIRY baseboard, QT5 with X11 image for 7 inch LVDS panel:*
For EDM-IMX6 with QCA WLAN:
```bash
DISPLAY=lvds7 WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=edm-imx6 BASEBOARD=fairy source tn-setup-release.sh -b build-x11-edm-imx6
bitbake imx-image-full
```
*TC-0700/TC0710 baseboard, QT5 with X11 image for 7 inch LVDS panel:*
```bash
DISPLAY=lvds7 WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=edm-imx6 BASEBOARD=tc0700 source tn-setup-release.sh -b build-x11-edm-imx6
bitbake imx-image-full
```
####  For PICO-IMX6
*NYMPH baseboard, Yocto wayland image for HDMI output:*
For PICO-IMX6 with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=pico-imx6 BASEBOARD=nymph source tn-setup-release.sh -b build-x11-pico-imx6
bitbake imx-image-full
```
#### For PICO-IMX6UL/PICO-IMX6ULL
*PI baseboard, QT5 with X11 image for 5" and 7" TFT-LCD output:*
For PICO-IMX6UL/ULL with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=pico-imx6ul BASEBOARD=pi source tn-setup-release.sh -b build-x11-pico-imx6ul
bitbake imx-image-full
```

### Adding Support for the Chromium Browser

You may add support for Chromium in the file `conf/local.conf`. This file is generated during BSP setup and initialization, so it is only available to edit after running the steps above.

* For XWayland or Wayland, add Chromium into your image
```
CORE_IMAGE_EXTRA_INSTALL += "chromium-ozone-wayland rng-tools"
```

### QTWebkit
WARNING: QtWebengine will only work on *SoC with GPU graphics hardware* on *i.MX 6*, *i.MX 7* and *i.MX 8*.

There are four Qt 6 browsers available. QtWebEngine browsers can be found in:
```bash
 /usr/share/qt6/examples/webenginewidgets/StyleSheetbrowser
 /usr/share/qt6/examples/webenginewidgets/Simplebrowser
 /usr/share/qt6/examples/webenginewidgets/Cookiebrowser
 /usr/share/qt6/examples/webengine/quicknanobrowser
```

To include Qtwebengine in the image put the following in *local.conf* or in the image recipe.
```bash
IMAGE_INSTALL_append = "packagegroup-qt6-webengine"
```

All browsers can be run by going to the directory above and running the executable found there.
* Touchscreen can be enabled by adding the parameters `-plugin evdevtouch:/dev/input/event0` to the executable.
```bash
./quicknanobrowser -plugin evdevtouch:/dev/input/event0
```

### Qt 6
WARNING: Qt is not supported on *i.MX 6UltraLite* and *i.MX 7Dual*. It works on X11 backend only but is not a supported feature.

* Note that Qt has both a commercial and open source license options.
* Make the decision about which license to use before starting work on custom Qt applications.
* Once custom Qt applications are started with an open source Qt license the work can not be used with a commercial Qt license.
* Work with a legal representative to understand the differences between each license.

### NXP eIQ machine learning
The meta-ml layer is the integration of NXP eIQ machine learning, which was formerly released as a separate meta-imx-machinelearning layer and is now integrated into the standard BSP image (imx-image-full).

Note that many of the features require Qt 5. In case of using other configuration than `imx-image-full`, put the following in local.conf:
```
IMAGE_INSTALL_append = "packagegroup-imx-ml"
```

### Systemd
Systemd support is enabled as default but it can be disabled by commenting out the systemd settings in `imx/meta-sdk/conf/distro/include/fsl-imx-preferred-env.inc`.

### Image Deployment
When build completes, the generated release image is under ```${BUILD-TYPE}/tmp/deploy/images/${MACHINE}”```:
For example, for PICO-IMX8MM:
```bash
build-desktop-pico-imx8mm/tmp/deploy/images/pico-imx8mm
```

To decompress the .bz2:
```bash
bzip2 -fdk fsl-image-XXX.rootfs.wic.bz2 "fsl-image-XXX.rootfs.wic"
```
To deploy the image to your board, please follow the instructions for loading software into the flash or microSD card of your development kit.
Use `bmaptool` to flash imx-image-full-XXX.rootfs.wic.bz2 directly.
Example: flash image of PICO-IMX8MM to /dev/sdj
```bash
bmaptool copy --bmap imx-image-full-pico-imx8mm.rootfs.wic.bmap imx-image-full-pico-imx8mm.rootfs.wic.bz2 /dev/sdj
```

Use `bmaptool` with `--nobmap` to flash imx-image-full-XXX.rootfs.wic.bz2 directly.
NOTE: **This will take a long time to flash image.**
```bash
bmaptool copy --nobmap imx-image-full-pico-imx8mm.rootfs.wic.bz2 /dev/sdj
```

#### For i.mx6/i.mx6ul/i.mx7

Please refer to the link below to flash the image into eMMC on the target board:

[https://download.technexion.com/development_resources/development_tools/installer/](https://download.technexion.com/development_resources/development_tools/installer/)
```bash
pico-imx6-imx6ul-imx7_otg-installer_xxx.zip
<platform>_generic-installer_xxx.zip
```
#### For i.MX6UL/i.MX6ULL/i.MX6DL/i.MX6Q/i.MX7D/i.MX8MM/i.MX8MQ/i.MX8MP image deploy by "UUU"**
Please refer to the link as below to get more detail information:

[https://developer.technexion.com/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc](https://developer.technexion.com/docs/embedded-software/linux/usage-guides/loading-software/using-uuu-to-flash-emmc)

### Login target
#### Yocto
* Default user
** account: root
** password: `no password`

To change the default account or password, modify the APTGET_ADD_USERS variable in `<build_dir>/conf/local.conf`.
NOTE: **The password must be generated by openssl v1.x**