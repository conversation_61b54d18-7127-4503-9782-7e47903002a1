---
title: Building Your First Image
description: Build your first image with <PERSON><PERSON><PERSON> for TechNexion products using our metadata and setup scripts
sidebar_position: 1
---

## Introduction
This article provides step-by-step instructions for building a Linux image with TechNexion's Yocto BSP, which supports the i.MX series of processors.

### System Requirements
Building an image from source with Yocto requires a host with the following:
* 8GB RAM (more is better)
* 4 Core processor
* 200 GB storage

If less memory is used, then some additional swap space may be needed. Inadequate memory may result slow builds and random build errors.

In addition, the host must be connected to a network and have access to the Internet so that all source code and tools may be downloaded.

#### Set up build environment on host PC:

The build environment is tested under Ubuntu 18.04 and Ubuntu 20.04.

#### Install required packages:
```bash
# First update the host package list
sudo apt-get update

# Install necessary packages
sudo apt-get install gawk wget git git-core diffstat unzip texinfo gcc-multilib build-essential \
chrpath socat cpio python python3 python3-pip python3-pexpect \
python3-git python3-jinja2 libegl1-mesa pylint3 rsync bc bison \
xz-utils debianutils iputils-ping libsdl1.2-dev xterm \
language-pack-en coreutils texi2html file docbook-utils \
python-pysqlite2 help2man desktop-file-utils \
libgl1-mesa-dev libglu1-mesa-dev mercurial autoconf automake \
groff curl lzop asciidoc u-boot-tools libreoffice-writer \
sshpass ssh-askpass zip xz-utils kpartx vim screen
```
### Install the Repo Tool
There are many respositories required. Google's `repo` tool is used to manage this.
```bash
mkdir ~/bin
curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
chmod a+x ~/bin/repo
```
### Download the BSP source
```bash
PATH=${PATH}:~/bin
mkdir edm_yocto
cd edm_yocto
```
Next, initialize the repositories based on the release you wish to build:

| Release | Command |
|---|---
|Yocto Zeus 3.0 TN2021Q4 | `repo init -u https://github.com/TechNexion/tn-imx-yocto-manifest.git -b zeus_5.4.y-stable -m imx-5.4.70-2.3.0_2021Q4.xml`
|Yocto Zeus 3.0 TN2021Q1 | `repo init -u https://github.com/TechNexion/tn-imx-yocto-manifest.git -b zeus_5.4.y-stable -m imx-5.4.70-2.3.0_2021Q1.xml`

:::warning
The content of this article pertains to Yocto 3.0 (Zeus) and we intend to keep this current for future revisions.

For earlier revisions, please review the README in the project manifest here:
[https://github.com/TechNexion/tn-imx-yocto-manifest/#readme](https://github.com/TechNexion/tn-imx-yocto-manifest/#readme)

Use the dropdown menu to select the target branch.
![select-yocto-version-github-dropdown.png](//img/select-yocto-version-github-dropdown.png)
:::

### Fetch the source code:
```bash
repo sync -j8
```
### Create build environment
There are several options regarding the build environment that you can use.

#### Prepare WIFI/BT firmware

You may optionally choose to add WIFI/BT firmware to the build. To do  this, add the argument "WIFI_FIRMWARE=y" in during BSP setup. Please refer to following for an explanation of other BSP initialization options.

#### Configurations for setup script

`MACHINE` is the target of build. It usually corresponds to the name of SOM or SBC.
For more information, please check the file under `“sources/meta-tn-imx-bsp/conf/machine”`.

`DISTRO` identifies the Yocto distribution and is the new way to configure for any backends.

`“-b”` specify the build directory.

`DISPLAY` is the disply type. This option only works on i.MX6 (i.MX6 Solo/Dual Lite/Dual/Quad) SOMs. Does not work for products based on i.MX6UL/ULL, i.MX6SX, and i.MX7.

`BASEBOARD` is the baseboard type.

`WIFI_MODULE` is to choose what kind of WLAN is on board. (qca/ath-pci)

`WIFI_FIRMWARE` is to choose to add WLAN firmware files in target rootfs or not. (y/all)

#### Build configurations for supported hardware

The following is a list of valid options for `MACHINE` based on the hardware.
| MACHINE | Description
|---|---
|edm-g-imx8mm |Compatible with TechNexion EDM-G-IMX8MM (i.MX8M Mini)
|edm-g-imx8mn | Compatible with TechNexion EDM-G-IMX8MN (i.MX8M Nano)
|edm-g-imx8mp |Compatible with TechNexion EDM-G-IMX8MP (i.MX8M Plus)
|edm-imx8mq |Compatible with TechNexion EDM-IMX8M (i.MX8M Quad)
|pico-imx8mm |Compatible with TechNexion PICO-IMX8MM (i.MX8M Mini)
|pico-imx8mq |Compatible with TechNexion PICO-IMX8M (i.MX8M Quad)
|flex-imx8mm |Compatible with TechNexion FLEX-IMX8MM (i.MX8M Mini)
|pico-imx7 |Compatible with TechNexion PICO-IMX7 (i.MX7 Dual)
|tep1-imx7 |Compatible with TechNexion TEP-0500-IMX7/TEP-0700-IMX7
|edm-imx6 |Compatible with TechNexion EDM-IMX6 (i.MX6 Solo/Duallite/Quad/QuadPlus)
|wandboard-imx6 |Compatible with TechNexion WANDBOARD-IMX6 (i.MX6 Solo/Duallite/Quad/QuadPlus)
|pico-imx6 |Compatible with TechNexion PICO-IMX6 (i.MX6 Solo/Duallite/Quad)
|pico-imx6ul |Compatible with TechNexion PICO-IMX6UL/PICO-IMX6ULL (i.MX6 UltraLite/ULL)
|tek-imx6 |Compatible with TechNexion TEK3-IMX6/TEP-1010-IMX6/TEP-1560-IMX6/TWP-1010-IMX6/TWP-1560-IMX6 (i.MX6 Solo/DL/Dual/Quad/Quad Plus)

The following is a list of valid options for `DISTRO`.
:::info
The X11 and Framebuffer distros are only supported for i.MX 6 and i.MX 7. i.MX 8 should use xwayland only.

XWayland is the default distro for all i.MX families
:::
| `DISTRO` | Description
|---|---
|fsl-imx-x11 |Only X11 graphics. X11 graphics are not supported on i.MX8.
|fsl-imx-wayland |Wayland weston graphics
|fsl-imx-xwayland |Wayland graphics and X11. X11 applications using EGL are not supported
|fsl-imx-fb |Frame Buffer graphics - no X11 or Wayland. Frame Buffer is not supported on i.MX8.

The following is a list of valid options for `BASEBOARD` based on the hardware. The `BASEBOARD` parameter specifies the 'baseboard' variable in uEnv.txt.
|`BASEBOARD` | Description
|---|---
|pi, nymph, dwarf, hobbit | Compatible with TechNexion PICO-IMX6/PICO-IMX7 (i.MX6 Solo/DL/Quad/UL/ULL)(i.MX7 Dual).
|fairy, tc0700, tc1000 |Compatible with TechNexion EDM-IMX6 (i.MX6 Solo/DL/Quad/Quad Plus).
|pi |Compatible with TechNexion PICO-IMX8MQ/PICO-IMX8MM/FLEX-IMX8MM (i.MX8M Quad)(i.MX8M Mini).
|wizard |Compatible with TechNexion EDM-IMX8MQ/PICO-IMX8MQ/PICO-IMX8MM/FLEX-IMX8MM (i.MX8M Quad)(i.MX8M Mini).
|wb |Compatible with TechNexion EDM-G-IMX8MP/EDM-G-IMX8MM/EDM-G-IMX8MN (i.MX8M Plus)(i.MX8M Mini)(i.MX8M Nano).

#### `WIFI_MODULE`
The following is a list of valid options for `WIFI_MODULE`. This specifies the 'wifi_module' variable in uEnv.txt).

|`WIFI_MODULE` | Description
|---|---
|'qca', 'ath-pci' | Choose what kind of WLAN is on board.

#### `WIFI_FIRMWARE`
The following is a list of valid options for `WIFI_FIRMWARE`.
|`WIFI_FIRMWARE` | Description
|---|---
|'y' |'y' option depends on 'WIFI_MODULE'. If you specify 'WIFI_MODULE' as 'qca'. Then, it only add 'qca' firmware package in yocto build.
|'all' | 'all' option will add both 'qca' and 'ath-pci' firmware package in yocto build.

Please refer to section "Prepare WIFI/BT firmware" to ensure you already put firmware files in the right place.

#### `DISPLAY`
The following is a list of valid options for `DISPLAY`. This parameter only works on i.MX6 and i.MX8M. This specifies the `displayinfo` variable in uEnv.txt.

|`DISPLAY` | Description
|---|---
|lvds7 | On i.MX6, this specifies the 7 inch 1024x600 LVDS panel
|lvds10 | On i.MX6, this specifies the 10 inch 1280x800 LVDS panel
|lvds15   |On i.MX6, 15 inch 1366x768 LVDS panel
|hdmi720p | On i.MX6, 1280x720 HDMI output
|hdmi1080p |On i.MX6,  1920x1080 HDMI output
|lcd |On i.MX6,  5 inch/7 inch 800x480 TTL parallel LCD panel
|lvds7_hdmi720p | On i.MX6,  Dual display output to both 7 inch LVDS and HDMI
|custom |On i.MX6, Reserved for custom panels
|mipi5 | On i.MX8M, MIPI-DSI 5 inch panel(with ILI9881 controller)
|hdmi |On i.MX8M, HDMI monitor (the resolution is decided by EDID)

#### `-b` : Specify the build directory name

| Usage:| |
|---|---
|   -b `<build dir>`  |  Assign the name of build directory

#### Choosing Yocto target image

The following is a list of target image options:

|Image name |Target
|---|---
|core-image-minimal |A small image that only allows a device to boot
|core-image-base |A console-only image that fully supports the target device hardware
|core-image-sato |An image with Sato, a mobile environment and visual style for mobile devices. The image supports X11 with a Sato theme, Pimlico applications. It contains a terminal, an editor and a file manager
|imx-image-core |An i.MX image with i.MX test applications to be used for Wayland backends
|fsl-image-machine-test |An FSL Community i.MX core image with console environment - no GUI interface
|imx-image-multimedia |Builds an i.MX image with a GUI without any Qt content
|tn-image-multimedia-full |Builds an i.MX image with test tools and a GUI without any Qt content
|imx-image-full |Builds an opensource Qt 5 image with Machine Learning features. These images are only supported for i.MX SoC with hardware graphics. They are not supported on the i.MX 6UltraLite, i.MX 6UltraLiteLite, i.MX 6SLL, and i.MX 7Dual.

### Example BSP Setup and Build Commands for Supported Targets
Build Yocto for TechNexion target platform.

#### For EDM-G-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-g-imx8mm source tn-setup-release.sh -b build-xwayland-edm-g-imx8mm

bitbake imx-image-full
```
#### For EDM-G-IMX8MN
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-g-imx8mn source tn-setup-release.sh -b build-xwayland-edm-g-imx8mn

bitbake fimx-image-full
```

#### For EDM-G-IMX8MP
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-g-imx8mp source tn-setup-release.sh -b build-xwayland-edm-g-imx8mp

bitbake imx-image-full
```

#### For EDM-IMX8MQ
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=edm-imx8mq source tn-setup-release.sh -b build-xwayland-edm-imx8mq

bitbake imx-image-full
```

#### For PICO-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=pico-imx8mm source tn-setup-release.sh -b build-xwayland-pico-imx8mm

bitbake imx-image-full
```

#### For PICO-IMX8MQ
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=pico-imx8mq source tn-setup-release.sh -b build-xwayland-pico-imx8mq

bitbake imx-image-full
```
#### For FLEX-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=fsl-imx-xwayland MACHINE=flex-imx8mm source tn-setup-release.sh -b build-xwayland-flex-imx8mm

bitbake imx-image-full
```

:::info
**DISTRO: DISTRO can be replaced to "fsl-imx-wayland"**
:::
#### For PICO-IMX7
*PI baseboard, QT5 with X11 image for 7 inch/5 inch TTL-LCD panel:*

For PICO-IMX7 with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=pico-imx7 BASEBOARD=pi source tn-setup-release.sh -b build-x11-pico-imx7

bitbake imx-image-full
```

#### For TEP1-IMX7
*PI baseboard, QT5 with X11 image for 7 inch/5 inch TTL-LCD panel:*
For TEP1-IMX7 with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=ath-pci DISTRO=fsl-imx-x11 MACHINE=tep1-imx7 source tn-setup-release.sh -b build-x11-pico-imx7

bitbake imx-image-full
```
#### For EDM-IMX6
*FAIRY baseboard, QT5 with X11 image for 7 inch LVDS panel:*
For EDM-IMX6 with QCA WLAN:
```bash
DISPLAY=lvds7 WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=edm-imx6 BASEBOARD=fairy source tn-setup-release.sh -b build-x11-edm-imx6

bitbake imx-image-full
```
*TC-0700/TC0710 baseboard, QT5 with X11 image for 7 inch LVDS panel:*
```bash
DISPLAY=lvds7 WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=edm-imx6 BASEBOARD=tc0700 source tn-setup-release.sh -b build-x11-edm-imx6

bitbake imx-image-full
```

#### For WANDBOARD-IMX6
*QT5 with X11 image for HDMI:*
For WANDBOARD-IMX6 with QCA WLAN:
```bash
DISPLAY=hdmi WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=wandboard-imx6 source tn-setup-release.sh -b build-x11-wandboard-imx6

bitbake imx-image-full
```

####  For PICO-IMX6
*PI baseboard, QT5 with X11 image for HDMI output:*
For PICO-IMX6 with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=pico-imx6 BASEBOARD=pi source tn-setup-release.sh -b build-x11-pico-imx6

bitbake imx-image-full
```
#### For PICO-IMX6UL/PICO-IMX6ULL
*PI baseboard, QT5 with X11 image for 5" and 7" TFT-LCD output:*
For PICO-IMX6UL/ULL with QCA WLAN:
```bash
WIFI_FIRMWARE=y WIFI_MODULE=qca DISTRO=fsl-imx-x11 MACHINE=pico-imx6ul BASEBOARD=pi source tn-setup-release.sh -b build-x11-pico-imx6ul

bitbake imx-image-full
```

### Adding Support for the Chromium Browser

You may add support for Chromium in the file `conf/local.conf`. This file is generated during BSP setup and initialization, so it is only available to edit after running the steps above.

* For X11 on MX6 with GPU, add Chromium into your image
```
CORE_IMAGE_EXTRA_INSTALL += "chromium-x11 rng-tools"
```
* For XWayland or Wayland, add Chromium into your image
```
CORE_IMAGE_EXTRA_INSTALL += "chromium-ozone-wayland rng-tools"
```

### QTWebkit
There are four Qt 5 browsers available. QtWebEngine browsers can be found in:
```
 /usr/share/qt5/examples/webenginewidgets/StyleSheetbrowser
 /usr/share/qt5/examples/webenginewidgets/Simplebrowser
 /usr/share/qt5/examples/webenginewidgets/Cookiebrowser
 /usr/share/qt5/examples/webengine/quicknanobrowser
```
All three browsers can be run by going to the directory above and running the executable found there. Touchscreen can be enabled by adding the parameters `-plugin evdevtouch:/dev/input/event0` to the executable.

`./quicknanobrowser -plugin evdevtouch:/dev/input/event0`

QtWebengine will only work on SoC with GPU graphics hardware on i.MX 6, i.MX 7 and i.MX 8. To include Qtwebengine in the image put the following in local.conf or in the image recipe.
```
IMAGE_INSTALL_append = "packagegroup-qt5-webengine"
```

### Qt 5
Note that Qt has both a commercial and open source license options.  Make the decision about which license to use before starting work on custom Qt applications.  Once custom Qt applications are started with an open source Qt license the work can not be used with a commercial Qt license.  Work with a legal representative to understand the differences between each license.

:::info
Qt is not supported on i.MX 6UltraLite and i.MX 7Dual. It works on X11 backend only but is not a supported feature.
:::
### NXP eIQ machine learning
The meta-ml layer is the integration of NXP eIQ machine learning, which was formerly released as a separate meta-imx-machinelearning layer and is now integrated into the standard BSP image (imx-image-full).

Note that many of the features require Qt 5. In case of using other configuration than imx-image-full, put the following in local.conf:
```
IMAGE_INSTALL_append = "packagegroup-imx-ml"
```

### Systemd
Systemd support is enabled as default but it can be disabled by commenting out the systemd settings in imx/meta-sdk/conf/distro/include/fsl-imx-preferred-env.inc.

### Image Deployment
When build completes, the generated release image is under ```${BUILD-TYPE}/tmp/deploy/images/${MACHINE}”```:

To decompress the .bz2:
```basg
bzip2 -fdk fsl-image-XXX.rootfs.wic.bz2 "fsl-image-XXX.rootfs.wic"
```
or use `bmaptool` to flash fsl-image-XXX.rootfs.wic.bz2 directly.

To deploy the image to your board, please follow the instructions for loading software into the flash or microSD card of your development kit.

## Tutorial Video

We have put together a video showing this process, including download the image, into a PICO-IMX8M-MINI.

[![Building an Embedded Linux Distribution with Yocto Project on TechNexion Hardware](https://img.youtube.com/vi/AVkyVh9jhoA/0.jpg)](https://www.youtube.com/watch?v=AVkyVh9jhoA)