---
title: Yocto
description: Yocto Overview
sidebar_position: 1
---

<!-- Note that images are rendered in native size by default if the client window
     is large enough.  However, this looks pretty bad on some images with higher
     resolution.  You can use JSX styling to make them smaller.-->
<img src="/img/Yocto-project.png" alt="Yocto Project Logo" style={{width:200}}/>

# Open Source Customizable Linux Distributions

The Yocto Project (YP) is an open source collaboration project that helps developers create custom Linux-based systems regardless of the hardware architecture. The project provides a flexible set of tools and a space where embedded developers worldwide can share technologies, software stacks, configurations, and best practices that can be used to create tailored Linux images for embedded and IOT devices, or anywhere a customized Linux OS is needed.

TechNexion maintains Linux support in Yocto for all of our products. The current version of Yocto we support is Scarthgap (Yocto 5.0).

## Getting Started

If you are new to using Yocto, or are an experienced Yocto developer, you may be interested in taking a look at our Yocto Development Guide.

## TechNexion Yocto Project Roadmap

The following table is our current Yocto project roadmap.

[Insert table here with Yocto roadmap.  Note that pretty HTML tables, with embedded colors, require a lot of VSX styles]

## Development versus Stable Branches

At TechNexion, we utilize a common practice where we maintain publicly accessible *development branches and stable branches*.

### Development Branches (-next)

We push the latest commits into development branches (branch name suffix: "**-next**"). These changes have not yet been fully tested, and developers should exercise caution when utilizing them.

### Stable Branches (-stable)

Periodically, usually 2-4 times per year, we will issue a new release. As part of that release, commits in the development branches are shifted to the "stable" branches (suffix: "-stable").

For an example of this, you can check our Yocto Project git repository:

![Screenshot-of-yocto-stable-and-development-branches-scarthgap](/img/stable-vs-development-branches-scarthgap.png)

### Latest Stable Release

The latest stable release is Yocto 5.0 (Scarthgap), released in Q2, 2025.

[yocto-latest-stable](release-notes/release-notes-yp50-2025q2)

View all Yocto Project release notes:

[Link to all Yocto release notes when they are uploaded]
