---
title: Create Custom Bootable system.img
description: Create Custom bootable system.img
sidebar_position: 7
---
## Demo image (system.img)

There are two step to do:

1. Prepare files
2. create system.img

### 1. Prepare **files**

- kernel Image
- kernel modules
- kernel dts
- pinmux
- change boot config for custom device-tree(overlay)

> #### kernel Image
>
Copy from:
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/kernel-5.10/arch/arm64/boot/Image
```
to here
```
<nvidia_folder>/Linux_for_Tegra/kernel/
```
<br/>

> #### kernel modules
>
Copy from
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/modules/lib/
```
to here
```
<nvidia_folder>/Linux_for_Tegra/rootfs/
```
<br/>

> #### rootfs files
>
Copy anything you want to deploy to image to here
```
<nvidia_folder>/Linux_for_Tegra/rootfs/
```
<br/>

> #### device-tree
>
Copy from
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/kernel-5.10/arch/arm64/boot/dts/<target>.dtb
```
to here
```
<nvidia_folder>/Linux_for_Tegra/rootfs/boot/<target>.dtb
```

<br/>

> #### pinmux (**TEK-ORIN series for example**)
>
Copy
```
Orin-tek-orin-a1-gpio-default.dtsi
```
to here
```
<nvidia_folder>/Linux_for_Tegra/bootloader/
```
Copy
```
Orin-tek-orin-a1-pinmux.dtsi
```
to here
```
<nvidia_folder>/Linux_for_Tegra/bootloader/t186ref/BCT/
```
:::info For Orin-EVK pinmux
Remember, ORIN-EVK series has different pinmux file name.<br/>
Please rename it.<br/>
`<SOM>-<board_name >-gpio-default.dtsi`<br/>
-> `tegra234-mb1-bct-gpio-p3767-dp-a03.dtsi`<br/>
`<SOM>-<board_name >-pinmux.dtsi`<br/>
-> `tegra234-mb1-bct-pinmux-p3767-dp-a03.dtsi`
:::

> #### boot config for custom device-tree
>
```
<nvidia_folder>/Linux_for_Tegra/rootfs/boot/extlinux/extlinux.conf
```
```
LABEL primary
      MENU LABEL primary kernel
      LINUX /boot/Image
      INITRD /boot/initrd
+     FDT /boot/<target>.dtb
-     APPEND ${cbootargs} quiet
+     APPEND ${cbootargs}
```

### 2-1. create `system.img`
Follow Flash Step section: [Create new system.img, DO NOT flash](/docs/embedded-software/linux/nvidia-jetpack/usage-guides/r3531-kernel-510/flash-step#create-new-systemimg-do-not-flash).

### 2-2. Create raw `system.img` can be dd into storage
Follow Flash Step section: [Flash demo image ](/docs/embedded-software/linux/nvidia-jetpack/usage-guides/r3531-kernel-510/flash-step#flash-demo-image).
