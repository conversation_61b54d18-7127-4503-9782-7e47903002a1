---
title: Compile Step
description: Compile Step
sidebar_position: 5
---
The tool chain and compile script is prepared by download script.<br/>
You can use it directly.
> ## The gccv7 tool chain
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/gcc_tool_chain
```
<br/>
> ## Compile kernel
```bash
$ cd <nvidia_folder>/Linux_for_Tegra/sources/kernel/kernel-5.10/
$ ./compile_kernel.sh
```
### kernel image location
```
arch/arm64/boot/Image
```
### device-tree location
```
arch/arm64/boot/dts/
```
```bash
# For TEK6100-ORIN-NX
tegra234-p3767-0000-tek-orin-a1.dtb

# For TEK6070-ORIN-NX
tegra234-p3767-0001-tek-orin-a1.dtb

# For TEK6040-ORIN-NANO
tegra234-p3767-0003-tek-orin-a1.dtb

# For TEK6020-ORIN-<PERSON><PERSON><PERSON>
tegra234-p3767-0004-tek-orin-a1.dtb

# For RPI22 and TEVI with ORIN-NANO-EVK
tegra234-p3767-0003-p3768-0000-a0-tevi.dtb

# For RPI22 and TEVS with ORIN-NANO-EVK
tegra234-p3767-0003-p3768-0000-a0-tevs.dtb

# For VLS3-ORIN-EVK with ORIN-NANO-EVK
tegra234-p3767-0003-p3768-0000-a0-vl316-vls.dtb
```
### kernel modules location
```
../modules/lib/
```