---
title: Flash Step
description: Flash Step
sidebar_position: 6
---
## Enter Recovery mode
When it comes to **using 'flash.sh'**, make sure you **enter the Recovery mode** first.
1. **Connect** to computer via **M-USB1**.
2. Plug the **storage** you want to flash system image.
3. Press **'Recovery**' button and '**Reset**' button **at the same time**.
4. Release '**Reset**' button.
5. Relesee '**Recovery**' button.
6. Check wether the device is connected.
```bash
$ lsusb
Bus 001 Device 012: ID 0955:7e19 NVIDIA Corp. APX
```

* * *
## Flash command
#### Please flash using Nvidia script for ORIN series
```bash
$ cd <nvidia_folder>/Linux_for_Tegra/
$ ls ./tools/kernel_flash/l4t_initrd_flash.sh
./tools/kernel_flash/l4t_initrd_flash.sh
```

Base on your modules, select the `<board name>` for script.

| Product name | `<board name>` | Boot device |
| --- | --- | --- |
| TEK6100-ORIN-NX(Micron dram) | `tn-tek6100-orin` | NVMe/ USB |
| TEK6100-ORIN-NX-HYNIX(Hynix dram) | `tn-tek6100-orin-hynix` | NVMe/ USB |
| TEK6070-ORIN-NX | `tn-tek6070-orin` | NVMe/ USB |
| TEK6040-ORIN-NANO | `tn-tek6040-orin` | NVMe/ USB |
| TEK6020-ORIN-NANO | `tn-tek6020-orin` | NVMe/ USB |
| VLS3-ORIN-EVK-VLS3 | `tn-vls3-orin-evk-tevs` | SD |
| RPI22-TEVS | `tn-tev-rpi22-tevs` | SD |
| RPI22-TEVI | `tn-tev-rpi22-tevi` | SD |

Base on your condition, select the `<storage name>` for script.

| Boot device| `<storage name>` |
| --- | --- |
| NVMe | `nvme0n1p1` |
| USB | `sda1` |
| SD | `mmcblk1p1` |

:::warning USB boot
When using USB boot, make sure there is **only one** USB storage connected to device.
In each boot stage, USB storage number(/dev/sdX) is not the same.
Leads to boot failed.
:::
:::warning Flash failed for TEK6100-ORIN-NX
TEK6100-ORIN-NX has two DRAM sku, Micron and Hynix.<br/>
Hynix dram need another tweak, [please re-download](/docs/embedded-software/linux/nvidia-jetpack/usage-guides/r3531-kernel-510/host-environment-setting) `TEV-Jetson_Jetpack_script` for it !!<br/>
( If you prepare script after 2025-02-15, you are up to date!)

---
You can check your sku using this command:
```
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh -p "-c bootloader/t186ref/cfg/flash_t234_qspi.xml --no-systemimg" --showlogs --no-flash --check-only --network usb0 jetson-orin-nano-devkit-nvme internal
```
examples:
```
=========================

TEK6100-ORIN-NX (Orin-NX 16GB) Micron dram

=========================
```
```
=========================

TEK6100-ORIN-NX (Orin-NX 16GB) Hynix dram

=========================
```
And you must re-create the image, follow [Create new system.img, and flash](#create-new-systemimg-and-flash)  topic.
:::

> ### Flash demo image
 1. * Download TechNexion TEK-ORIN products demo image from [HERE](https://download.technexion.com/demo_software/TEK/)
![image(63).png](//img/image%2863%29.png)
    * Download Jetson ORIN NANO EVK demo image from [HERE](https://download.technexion.com/demo_software/EVK/NVIDIA/OrinNano/)
![image(64).png](//img/image%2864%29.png)

    Make sure you download the image with the boot device you need.
 2. Unzip the file
 3. Connect the storage to computer and flash to block device
```bash
$ lsblk
$ sudo bmaptool copy <FILE.bz2> /dev/<BLOCK_DEVICE>
```
:::info NVMe storage
You can use something like 'SSD Enclosure Adapter' to transform SSD to USB block device.
:::
3. Plug the storage back to device and boot.
 :::info Multiple storage connected
Make sure you select the right boot device in NVIDIA UEFI stage.
:::
<br/>
> ### DO NOT create new `system.img`,  flash only
You can follow section: [Create new system.img, DO NOT flash](#create-new-systemimg-do-not-flash) to create system.img.
:::info You are ready
When you prepare Jetpack, download script will create `system.img`,<br/>
you can flash into device directly.
:::

```bash
# --flash-only
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device <storage name> -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/t186ref/cfg/flash_t234_qspi.xml" \
        --showlogs --flash-only --network usb0 <board name> internal
```

:::warning `--flash-only` limitation
Jetpack will storage the command that you previous create system image<br/>
in `bootloader/flashcmd.txt`<br/>
ex1: TEK6100-ORIN-NX: NVMe boot
```bash
./tegraflash.py ...
--bins bpmp_fw bpmp_t234-TE990M-A1_prod_sigheader.bin.encrypt;
bpmp_fw_dtb tegra234-bpmp-3767-0000-a02-3509-a02_with_odm_sigheader.dtb.encrypt;
kernel_dtb tegra234-p3767-0000-tek-orin-a1.dtb"
--ramcode 0
```
ex2: TEK6040-ORIN-NANO: NVMe boot
```bash
./tegraflash.py ...
--bins bpmp_fw bpmp_t234-TE950M-A1_prod_sigheader.bin.encrypt;
bpmp_fw_dtb tegra234-bpmp-3767-0003-3509-a02_with_odm_sigheader.dtb.encrypt;
kernel_dtb tegra234-p3767-0003-tek-orin-a1.dtb"
--ramcode 2
```
Most of the differences can be found in their board conf file.
<br/>
In conclusion, when running `--flash-only` command, if the `<storage name>` or `<board name>` is not match the previous created one, flash script will be terminated.

When any of them changes, need to create `system.img` again.<br/>
Or you can just create new Jetpack with different system image command.
:::

<br/>
> ### Create new `system.img`, and flash
```bash
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device <storage name> -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/t186ref/cfg/flash_t234_qspi.xml" \
        --showlogs --network usb0 <board name> internal
```
<br/>
> ### Create new system.img, DO NOT flash
```bash
# --no-flash
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device <storage name> -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/t186ref/cfg/flash_t234_qspi.xml" \
        --showlogs --no-flash --network usb0 <board name> internal
```
You can flash to device follow by section:[DO NOT create new system.img, flash only](#do-not-create-new-systemimg--flash-only).

<br/>
> ### Backup and restore currect device image
Download [backup/ restore script](https://download.technexion.com/development_resources/NVIDIA/backup_restore/) of Jetpack R35.4.1.<br/>
Use it to replace the files in `Linux_for_Tegra/tools/backup_restore/` .

### backup
```bash
# -b: jetson-orin-nano-devkit for EVK
#     jetson-orin-nano-devkit-nvme for TEK-ORIN series
# -e: mmcblk1p1 for ORIN-EVK
#     nvme0n1 for TEK-ORIN series
$ sudo ./tools/backup_restore/l4t_backup_restore.sh -e <storage name> -b <board name>
```

### restore
```bash
# -b: jetson-orin-nano-devkit for EVK
#     jetson-orin-nano-devkit-nvme for TEK-ORIN series
# -e: mmcblk1p1 for ORIN-EVK
#     nvme0n1 for TEK-ORIN series
$ sudo ./tools/backup_restore/l4t_backup_restore.sh -e <storage name> -r <board name>
```
:::info **restore failed**
When you restore image to device, script will check if the `<board name>` and `<storage name>`, if is not match, flash script will be terminated.

Also, script will be terminated If the storage size is *slightly smaller* than backup image.

This might happend if you want to ***copy*** the backup image to other device, since every NVMe/ SD size will not always be the same.
:::

> ### Create raw disk image (can be dd into storage)
Download script has the tweaked disk image creator ready.
```bash
$ ls tools/jetson-disk-image-creator.sh
tools/jetson-disk-image-creator.sh
```

```bash
# -o: output file name
# -b: board conf name
#       TEK6100-ORIN-NX/ TEK6040-ORIN-NANO ...
# -d: Boot device name
#       NVMe/ USB/ SD
$ sudo ./tools/jetson-disk-image-creator.sh \
        -o <image_name>.img -b <board name> -d <Boot device>
```
ex1: TEK6100-ORIN-NX with NVMe boot
```bash
$ sudo ./tools/jetson-disk-image-creator.sh \
    -o TEK6020-ORIN-NANO_20240504_NVMe_disk.img \
    -b tn-tek6020-orin-nano -d NVMe
```

ex2: VLS3-ORIN-EVK with SD boot
```bash
$ sudo ./tools/jetson-disk-image-creator.sh \
    -o VLS3-ORIN-EVK-VLS3_20240504_SD_disk.img \
    -b tn-vls3-orin-evk-vls3 -d SD
```
Now you can deploy the image into you device follow section: [Flash demo image ](#flash-demo-image).

<br/>
> ### Flash new pinmux image
#### 0) Edit the xlsm pinmux file via  windows environment.
```
<nvidia_folder>/Linux_for_Tegra/sources/TEK-ORIN_Orin-Nano_pinmux/Jetson_Orin_NX_series_and_Orin_Nano_series_Pinmux_Config_Template.xlsm
```
:::info ORIN-EVK
Download xlsm pinmux file from [NVIDIA server](https://www.google.com/url?sa=t&source=web&rct=j&opi=89978449&url=https://developer.nvidia.com/downloads/jetson-orin-nx-and-orin-nano-series-pinmux-config-template&ved=2ahUKEwil7-Px4viFAxUfm68BHWNaAm8QFnoECBIQAQ&usg=AOvVaw02aSTn-2sD_TSLWAEuhtaO).
:::
![pinmux1.png](//img/pinmux1.png)

Use the scroll bar right to select the pinmux of the cell(pin).
:::info watch out the sheet
You should work on `+HDMI A03` sheet, there are slightly different between them.
:::
If done, **click the above Macro**(Generate DT file) to generate pinmux file.

![image(67).png](//img/image%2867%29.png)

#### 1) Type the \<name> twice.
**tek-orin-a1** for TEK-ORIN series
![image(68).png](//img/image%2868%29.png)

You will see this result.
![image(71).png](//img/image%2871%29.png)

#### 2) Copy generated dtsi to `bootloader/` folder
```bash
$ sudo cp -rp Orin-tek-orin-a1-gpio-default.dtsi Linux_for_Tegra/bootloader/
$ sudo cp -rp Orin-tek-orin-a1-pinmux.dtsi Linux_for_Tegra/bootloader/t186ref/BCT/
```
#### 4) Update NVIDIA QSPI for pinmux
follow the 'Create and flash new QSPI image only'.
<br/>
> ### Create and flash new QSPI image only
~~Just remove `--external-device` option~~
```bash
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        -p "-c bootloader/t186ref/cfg/flash_t234_qspi.xml" \
        --showlogs --network usb0 <board name> internal
```
*this also support the `--no-flash` and `--flash-only` option*
:::info This will update the 'create image' command stored in Jetpack
Which means you can only use
```bash
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        -p "-c bootloader/t186ref/cfg/flash_t234_qspi.xml" \
        --showlogs --flash-only --network usb0 <board name> internal
```
Until next create image cammand.
:::

## Those only update by file system
:::info Protect the boot flow for saving your time.
You may want to see [this article](/docs/embedded-software/linux/nvidia-jetpack/usage-guides/r3531-kernel-510/some-debug-tips-before-you-start) before you changing files.
:::
Those part below can't be changed by flash.sh.
Need to change the file by:
1. ssh (scp)
2. USB stick
<br/>

> ### Update new kernel Image
Copy from:
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/kernel-5.10/arch/arm64/boot/Image
```
to device:
```
/boot/
```
<br/>
> ### Update new kernel modules
Copy from
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/modules/lib/
```
to device:
```
/lib/
```
<br/>
> ### Update device tree
From:
```
<nvidia_folder>/Linux_for_Tegra/sources/kernel/kernel-5.10/arch/arm64/boot/dts/<target>.dtb
```
to device:
```
/boot/<target>.dtb
```
#### Change default device-tree
On device:
```bash
$ vi /boot/extlinux/extlinux.conf
LABEL primary
      MENU LABEL primary kernel
+     FDT /boot/<target>.dtb
      LINUX /boot/Image
      INITRD /boot/initrd
      APPEND ${cbootargs} root=/dev/mmcblk0p1 rw rootwait rootfstype=ext4 console=ttyTCU0,115200n8 console=tty0 fbcon=map:0 net.ifnames=0
```