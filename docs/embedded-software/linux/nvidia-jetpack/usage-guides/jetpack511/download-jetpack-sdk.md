---
title: Download Jetpack SDK
description: Download Jetpack SDK
sidebar_position: 8
---
This article will guide you to install JetPack SDK and check the installation.

### install JetPack SDK
```
$ sudo apt update
$ sudo apt install nvidia-jetpack
$ apt depends nvidia-jetpack | awk '{print $2}' | xargs -I {} sudo apt install -y {}
```
#### ( option ) update Jetpack to newer version
```
$ sudo apt dist-upgrade
$ sudo apt install --fix-broken -o Dpkg::Options::="--force-overwrite"
```
### install jetson-stats to manage package version
```
$ sudo apt-get install python3-pip
$ sudo -H pip install -U jetson-stats
```
### config cuDNN env
```
$ echo ' ' >> ~/.bashrc
$ echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda/lib64' >> ~/.bashrc
$ echo 'export PATH=$PATH:/usr/local/cuda/bin' >> ~/.bashrc
$ echo 'export CUDA_HOME=$CUDA_HOME:/usr/local/cuda' >> ~/.bashrc

$ source ~/.bashrc

$ sudo apt-get install zlib1g libfreeimage3 libfreeimage-dev
```
:::warning restart docker service
We have installed the `nvidia-container`, need to restart the docker  service to import the config
```
$ systemctl restart docker
```
:::

* * *
## test/ check the package
### check Jetpack version
```
$ sudo apt show nvidia-jetpack
```
![image(82).png](//img/image%2882%29.png)

### check jetson_release
```
$ sudo jetson_release
```
![image(83).png](//img/image%2883%29.png)

### test cuDNN
```
$ nvcc -V
```
![image(84).png](//img/image%2884%29.png)

### Building a cuDNN Dependent Program
```
$ sudo cp -rv /usr/src/cudnn_samples_v8 ~/
$ cd ~/cudnn_samples_v8/mnistCUDNN/
$ sudo chmod 777 ~/cudnn_samples_v8/
$ sudo make clean && sudo make
$ ./mnistCUDNN
```
![image(86).png](//img/image%2886%29.png)

* * *
## prepare jetson-containers
```
$ git clone https://github.com/dusty-nv/jetson-containers
```
:::caution patch for `jetson-containers`
Lots of the AI demo need `jetson-containers` to prepare environment.
But currently, the containers can't support the MIPI camera.
Please download the [patch](https://download.technexion.com/development_resources/NVIDIA/0001-add-v4l-subdev-vi-capture-channel-media-devices.patch), and run the below command.
```
$ cd jetson-containers
$ git apply 0001-add-v4l-subdev-vi-capture-channel-media-devices.patch
$ cd ../
```
:::
```
$ bash jetson-containers/install.sh
```
* * *
## Reference:
[How to Install JetPack](https://docs.nvidia.com/jetson/archives/jetpack-archived/jetpack-461/install-jetpack/index.html)<br/>
[jetson-stats](https://rnext.it/jetson_stats/)<br/>
[cuDNN](https://docs.nvidia.com/deeplearning/cudnn/archives/cudnn-891/install-guide/index.html)<br/>
[FreeImage is not set up correctly. Please ensure FreeImae is set up correctly](https://forums.developer.nvidia.com/t/freeimage-is-not-set-up-correctly-please-ensure-freeimae-is-set-up-correctly/66950/19?page=2)