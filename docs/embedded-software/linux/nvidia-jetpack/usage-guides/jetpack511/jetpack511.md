---
title: JetPack 5.1.1
description: NVIDIA JetPack Usage Guides
sidebar_position: 10
---
**NVIDIA JetPack Usage Guides for Jetpack 5.1.1**

Software Version Table:
<div className="md-table">
|JetPack 5.1.1||
|---|---|
|Jetson Linux|R35.3.1|
|Kernel|5.10.104|
|Ubuntu|20.04|
|UEFI firmware|3.1-32827747|
|**TechNexion release**||
|Branch name|tn_l4t-r35.3.1.ga_kernel-5.10|
|Tag|r35.3.ga|
</div>


<br/>
**Guideline link:**
- [Demo image Guide](demo-image-guideoop)
- Refer [Host Environment Setting](host-environment-setting) for setup your compilable computer or virtual machine.
- [Workspace Guide](workspace-guide) describes the codebase structure.
- [Some Debug Tips](some-debug-tips-before-you-start) for your customized booting.
- Refer to [Compile step](compile-step) for partial builds.
- Create images
  - [Create system images](flash-step#create-new-systemimg-do-not-flash), that is used for flashing on recovery mode.
  - [Create disk image](flash-step#create-raw-disk-image-can-be-dd-into-storage), that is used for flashing into external storage.
  - [Create QSPI image](flash-step#create-and-flash-new-qspi-image-only), that is used for updating UEFI firmware.
- How to update software
  - When you want to compile custom software or don't want to unplug boot storage, [flash with compiled images on recovery mode](flash-step#do-not-create-new-systemimg-flash-only)
  - When you have no compilation requirements and can plug out boot storage, [flash with TechNexion demo image on external storage](flash-step#flash-demo-image)
- [Download Jetpack SDK](download-jetpack-sdk) to get more features from NVIDIA.