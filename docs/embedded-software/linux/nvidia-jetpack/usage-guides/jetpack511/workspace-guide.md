---
title: Workspace Guide
description: Workspace Guide
sidebar_position: 3
---
Make sure you download the workspace mentioned in previous page.

## Workspace map
Here are some major folders that you will visit often.
```bash
$ cd Linux_for_Tegra
$ tree
.
    # download binary for Jetpack
├── apply_binaries.sh
├── bootloader
        # sparse file for system image
│   ├── system.img
        # raw file system image
│   ├── system.img.raw
    # top flash command
    # (Orin series use './tools/kernel_flash/l4t_initrd_flash.sh' to call this function)
├── flash.sh
    # kernel partition file, separated with /rootfs/boot/
├── kernel
│   ├── dtb
│   ├── dtc
│   ├── Image
    # rootfs files, you can put custom file or service to here to deploy them
├── rootfs
    # source code folder, need to manually update result file to jetpack.
    # (Described in Flash Step page)
├── sources
        # NVIDIA device-tree source code
│   ├── hardware
│   │   └── nvidia
│   │       ├── platform
│   │       │   ├── t23x
│   │       │   │   ├── p3768
                        # ORIN NANO series device-tree
│   │       │   │   └── kernel-dts
                                # TEK6100-ORIN-NX
│   │       │   │   │       ├── tegra234-p3767-0000-tek-orin-a1.dts
                                # TEK6070-ORIN-NX
│   │       │   │   │       ├── tegra234-p3767-0001-tek-orin-a1.dts
                                # ORIN EVK
│   │       │   │   │       ├── tegra234-p3767-0003-p3768-0000-a0.dts
                                # RPI22-TEVI
│   │       │   │   │       ├── tegra234-p3767-0003-p3768-0000-a0-tevi.dts
                                # RPI22-TEVS
│   │       │   │   │       ├── tegra234-p3767-0003-p3768-0000-a0-tevs.dts
                                # VLS3-ORIN-EVK-VLS3
│   │       │   │   │       ├── tegra234-p3767-0003-p3768-0000-a0-vl316-vls.dts
                                # TEK6040-ORIN-NANO
│   │       │   │   │       ├── tegra234-p3767-0003-tek-orin-a1.dts
                                # TEK6020-ORIN-NANO
│   │       │   │   │       └── tegra234-p3767-0004-tek-orin-a1.dts
...
│   ├── kernel
            # gcc tool chain for cross compile
│   │    ├── gcc_tool_chain
            # kernel source code with TechNexion modifies
│   │    ├── kernel-5.10
            # module .ko file
│   │    ├── modules
            # TechNexion camera source code
│   │    └── technexion
        # Pinmux backup files for TEK-ORIN series
│   └── TEK-ORIN_Orin-Nano_pinmux
    # script for sync soure code from NVIDIA server
├── source_sync.sh
    # ORIN NANO EVK board conf => SD card boot
├── jetson-orin-nano-devkit.conf -> p3768-0000+p3767-0000.conf
    # ORIN NANO EVK board conf => NVMe boot
├── jetson-orin-nano-devkit-nvme.conf -> p3768-0000+p3767-0000-nvme.conf
    # TEK6020-ORIN-NANO board conf => NVMe boot
├── tn-tek6020-orin.conf
    # TEK6040-ORIN-NANO board conf => NVMe boot
├── tn-tek6040-orin.conf
    # TEK6070-ORIN-NX board conf => NVMe boot
├── tn-tek6070-orin.conf
    # TEK6100-ORIN-NX board conf => NVMe boot
├── tn-tek6100-orin.conf
    # RPI22-TEVI board conf => SD boot
├── tn-tev-rpi22-tevi.conf
    # RPI22-TEVS board conf => SD boot
├── tn-tev-rpi22-tevs.conf
    # VLS3-ORIN-EVK-VLS3board conf => SD boot
├── tn-vls3-orin-evk-tevs.conf
    # tools for develop NVIDIA product
├── tools
        # backup/ restore the system image for device
│   ├── backup_restore
        # script for generate flash-able USB/ SD card image base on system.img.raw
│   ├── jetson-disk-image-creator.sh
        # ORIN series need to use this script to generate/ flash image.
│   ├── kernel_flash
        # create default user for ubuntu system image
│   ├── l4t_create_default_user.sh
        # prepare requirement packages for ubuntu environment
│   ├── l4t_flash_prerequisites.sh
...
```