---
title: Some Debug Tips Before You Start
description: Some debug tips before you start
sidebar_position: 4
---
## Add another boot config
#### We highly recommend you to add another boot config for it.
So when you make a mistake, you can boot using another boot config.<br/>
**(Re-creating system.img or flashing the entire system image takes a lot of time.)**<br/>
And rename your test file before you copy into target folder.
```bash
$ vi /boot/extlinux/extlinux.conf
```
```
LABEL primary
      MENU LABEL primary kernel
      LINUX /boot/Image
      INITRD /boot/initrd
      FDT /boot/<target>.dtb
      APPEND ${cbootargs} ...

+ LABEL TEST_KERNEL
+      MENU LABEL TEST kernel
+      LINUX /boot/Image_test
+      INITRD /boot/initrd
+      FDT /boot/<target>_test.dtb
+      APPEND ${cbootargs} ...
```

When booting to UEFI, follow the [step](https://forums.developer.nvidia.com/t/boot-problem-on-live-device/241449/8?u=joe.zhang) to change boot device.<br/>
Or you can **select the TEST_KERNEL boot** config by typing '2' in console.


## Boot from USB stick
Another way for safety boot is using USB stick boot.<br/>
Follow section: [Flash demo image](flash-step#flash-demo-image) to create new USB boot device, when boot to UEFI select the right boot device.
:::info Some trap
If you follow the section: [Create new `system.img`, and flash](flash-step#create-new-systemimg-and-flash) to create a USB boot device, make sure you modify the `/boot/extlinux/extlinux.conf`
```
- root=PARTUUID=ad43ec6f-6d79-4df0-b915-1c936b0e33ef
+ root=/dev/sda1
Otherwise, the OS might select the wrong rootfs device.
```
Go to [this website](https://forums.developer.nvidia.com/t/orin-nano-cant-change-select-uefi-boot-device/266221/9) for more detail.
:::

## Remove/ modify the default user and password
In download script, we add a default user ubuntu:ubuntu
```
# vim technexion_jetpack_download.sh #Line: 274
        # create default user and auto login
        sudo Linux_for_Tegra/tools/l4t_create_default_user.sh -u ubuntu -p ubuntu -a
# -a stands for auto login.
```
This user can't be removed, so remove/ modify this line *BEFORE* you run the download script.