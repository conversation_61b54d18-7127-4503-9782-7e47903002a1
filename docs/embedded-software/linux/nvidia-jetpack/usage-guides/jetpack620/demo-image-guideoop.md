---
title: Demo Image Guide(OOP)
description: Demo Image Guide(OOP)
sidebar_position: 1
---

![image(65).png](//img/image%2865%29.png)

## Login to device CUI/ GUI
:::warning (TEK6XX0-ORIN-NX/ TEK60X0-<PERSON><PERSON>-<PERSON><PERSON><PERSON> has pre-build user)
user(root): ubuntu<br/>
password: ubuntu

You can skip this step while using those product.
If you want to remove/ change the default user profile, please follow this section: [Remove/ modify the default user and password](some-debug-tips-before-you-start#remove-modify-the-default-user-and-password).
:::
When first booting, you will see this on CUI:
```
[   31.893157] Please complete system configuration setup on desktop to proceed...
```
You need to **[follow the step](https://www.linuxtechi.com/ubuntu-20-04-lts-installation-steps-screenshots/)** to creat system configuration **on GUI**.<br/>
(Need to prepare **USB keyboard/ mouse**, and **DP/ HDMI monitor**.)

And you can login GUI/ CUI with your account.

## Bring up Cameras
You can follow [this page](/docs/embedded-vision/tevs/usage-guides/nvidia/quick-start/technexion-camera-modules-for-jetpack-6x) to get full instruction of camera.
