---
title: Compile Stage
description: Compile Stage
sidebar_position: 5
---
The tool chain and compile script is prepared by download script.<br/>
You can use it directly.
## The gcc v11.3 tool chain
```
<nvidia_folder>/Linux_for_Tegra/source/kernel/gcc_tool_chain
```

## Compile kernel
```bash
$ cd <nvidia_folder>/Linux_for_Tegra/sources/
# build kernel and modules
$ ./nvbuild.sh
# install kernel and modules
$ ./nvbuild.sh -i
```
### kernel synced and output folder
```bash
kernel_out/
```

### kernel image location
```
kernel_out/arch/arm64/boot/Image
```

### device-tree location
```
kernel_out/hardware/nvidia/t23x/nv-public
```

```bash
# For TEK6100-ORIN-NX
nv-platform/tegra234-tek-orin+p3767-0000-nv.dts

# For TEK6070-ORIN-NX
nv-platform/tegra234-tek-orin+p3767-0001-nv.dts

# For TEK6040-ORIN-NANO
nv-platform/tegra234-tek-orin+p3767-0003-nv.dts

# For TEK6020-ORIN-NANO
nv-platform/tegra234-tek-orin+p3767-0004-nv.dts

```
### kernel modules location
```
../rootfs/lib/
```