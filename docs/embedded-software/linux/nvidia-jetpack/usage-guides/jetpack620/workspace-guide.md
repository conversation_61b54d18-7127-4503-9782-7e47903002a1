---
title: Workspace Guide
description: Workspace Guide
sidebar_position: 3
---
Make sure you download the workspace mentioned in previous page.

## Workspace map
Here are some major folders that you will visit often.
```bash
$ cd Linux_for_Tegra
$ tree
.
    # download binary for Jetpack
├── apply_binaries.sh
├── bootloader
        # sparse file for system image
│   ├── system.img
        # raw file system image
│   ├── system.img.raw
    # top flash command
    # (Orin series use './tools/kernel_flash/l4t_initrd_flash.sh' to call this function)
├── flash.sh
    # kernel partition file, separated with /rootfs/boot/
├── kernel
│   ├── dtb
│   ├── dtc
│   ├── fdtoverlay
│   ├── Image
    # rootfs files, you can put custom file or service to here to deploy them
├── rootfs
    # source code folder, need to manually update result file to jetpack.
    # (Described in Flash Step page)
├── source
        # NVIDIA device-tree source code
│   ├── hardware
│   │   └── nvidia
│   │       ├── t23x
│   │       │   └── nv-public
                        # Top ORIN NANO/NX series device-tree
│   │       │   │   ├── nv-platform
                                # ORIN NX EVK
│   │       │   │   │       ├── tegra234-p3768-0000+p3767-0000-nv.dts
                                # ORIN NANO EVK
│   │       │   │   │       ├── tegra234-p3768-0000+p3767-0003-nv.dts
                                # TEK6100-ORIN-NX
│   │       │   │   │       ├── tegra234-tek-orin+p3767-0000-nv.dts
                                # TEK6070-ORIN-NX
│   │       │   │   │       ├── tegra234-tek-orin+p3767-0001-nv.dts
                                # TEK6040-ORIN-NANO
│   │       │   │   │       ├── tegra234-tek-orin+p3767-0003-nv.dts
                                # TEK6020-ORIN-NANO
│   │       │   │   │       └── tegra234-tek-orin+p3767-0004-nv.dts
                        # overlay device tree
│   │       │       ├── overlay
                            # TechNexion camera overlay device tree
│   │       │       │   ├── tegra234-camera-tek-orin-vls3.dtsi
...
│   ├── kernel
            # gcc tool chain for cross compile
│   │    ├── gcc_tool_chain
            # kernel source code with TechNexion modifies
│   │    ├── kernel-jammy-src
        # total kernel synced source code and kernel out folder when compile
│   ├── kernel_out
        # NVIDIA out of tree driver
│   ├── nvidia-oot
│   │   ├── drivers
│   │   │   ├── media
│   │   │   │   ├── i2c
                        # TechNexion camera source code
│   │   │   │   │   ├── technexion
        # Pinmux backup files for TEK-ORIN series
│   └── TEK-ORIN_Orin-Nano_pinmux
        # script for compile kernel
│   ├── nvbuild.sh
        # script for sync soure code from NVIDIA server
│   ├── source_sync.sh
    # ORIN NANO EVK board conf => SD card boot
├── jetson-orin-nano-devkit.conf -> p3768-0000-p3767-0000-a0.conf
    # ORIN NANO EVK board conf => NVMe boot
├── jetson-orin-nano-devkit-nvme.conf -> p3768-0000-p3767-0000-a0-nvme.conf
    # TEK6020-ORIN-NANO board conf => NVMe boot
├── tn-tek6020-orin.conf
    # TEK6040-ORIN-NANO board conf => NVMe boot
├── tn-tek6040-orin.conf
    # TEK6070-ORIN-NX board conf => NVMe boot
├── tn-tek6070-orin.conf
    # TEK6100-ORIN-NX board conf => NVMe boot
├── tn-tek6100-orin.conf
    # tools for develop NVIDIA product
├── tools
        # backup/ restore the system image for device
│   ├── backup_restore
        # script for generate flash-able USB/ SD card image base on system.img.raw
│   ├── jetson-disk-image-creator.sh
        # ORIN series need to use this script to generate/ flash image.
│   ├── kernel_flash
        # create default user for ubuntu system image
│   ├── l4t_create_default_user.sh
        # prepare requirement packages for ubuntu environment
│   ├── l4t_flash_prerequisites.sh
        # create qspi image and package
│   ├── qspi_flash
...
```