---
title: Update UEFI Firmware
description: Update UEFI Firmware steps
sidebar_position: 8
---

When Jetpack version is changed, you also need to update UEFI firmware.<br/>
If UEFI firmware and system image are not matched, device can't boot up.

:::danger **BE CAREFUL!!**
When you upgrade UEFI firmware to Jetpack 6(R36.4.ga), you **CAN NOT** downgrade to Jetpack 5(R35.3.ga) quickly.<br/>
If you need to downgrade to Jetpack 5, please [download Jetpack 5 SDK](../jetpack511/host-environment-setting) then [compile and build QSPI image](../jetpack511/flash-step#create-and-flash-new-qspi-image-only).
:::

## Update from demo image
1. Download [demo image](flash-demo-image#demo-image) for your product, and unzip file.
2. <PERSON><PERSON> enter to [recovery mode](create-images-and-flash#enter-recovery-mode)
3. unzip `<Product>_hostflash.tbz2` and flash
        ```bash
        $ tar -xvf <hostflash.tbz2 file>
        $ sudo hostflash/host_flash_qspi.sh
        ``` 
The device will automatically reboot after flash completed, which takes about 6 minutes.
<br/>
