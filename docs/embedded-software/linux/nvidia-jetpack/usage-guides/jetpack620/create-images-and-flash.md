---
title: Create Images and Flash
description: Create Images for updating software
sidebar_position: 6
---

Before you begin, following are parameter descriptions: <br/>
Base on your modules, select the `<board-name>`.

| Product name | `<board-name>` | Boot device |
| :--- | --- | --- |
| TEK6100-ORIN-NX | `tn-tek6100-orin` | NVMe/ USB |
| TEK6040-ORIN-NANO | `tn-tek6040-orin` | NVMe/ USB |

Base on your condition, select the `<rootdev>`.

| Boot device| `<rootdev>` |
| --- | --- |
| NVMe | `nvme0n1p1` |
| USB | `sda1` |

<br/>
`<board-name>` : specifies the configuration of the target device. <br/>
`<rootdev>` : specifies the device on which the root file system is located. <br/>

---

## Enter Recovery Mode
When it comes to **using 'flash.sh'**, make sure you **enter the Recovery mode** first.
1. **Connect** to computer via **M-USB1**.
2. Plug the **storage** you want to flash system image.
3. Press **'Recovery**' button and '**Reset**' button **at the same time**.
4. Release '**Reset**' button.
5. Release '**Recovery**' button.
6. Check wether the device is connected.
        ```bash
        $ lsusb
        Bus 003 Device 026: ID 0955:7323 NVIDIA Corp. APX
        ```

## Prepare JetPack host environment
Please follow [host environment setting](host-environment-setting) to download codebase and compile images.
:::info You are ready
When you prepare JetPack, the download script will create `system.img`,<br/>
you can [flash into device](#do-not-create-new-systemimg-flash-only) directly.
:::

## Flash on Recovery Mode
### Flash options
```bash
$ cd <nvidia_folder>
$ ./technexion_jetpack_download.sh -b <board-name> <flash_option>
```
`<flash_option>` : support the following flash options
- `--flash-only` : same as [DO NOT create new system.img, flash only](#do-not-create-new-systemimg-flash-only)
- `--build-flash`: same as [Create new system.img, and flash](#create-new-systemimg-and-flash)
- `--no-flash`   : default value. Same as [Create new system.img, DO NOT flash](#create-new-systemimg-do-not-flash)

Example: build system.img and flash into TEK6100-ORIN-NX
```bash
$ ./technexion_jetpack_download.sh -b TEK6100-ORIN-NX --build-flash
```
---

**Flash using Nvidia script for ORIN series**<br/>
Run this command from the Linux_for_Tegra folder:
```base
$ cd <nvidia_folder>/Linux_for_Tegra/
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh <option> <board-name> <rootdev>
```
`<option>` : is one or more command line options for flash.sh script.<br/>

### DO NOT create new `system.img`, flash only
You can follow section: [Create new system.img, DO NOT flash](#create-new-systemimg-do-not-flash) to create system.img.

```bash
# use '--flash-only' option
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device <rootdev> -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/generic/cfg/flash_t234_qspi.xml" \
        --showlogs --flash-only --network usb0 <board-name> internal
```
Example: flash TEK6100-ORIN-NX with NVMe boot device.
```bash
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device nvme0n1p1  -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/generic/cfg/flash_t234_qspi.xml" \
        --showlogs --flash-only --network usb0 tn-tek6100-orin-nx internal
```

:::warning `--flash-only` limitation
Jetpack stores the command that you previously used to create system image<br/>
in `bootloader/flashcmd.txt`<br/>
Example1: TEK6100-ORIN-NX: NVMe boot
```bash
./tegraflash.py ...
bpmp_fw bpmp_t234-TE980M-A1_prod_sigheader.bin.encrypt;
bpmp_fw_dtb tegra234-bpmp-3767-0000-a02-3509-a02_with_odm_sigheader.dtb.encrypt;
kernel_dtb tegra234-tek-orin+p3767-0000-nv.dtb
...
```
Example2: TEK6040-ORIN-NANO: NVMe boot
```bash
./tegraflash.py ...
bpmp_fw bpmp_t234-TE950M-A1_prod_sigheader.bin.encrypt;
bpmp_fw_dtb tegra234-bpmp-3767-0001-3509-a02_with_odm_sigheader.dtb.encrypt;
kernel_dtb tegra234-tek-orin+p3767-0003-nv.dtb
...
```
Most of the differences can be found in their board conf file.
<br/>
In conclusion, when running `--flash-only` command, if the `<rootdev>` or `<board-name>` does not match the previously created one, flash script will be terminated.

When any of them changes, need to create `system.img` again.<br/>
Or you can just create new Jetpack with different system image command.
:::
<br/>


### Create new `system.img`, and flash
```bash
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device <rootdev> -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/generic/cfg/flash_t234_qspi.xml" \
        --showlogs --network usb0 <board-name> internal
```
<br/>

### Create new `system.img`, DO NOT flash
:::info You are ready
When you prepare Jetpack, download script will create `system.img`,<br/>
you can flash into device directly.
:::
```bash
# use `--no-flash` option
$ sudo ./tools/kernel_flash/l4t_initrd_flash.sh \
        --external-device <rootdev> -c tools/kernel_flash/flash_l4t_external.xml \
        -p "-c bootloader/generic/cfg/flash_t234_qspi.xml" \
        --showlogs --no-flash --network usb0 <board-name> internal
```
You can flash to device following section: [DO NOT create new system.img, flash only](#do-not-create-new-systemimg-flash-only).
<br/>

---
## Create disk image and flash
You also can create disk image for flashing into external storage.<br/>
1. Run below commmand to 
        ```bash
        $ cd <nvidia_folder>/Linux_for_Tegra/
        $ sudo ./tools/jetson-disk-image-creator.sh -o <output-img> -b <board-name> -d <rootdev>
        ```
   For example:<br/>
   `sudo ./tools/jetson-disk-image-creator.sh -o TEK6100_nvme.img -b tn-tek6100-orin-nx -d NVMe`<br/>
   This command will create a TEK6100_nvme.img. Could be `dd` into storage.

2. Generate bmaptool files
        ```bash
        $ sudo bmaptool create <output-img> -o <output-img>.bmap
        $ sudo bzip2 -z <output-img>
        ```
3. Connect the storage to computer and flash to block device
    ```bash
    # get your storage block device, it maybe shown sdX
    $ lsblk -d
    sdb      8:16   0 232.9G  0 disk

    # umount block device. Based on above example, <BLOCK_DEVICE> is sdb
    $ sudo umount /dev/<BLOCK_DEVICE>?

    # flash demo image into block device
    $ sudo bmaptool copy <output-img>.bz2 /dev/<BLOCK_DEVICE>
    ```

    :::info NVMe storage
    We recommend using 'SSD Enclosure Adapter' to transform NVMe SSD to USB block device.
    :::
4. Plug the storage back to device and boot.
    :::info Multiple storage connected
    Make sure you select the right boot device in NVIDIA UEFI stage.
    :::

---
## Create QSPI image
After Jetpack 6, we can use `generate_qspi_for_flash.sh` to create QSPI image.
:::danger **BE CAREFUL!!**
When you upgrade UEFI firmware to Jetpack 6(R36.4.ga), you **CAN NOT** downgrade to Jetpack 5(R35.3.ga) quickly.<br/>
If you need to downgrade to Jetpack 5, please [download Jetpack 5 SDK](../jetpack511/host-environment-setting) then [compile and build QSPI image](../jetpack511/flash-step#create-and-flash-new-qspi-image-only).
:::
<br/>

1. generate qspi package
        ```bash
        $ cd <nvidia_folder>/Linux_for_Tegra/
        $ sudo ./tools/qspi_flash/generate_qspi_for_flash.sh <board-name>
        ```
   The output file is host_tegraflash_\$\{VERSION\}.tbz2. <br/>
   \$\{VERSION\} is R36.4.3 for Jetpack 6.2.<br/>

2. Device enter to [recovery mode](#enter-recovery-mode)
3. unzip host_tegraflash_\$\{VERSION\}.tbz2 and flash
        ```bash
        $ tar -xvf host_tegraflash_${VERSION}.tbz2
        $ sudo hostflash/host_flash_qspi.sh
        ```
<br/>

---
## Flash new pinmux image
1. Edit the xlsm pinmux file via windows environment.
        TechNexion source file:
        ```
        <nvidia_folder>/Linux_for_Tegra/sources/TEK-ORIN_Orin-Nano_pinmux/Jetson_Orin_NX_and_Orin_Nano_series_Pinmux_Config_Template.xlsm
        ```

        :::info ORIN-EVK
        Download xlsm pinmux file from [NVIDIA server](https://developer.nvidia.com/downloads/jetson-orin-nx-and-orin-nano-series-pinmux-config-template).
        :::
        ![pinmux1.png](//img/pinmux1.png)

        Use the scroll bar right to select the pinmux of the cell(pin).
        :::info watch out the sheet
        - You should work on `HDMI` sheet, there are slightly different between them.
        - Please enable macro before you edit pinmux table.
        :::
        If done, **click the above Macro**(Generate DT file) to generate pinmux file.

        ![image(67).png](//img/image%2867%29.png)

2. Type the \<name> twice.
        **tek-orin-a1** for TEK-ORIN series
        ![image(68).png](//img/image%2868%29.png)

        You will see this result.
        ![image(71).png](//img/image%2871%29.png)
        There are 3 dtsi files generated.

3. Copy generated dtsi to `bootloader/` folder
        ```bash
        $ sudo cp -rp Orin-tek-orin-a1-gpio-default.dtsi Linux_for_Tegra/bootloader/
        $ sudo cp -rp Orin-tek-orin-a1-pinmux.dtsi Linux_for_Tegra/bootloader/generic/BCT/
        ```
4. Update NVIDIA QSPI for pinmux
        follow the [Create and flash new QSPI image only](#create-qspi-image).
<br/>

---
## Those only update by file system
:::info Protect the boot flow for saving your time.
You may want to see [this article](/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack511/some-debug-tips-before-you-start) before you changing files.
:::
Those part below can't be changed by flash.sh.
Need to change the file by:
1. ssh (scp)
2. USB stick
<br/>

> ### Update new kernel Image
Copy from:
```
<nvidia_folder>/Linux_for_Tegra/source/kernel_out/kernel/kernel-jammy-src/arch/arm64/boot/Image
```
to device:
```
/boot/
```
<br/>
> ### Update new kernel modules
Copy from
```
<nvidia_folder>/Linux_for_Tegra/rootfs/usr/lib/modules/
```
to device:
```
/lib/
```
<br/>
> ### Update device tree
From:
```
<nvidia_folder>/Linux_for_Tegra/source/kernel_out/kernel-devicetree/generic-dts/dtbs/<target>.dtb
```
to device:
```
/boot/<target>.dtb
```
#### Change default device-tree
On device:
```bash
$ vi /boot/extlinux/extlinux.conf
LABEL primary
      MENU LABEL primary kernel
+     FDT /boot/<target>.dtb
      LINUX /boot/Image
      INITRD /boot/initrd
      APPEND ${cbootargs} root=/dev/mmcblk0p1 rw rootwait rootfstype=ext4 console=ttyTCU0,115200n8 console=tty0 fbcon=map:0 net.ifnames=0
```
