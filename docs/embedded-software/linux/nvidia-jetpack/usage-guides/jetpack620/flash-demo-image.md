---
title: Flash Demo Image
description: Flash TechNexion Demo Image
sidebar_position: 7
---
:::danger
## Update UEFI firmware
When Jetpack version is changed, you also need to update UEFI firmware.<br/>
If UEFI firmware and system image are not matched, device can't boot up.<br/>
Please refer [Update UEFI firmware from demo image](update-uefi-fw#update-from-demo-image)
:::

## Flash demo image steps
1. Download TechNexion demo image:<br/>
   ### Demo image
    |Product|Demo image|
    |:--|:--|
    |TEK6100-ORIN-NX|NVMe [TEK6100-ORIN-NX_JP6_ubuntu-22.04_hdmi_NVMe_diskimg.zip](https://download.technexion.com/demo_software/TEK/Jetson-Orin-NX/TEK6100-ORIN-NX/DiskImage/TEK6100-ORIN-NX_JP6_ubuntu-22.04_hdmi_NVMe_diskimg.zip)<br/>USB [TEK6100-ORIN-NX_JP6_ubuntu-22.04_hdmi_USB_diskimg.zip](https://download.technexion.com/demo_software/TEK/Jetson-Orin-NX/TEK6100-ORIN-NX/DiskImage/TEK6100-ORIN-NX_JP6_ubuntu-22.04_hdmi_USB_diskimg.zip) |
    |TEK6040-ORIN-NANO|NVMe [TEK6040-ORIN-NANO_JP6_ubuntu-22.04_hdmi_NVMe_diskimg.zip](https://download.technexion.com/demo_software/TEK/Jetson-Orin-Nano/TEK6040-ORIN-NANO/DiskImage/TEK6040-ORIN-NANO_JP6_ubuntu-22.04_hdmi_NVMe_diskimg.zip)<br/>USB [TEK6040-ORIN-NANO_JP6_ubuntu-22.04_hdmi_USB_diskimg.zip](https://download.technexion.com/demo_software/TEK/Jetson-Orin-Nano/TEK6040-ORIN-NANO/DiskImage/TEK6040-ORIN-NANO_JP6_ubuntu-22.04_hdmi_USB_diskimg.zip)|
       
    Make sure you download the image with the boot device you need.
    :::tip DRAM sku on TEK6100-ORIN-NX
    TEK6100-ORIN-NX has two DRAM sku, Micron and Hynix.<br/>
    After JetPack6.1(R36.4.0), system can automatically detect dram and bring up successfully.<br/>
    :::

2. Unzip the zip file <br/>
    You will get 3 files likes below:
    - Product_storage_disk.**img.bz2**
    - Product_storage_disk.**img.bmap**
    - Product_**hostflash.tbz2**


3. There two methods to update demo image: <br/>
    - Flash demo image on [external storage](#flash-on-external-usb-device), you may need to plug out boot storage. <br/>
    - Flash demo image on [TEK device storage](#flash-on-tek-storage), you need prepare another USB storage. <br/>
<br/>

### Flash on external USB device
1. Connect the external storage to computer and flash to block device

    ```bash
    # get your storage block device, it maybe shown sdX
    $ lsblk -d
    sdb      8:16   0 232.9G  0 disk

    # umount block device. Based on above example, <BLOCK_DEVICE> is sdb
    $ sudo umount /dev/<BLOCK_DEVICE>?

    # flash demo image into block device
    $ sudo bmaptool copy <FILE.bz2> /dev/<BLOCK_DEVICE>
    ```

    :::info NVMe storage
    We recommend using 'SSD Enclosure Adapter' to transform NVMe SSD to USB block device.
    :::
 2. Plug the storage back to device and boot.
    :::info Multiple storage connected
    Make sure you select the right boot device in NVIDIA UEFI stage.
    :::
<br/>

### Flash on TEK storage
1. Follow [previous flash steps](#flash-on-external-usb-device) to prepare another bootable USB storage. <br/>
   Please make sure this bootable software version are matched currently UEFI firmware on device. <br/>

2. Plug USB to device <br/>
   Check block devices, you should find two bootable storage. <br/>
   For example, NVMe(nvme0n1) and USB(sda). <br/>
   ```
    $ lsblk -d
    NAME    MAJ:MIN RM   SIZE RO TYPE MOUNTPOINTS
    loop0     7:0    0    16M  1 loop
    sda       8:0    0 232.9G  0 disk
    zram0   252:0    0   1.9G  0 disk [SWAP]
    zram1   252:1    0   1.9G  0 disk [SWAP]
    zram2   252:2    0   1.9G  0 disk [SWAP]
    zram3   252:3    0   1.9G  0 disk [SWAP]
    nvme0n1 259:0    0 232.9G  0 disk
   ```

   Currently, boot from NVMe. <br/>
   ```
    $ df -h
    Filesystem       Size  Used Avail Use% Mounted on
    /dev/nvme0n1p1   228G  7.4G  211G   4% /
    /dev/nvme0n1p10   63M  110K   63M   1% /boot/efi
    /dev/sda1        227G   14G  203G   7% /media/ubuntu/d5b6430f-d69d-46ac-9c63-084f926e74dd
   ```
    <br/>

3. Reboot device and choice to USB as boot device. <br/>
   When NVIDIA log shown, press ESC to enter UEFI setup. <br/>
   Then enter 'Boot Manager', choice **USB** device. <br/>
   ![Nvidia-bootmng-usb.png](//img/NVIDIA-BootMng-USB.png)
   <br/>

4. Currently, you should boot from USB.
   ```
    $ df -h
    Filesystem      Size  Used Avail Use% Mounted on
    /dev/sda1       227G   14G  203G   7% /
    /dev/sda10       63M  110K   63M   1% /boot/efi
   ```

   Check the NVMe still could be recognized. <br/>
   ```
   $ lsblk /dev/nvme0n1
    nvme0n1      259:0    0 232.9G  0 disk
    ├─nvme0n1p1  259:1    0 231.4G  0 part
    ├─nvme0n1p2  259:2    0   128M  0 part
    ├─nvme0n1p3  259:3    0   768K  0 part
    ├─nvme0n1p4  259:4    0  31.6M  0 part
    ├─nvme0n1p5  259:5    0   128M  0 part
    ├─nvme0n1p6  259:6    0   768K  0 part
    ├─nvme0n1p7  259:7    0  31.6M  0 part
    ├─nvme0n1p8  259:8    0    80M  0 part
    ├─nvme0n1p9  259:9    0   512K  0 part
    ├─nvme0n1p10 259:10   0    64M  0 part
    ├─nvme0n1p11 259:11   0    80M  0 part
    ├─nvme0n1p12 259:12   0   512K  0 part
    ├─nvme0n1p13 259:13   0    64M  0 part
    └─nvme0n1p14 259:14   0 879.5M  0 part
   ```

5. On your HostPC, copy demo image files into TEK device. <br/>
   Using `scp ${file-on-HostPC} ubuntu@${IP}:/home/<USER>/>
   Please copy both bz2 and bmap file. <br/>
   For example: <br/>
    ```
    scp Product_storage_disk.img.bmap ubuntu@*******:/home/<USER>
    scp Product_storage_disk.img.bz2  ubuntu@*******:/home/<USER>
    ```

6. install bmaptool
    ```
    sudo add-apt-repository universe
    sudo apt update
    sudo apt install bmap-tools
    ```

7. Flash demo image on TEK storage.
   ```
   sudo bmaptool copy Product_storage_disk.img.bz2 /dev/nvme0n1
   ```
   Waiting for flash completely, and then reboot. <br/>

8. Reboot and choice **NVMe** as boot device.<br/>
   Follow step3 to enter boot manager.<br/>
   ![Nvidia-bootmng-nvme.png](//img/NVIDIA-BootMng-NVMe.png)
   <br/>


## Troubleshooting
1. After flash demo image, can't boot up.<br/>
   If you update Jetpack version, you also need to [update firmware](update-uefi-fw#update-from-demo-image). <br/>
   Find matched firmware version from [software version table](../../).<br/>

2. Is it inconvenient to select the boot device every time? <br/>
   You can [change boot order](some-debug-tips-before-you-start#change-boot-order) on UEFI. <br/>
   Or keep only one boot storage and remove others. <br/>
