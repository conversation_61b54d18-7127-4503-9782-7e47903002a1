---
title: JetPack 6.2.0
description: NVIDIA JetPack Usage Guides
sidebar_position: 9
---
**NVIDIA JetPack Usage Guides for Jetpack 6.2.0**

Software Version Table:
<div className="md-table">
|JetPack 6.2.0||
|---|---|
|Jetson Linux|R36.4.3|
|Kernel|5.15.148|
|Ubuntu|22.04|
|UEFI firmware|36.4.3-gcid-38968081|
|**TechNexion release**||
|Branch name|tn_l4t-r36.4.ga_kernel-5.15|
|Tag|r36.4.ga|
</div>


<br/>
**Guideline link:**
- [Demo image Guide](demo-image-guideoop)
- Refer [Host Environment Setting](host-environment-setting) for setup your compilable computer or virtual machine.
- [Workspace Guide](workspace-guide) describes the codebase structure.
- [Some Debug Tips](some-debug-tips-before-you-start) for your customized booting.
- Refer to [Compile stage](compile-stage) for partial builds
- Create images
  - [Create system images](create-images-and-flash), that is used for flash on recovery mode.
  - [Create disk image](create-images-and-flash#create-disk-image-and-flash), that is used for flashing into external storage.
  - [Create QSPI image](create-images-and-flash#create-qspi-image), that is used for updating UEFI firmware.
- How to update software
  - When you want to compile custom software, [flash with compiled images on recovery mode](create-images-and-flash#flash-on-recovery-mode).
  - When you have no compilation requirements, [flash with TechNexion demo image](flash-demo-image).
    - You can flash demo image on [external USB storage](flash-demo-image#flash-on-external-usb-device).
    - Also can flash demo image on [TEK storage](flash-demo-image#flash-on-tek-storage).
  - If your Jetpack version is changed, you also need to [update UEFI Firmware](update-uefi-fw).


```mermaid
graph TD
  %% judge node
  J1{Need customized <br/>software?}
  J2{Where SW <br/>flash in?}
  J3{Need package image?}
  J4{JatPack version<br/>change?}

  %% function node for flash
  F1(Use TechNexion<br/>Demo Image)
  F2(Flash into USB)
  F3(Flash into<br/>device storage)

  %% function node for compile
  F4(Prepare HostPC)
  F5(Enter recovery Mode)
  F6(Create system image)
  F7(Flash system image)
  F8(Create Disk image)
  F9(Create QSPI image<br/> and flash)

  %% UEFI FW
  U(Update UEFI FW);
  D(Done);

  %% link
  click F1 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/flash-demo-image#demo-image"
  click F2 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/flash-demo-image#flash-on-external-usb-device"
  click F3 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/flash-demo-image#flash-on-tek-storage"
  click F4 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/host-environment-setting"
  click F5 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/create-images-and-flash#enter-recovery-mode"
  click F6 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/create-images-and-flash#create-new-systemimg-do-not-flash"
  click F7 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/create-images-and-flash#do-not-create-new-systemimg-flash-only"
  click F8 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/create-images-and-flash#create-disk-image-and-flash"
  click F9 "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/create-images-and-flash#create-qspi-image"
  click U "https://developer.technexion.com/docs/embedded-software/linux/nvidia-jetpack/usage-guides/jetpack620/update-uefi-fw"

  subgraph "Preparation"
    F4
    F6
  end

  subgraph "System image update"
    F2
    F3
    F7
  end

  subgraph "UEFI update"
    F9
    U
  end

  %% flowchart
  J1 -- No --> F1 --> J2;
  J2 -- USB --> F2;
  J2 -- device storage --> F3;

  %% customize image
  J1 -- Yes --> Preparation;
  Preparation --> J3;
  J3 -- Yes --> F8 --> J2;
  J3 -- No --> F5 --> F7;
  F2 --> J4;
  F3 --> J4;
  F7 --> J4;

  %% UEFI FW update
  J4 -- Yes --> U;
  J4 -- No --> D;
  J4 -- Yes --> F9;
```
