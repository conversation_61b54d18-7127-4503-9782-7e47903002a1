---
title: Debian 12 images for TechNexion boards
description: Debian 12 images for TechNexion boards
---
## Debian 12 features with TechNexion flexbuild
![image.png](//img/image%28115%29.png)

 - [Build or install using flexbuild](https://developer.technexion.com/docs/how-to-build-and-deploy-debian-using-flexbuild)
 - Demo image is ready in [file server](https://download.technexion.com/demo_software/)

### What's new?

* Access wide range of application software using `apt-get `
* NXP-specific hardware-accelerated components for i.MX
* Easy to customize RootFS type(desktop/server)
* TechNexion cameras support
* TechNexion VizionViewer support

### VizionViewer with TEVS/VLS3
**Click the VizionViewer icon in Applications**
![Screenshot from 2025-01-16 08-55-32.png](//img/Screenshot%20from%202025-01-16%2008-55-32.png)

Or run `vizionviewer` in terminal
```
debian@imx8mp:~$ vizionviewer
```

![Screenshot from 2025-01-17 06-48-58.png](//img/Screenshot%20from%202025-01-17%2006-48-58.png)

### GPU
```
debian@imx8mp:~$ glmark2-es2-wayland
```
![Screenshot from 2025-01-16 08-48-12.png](//img/Screenshot%20from%202025-01-16%2008-48-12.png)

### Chromium Web Browser

Enjoy web browser with Hardware acceleration!
![Screenshot from 2025-01-16 08-54-25.png](//img/Screenshot%20from%202025-01-16%2008-54-25.png)

### NPU
=== EDM-G-IMX8MP ===
Follow **i.MX Machine Learning User's Guide => 7.1 Object detection pipeline example**
with TEVS.
```
debian@imx8mp:~$ wget https://github.com/google-coral/test_data/raw/master/ssd_mobilenet_v2_coco_quant_postprocess.tflite
debian@imx8mp:~$ wget https://github.com/google-coral/test_data/raw/master/coco_labels.txt
debian@imx8mp:~$ export MODEL=$(pwd)/ssd_mobilenet_v2_coco_quant_postprocess.tflite
debian@imx8mp:~$ export LABELS=$(pwd)/coco_labels.txt
```
```
# replace video2 to video<X> if needed.
debian@imx8mp:~$ gst-launch-1.0 --no-position v4l2src device=/dev/video2 ! \
        video/x-raw,width=640,height=480,framerate=60/1 ! \
        tee name=t t. ! queue max-size-buffers=2 leaky=2 ! \
        imxvideoconvert_g2d ! \
        video/x-raw,width=300,height=300,format=RGBA ! \
        videoconvert ! video/x-raw,format=RGB ! \
        tensor_converter ! \
        tensor_filter framework=tensorflow-lite model=${MODEL} \
        custom=Delegate:External,ExtDelegateLib:libvx_delegate.so ! \
        tensor_decoder mode=bounding_boxes option1=mobilenet-ssd-postprocess option2=${LABELS} \
        option3=0:1:2:3,50 option4=640:480 option5=300:300 ! \
        mix. t. ! queue max-size-buffers=2 ! \
        imxcompositor_g2d name=mix latency=30000000 min-upstream-latency=30000000 \
        sink_0::zorder=2 sink_1::zorder=1 ! waylandsink
```

![Screenshot from 2025-01-16 09-18-45.png](//img/Screenshot%20from%202025-01-16%2009-18-45.png)