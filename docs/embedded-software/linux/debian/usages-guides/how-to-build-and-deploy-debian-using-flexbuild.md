---
title: How to build and deplay Debian using flexbuild
description: How to build and deplay Debian using flexbuild
---
## Build Debian using flexbuild
[Github page](https://github.com/TechNexion/tn_debian_flexbuild) for tn_debian_flexbuild

### Build Environment

*  **Cross-build in Debian Docker container** hosted on x86 Ubuntu or any other distro for arm64 target
* **Cross-build on x86 host machine** running Debian 12 for arm64 target
* **Native-build on ARM board** running Debian for arm64 target


### Source Code Retrieval
```
$ git clone https://github.com/TechNexion/tn_debian_flexbuild.git
$ cd tn_debian_flexbuild
```
### Setting Up the Docker Build Environment
Host:
```
$ . setup.env
$ bld docker   (create or attach to docker)
```
in docker:
( check if the prompt is `[root@fbdebian tn_debian_flexbuild]`)
```
$ . setup.env
$ bld host-dep (install docker dependent packages)
```
### Select one of the following:
#### A. Automated Build of Debian 12 for Target Board
===EDM-IMX93===
```
$ bld -m imx93-edm
```
===EDM-G-IMX8MP===
```
$ bld -m imx8mp-edm-g
```
===EDM-G-IMX8MM===
```
$ bld -m imx8mm-edm-g
```
===AXON-IMX93===
```
$ bld -m imx93-axon
```
===AXON-IMX8MP===
```
$ bld -m imx8mp-axon
```
===PICO-IMX8MM===
```
$ bld -m imx8mm-pico
```

#### B. step by step build of Debian 12
:::info
Use `-p IMX` will build all platforms of the i.MX.
Swap `-p IMX` to `-m <machine>` to contain only that machine's components.
:::

**General RootFS**

1. download RootFS
2. build NXP-specific components(apps)
3. merge
4. pack RootFS
```
# create general rootfs with NXP-specific components all IMX platforms
$ bld rfs -p IMX               # create rootfs with debian desktop, for other rootfs: [-r debian:server | -r debian:base | -r poky:tiny ]
$ bld apps -p IMX              # create NXP-specific components(apps) for all IMX platforms, depends on rootfs created above
$ bld merge-apps -p IMX        # merge NXP-specific components(apps) to rootfs.
$ bld packrfs -p IMX           # pack rootfs to 'build_lsdk2412>/images/'
```
**Boot partition**
```
# create all boot image for all IMX platforms
$ bld boot -p IMX              # create boot partitions for all IMX pltforms to 'build_lsdk2412>/images/'
```
**Bootable firmware**
```
# create all firmware image for all IMX platforms
$ bld fwall -p IMX             # create all fw for all IMX platforms to 'build_lsdk2412>/images/'
```
#### C. using others build command:
Check in [Github page](https://github.com/TechNexion/tn_debian_flexbuild?tab=readme-ov-file#most-used-example-with-separate-command)
* * *
## Deploy (flex-installer) into device:
:::info
( `flex-installer` is in PATH when running `$ . setenv`.
If not, replace `flex-installer` with `./flex-installer` )
:::

Each debian image in flexbuild contain 3 files, all of them presents in `build_lsdk2412/images/` if you follow the build step above.
1. bootable firmware (`firmware_<machine>_sdboot.img`)
2. boot partition (`boot_IMX_arm64_<machine>_lts_6.6.52.tar.zst`)
3. General RootFS (`rootfs_lsdk2412_debian_desktop_arm64.tar.zst`)
:::info
For different flexbuild version, some of the name would changes.
Please adjust name by following rules:
* `2412` stands for flexbuild-v24.12.
* `boot_IMX_arm64` is the name of debian12.
* `6.6.52` is the kernel version.
* `desktop` is default distro(desktop | server | base)

Or follow the github page guide.
:::
### Go to build image folder
```
$ cd build_lsdk2412/images/
```
#### Flash directly to block device(/dev/sd):
Format block device
```
$ flex-installer -i pf -d /dev/sd<x>
```
== For EDM-IMX93 ==
```
$ flex-installer -m imx93-edm -d /dev/sd<X> -b boot_IMX_arm64_imx93-edm_lts_6.6.52 -f firmware_imx93-edm_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For EDM-G-IMX8MP ==
```
$ flex-installer -m imx8mp-edm-g -d /dev/sd<X> -b boot_IMX_arm64_imx8mp-edm-g_lts_6.6.52 -f firmware_imx8mp-edm-g_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For EDM-G-IMX8MM ==
```
$ flex-installer -m imx8mm-edm-g -d /dev/sd<X> -b boot_IMX_arm64_imx8mm-edm-g_lts_6.6.52 -f firmware_imx8mm-edm-g_sdboot.img -r rootfs_l2412_debian_desktop_arm64.tar.zst
```
== For AXON-IMX93 ==
```
$ flex-installer -m imx93-axon -d /dev/sd<X> -b boot_IMX_arm64_imx93-axon_lts_6.6.52 -f firmware_imx93-axon_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For AXON-IMX8MP ==
```
$ flex-installer -m imx8mp-axon -d /dev/sd<X> -b boot_IMX_arm64_imx8mp-axon_lts_6.6.52 -f firmware_imx8mp-axon_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For PICO-IMX8MM ==
```
$ flex-installer -m imx8mm-pico -d /dev/sd<X> -b boot_IMX_arm64_imx8mm-pico_lts_6.6.52 -f firmware_imx8mm-pico_sdboot.img -r rootfs_l2412_debian_desktop_arm64.tar.zst
```
#### Create sdcard image (8GB size image named sdcard.wic):
== For EDM-IMX93 ==
```
$ flex-installer -m imx93-edm -i mkwic -b boot_IMX_arm64_imx93-edm_lts_6.6.52 -f firmware_imx93-edm_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For EDM-G-IMX8MP ==
```
$ flex-installer -m imx8mp-edm-g -i mkwic -b boot_IMX_arm64_imx8mp-edm-g_lts_6.6.52 -f firmware_imx8mp-edm-g_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For EDM-G-IMX8MM ==
```
$ flex-installer -m imx8mm-edm-g -i mkwic -b boot_IMX_arm64_imx8mm-edm-g_lts_6.6.52 -f firmware_imx8mm-edm-g_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For AXON-IMX93 ==
```
$ flex-installer -m imx93-axon -i mkwic -b boot_IMX_arm64_imx93-axon_lts_6.6.52 -f firmware_imx93-axon_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For AXON-IMX8MP ==
```
$ flex-installer -m imx8mp-axon -i mkwic -b boot_IMX_arm64_imx8mp-axon_lts_6.6.52 -f firmware_imx8mp-axon_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```
== For PICO-IMX8MM ==
```
$ flex-installer -m imx8mm-pico -i mkwic -b boot_IMX_arm64_imx8mm-pico_lts_6.6.52 -f firmware_imx8mm-pico_sdboot.img -r rootfs_lsdk2412_debian_desktop_arm64.tar.zst
```

* * *
## Swap the uboot/ kernel
### Modify the config file:
```
$ vim configs/technexion.yml
```
Change the repository(`url:`) and version (`tag:` or `branch:` or `commit:`)
![image.png](//img/image%28111%29.png)

![image.png](//img/image%28112%29.png)

#### Delete the previous repository and build result if needed:

```
# linux
$ bld clean-linux -p IMX
$ rm -rf build_lsdk2412/images/boot_IMX_arm64_*
$ rm -rf components_lsdk2412/linux/linux/

# uboot
$ bld clean-bsp -p IMX
$ rm -rf build_lsdk2412/images/firmware_*
$ rm -rf components_lsdk2412/bsp/uboot/
```
#### modify the patches if needed
When you change the kernel/uboot, you might face the patch conflict issue.
You might need to adjust or remove the patches in these folder:
```
$ ls -la patch/linux/
$ ls -la patch/uboot/
```
* * *
## Trouble shooting
#### Custom board can't boot into uboot
Might facing the nameing check in `src/bsp/imx_mkimage.mk` cause generate `flash.bin`  not correctly.

#### Build failed for machine learning(ml) components
As for ml Makefile, NXP does not fully isolated the build environment.
Leads to build failed in some of the computers.

For now, disable the ml by applying this patch:
```
$ wget https://download.technexion.com/development_resources/NXP/debian/0001-flexbuild-disable-ml-component.patch
$ git am 0001-flexbuild-disable-ml-component.patch
```
:::info
Boot partition in debian12 is **EXT4** not **FAT32** like yocto.
If you want to add camera or display overlay, change command from:
```
$ fatls mmc X
```
to
```
$ ext4ls mmc X
```
:::
:::warning
For most use case, login using `debian` is enough.
`root` does not able to access the desktop session.
`debian` user has `root` permission, but some of the command still need to add `sudo` prefix.
:::