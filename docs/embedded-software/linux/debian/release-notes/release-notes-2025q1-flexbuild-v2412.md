---
title: <PERSON><PERSON> 12 (bookworm) 2025Q1 Release Notes
description: Release Notes for 2025Q1 flexbuild-v24.12
sidebar_position: 50
---
## Debian 12 (flexbuild v24.12) 2025Q1 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2024.04_6.6.52_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2024.04_6.6.52_2.2.0-stable) |153e8e48579c3f0c15a16a3d2649354868ba7912|
|Kernel| [tn-imx_6.6.52_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.6.52_2.2.0-stable)|c8aa74583f0a56a2237e047a07f9508d0be6fc1f|
|TN flexbuild BSP| [tn_v24.12_6.6.52_2.2.0_stable](https://github.com/TechNexion/tn_debian_flexbuild/tree/tn_v24.12_6.6.52_2.2.0_stable)|9c7cbe0dd3fc4f99009b7d60a6e71bb39edbd21a|

tag name: tn_debian-12_v2412_20250117

- [**Build Debian from source code**](https://github.com/TechNexion/tn_debian_flexbuild)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)
<br/>
---
### Supported Platforms in this Release
#### ARM64 SOM

<table border={0} overflow-x="auto" height={600} style={{ fontSize: 12 }}>
  {/* Form factor */}
  <tbody>
    <tr>
      <td style={{ backgroundColor: "#F9F801" }} align="left">
        <b>Form factor</b>
      </td>
      <td style={{ backgroundColor: "#96F8C8" }} align="center" colSpan={4}>
        <b>EDM-G</b>
      </td>
    </tr>
    {/* Product */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        <b>Product</b>
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        EDM-G-IMX8MP
      </td>
      <td style={{ backgroundColor: "#FAE197" }} align="center" colSpan={2}>
        EDM-G-IMX8MM
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        DRAM Variants
      </td>
      <td align="center" colSpan={2}>
        1/2/4/6/8 GB
      </td>
      <td align="center" colSpan={2}>
        1/2/4/8 GB
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Wireless
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
      <td align="center" colSpan={2}>
        QCA9377-3(SDIO)
        <br />
        BT 5.0
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Baseboard
      </td>
      <td align="center">EDM-G-WB</td>
      <td align="center">EDM-G-WIZARD</td>
      <td align="center">EDM-G-WB</td>
      <td align="center">EDM-G-WIZARD</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Atheros/Realtek phy
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    {/* MIPI Panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>MIPI Panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel - ILI9881C
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - g101uan02
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* LVDS panel support (Native LVDS or MIPI to LVDS) */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>LVDS panel support (Native LVDS or MIPI to LVDS)</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - vl101-12880YL-C01
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        10.1 inch panel - vl101-12880YL-C13
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        15.6 inch panel - vl15613676
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        15.6 inch panel - vl156192108
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        21.5 inch panel - vl215192108
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* HDMI */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>HDMI</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Native HDMI
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        MIPI to HDMI
        <br />
        (via ADV7535)
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Dual HDMI
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* Parallel TTL panel support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Parallel TTL panel support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        5 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        7 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        8 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 5 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 7 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VIZIONPANEL with 8 inch panel
      </td>
      <td align="center">NA</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    {/* Peripherals */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Peripherals</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        Vizionlink-HDMI
        <br />
        (HDMI input/capture)
      </td>
      <td align="center">
        Yes
        <br />
        (Dual/Single)
      </td>
      <td align="center">
        Yes
        <br />
        (Dual/Single)
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVI-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        TEVS-AR Camera
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLI-OV5640
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLI-AR Camera
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VLS3-AR Camera
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    {/* Software support */}
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left" colSpan={10}>
        <b>Software support</b>
      </td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        VizionSDK/VizionViewer
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        FreeRTOS(MCUXpresso v2.10)
      </td>
      <td align="center">Yes</td>
      <td align="center">Yes</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
    <tr>
      <td style={{ backgroundColor: "#FAE197" }} align="left">
        U-boot splash screen
      </td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
      <td align="center">NA</td>
    </tr>
  </tbody>
</table>

---
### Camera Module
- i.mx8 supports the following camera modules
   -  [TechNexion TEVS-AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
   -  [TechNexion VLS3 -AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
<br/>
---
### Prebuilt Images Available for Download

Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_debian-12-v2412_qca9377_hdmi-1920x1080_20250117.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_debian-12-v2412_qca9377_hdmi-1920x1080_20250117.zip) |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_debian-12-v2412_qca9377_lvds-1280x800_20250117.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_debian-12-v2412_qca9377_lvds-1280x800_20250117.zip) |