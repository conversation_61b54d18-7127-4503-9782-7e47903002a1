---
title: <PERSON><PERSON> 12 (bookworm) 2025Q2 Release Notes
description: Release Notes for 2025Q2 flexbuild-v24.12
sidebar_position: 49
---
## Debian 12 (flexbuild v24.12) 2025Q2 Release Notes

### Links for source repositories

|  | Branch | Commit ID  |
| :--- | --- | --- |
|U-boot| [tn-imx_v2024.04_6.6.52_2.2.0-stable](https://github.com/TechNexion/u-boot-tn-imx/tree/tn-imx_v2024.04_6.6.52_2.2.0-stable) |b76e0653558fff7a9983dadddc06d75183afc42e|
|Kernel| [tn-imx_6.6.52_2.2.0-stable](https://github.com/TechNexion/linux-tn-imx/tree/tn-imx_6.6.52_2.2.0-stable)|9dea8cf4dd1d15e6e80c73c925bac381ad6d5273|
|TN flexbuild BSP| [tn_v24.12_6.6.52_2.2.0_stable](https://github.com/TechNexion/tn_debian_flexbuild/tree/tn_v24.12_6.6.52_2.2.0_stable)|6cf2a68657301de91461125d0d12f5eeb5873af6|

tag name: tn_debian-12_v2412_20250513

- [**Build Debian from source code**](https://github.com/TechNexion/tn_debian_flexbuild)
- [**Get prebuilt Images**](#prebuilt-images-available-for-download)

---
### Supported Platforms in this Release
#### ARM64 SOM

<!--
All HTML tables converted from Doc360 must use JSX syntax.
The following website is helpful: https://transform.tools/html-to-jsx
-->

<table className="tn-table tn-table-color">
    <thead>
        <tr>
            <td>Form factor</td>
            <td colSpan={4} align="center">EDM-G</td>
            <td colSpan={2} align="center">PICO</td>
            <td colSpan={2} align="center">AXON</td>
            <td colSpan={1} align="center">EDM</td>
        </tr>
        <tr>
            <td>Product</td>
            <td colSpan={2} align="center">EDM-G-IMX8MP</td>
            <td colSpan={2} align="center">EDM-G-IMX8MM</td>
            <td colSpan={2} align="center">PICO-IMX8MM</td>
            <td colSpan={1} align="center">AXON-IMX8MP</td>
            <td colSpan={1} align="center">AXON-IMX93</td>
            <td colSpan={1} align="center">EDM-IMX93</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>DRAM Variants</td>
            <td colSpan={2}>1/2/4/6/8 GB</td>
            <td colSpan={2}>1/2/4/8 GB</td>
            <td colSpan={2}>1/2/4 GB</td>
            <td>2/4/8 GB</td>
            <td>2 GB</td>
            <td>2 GB</td>
        </tr>
        <tr>
            <td>Wireless</td>
            <td colSpan={2}>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td colSpan={2}>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td colSpan={2}>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>QCA9377-3(SDIO)<br/>BT 5.0</td>
            <td>IW416(SDIO)<br/>BT 5.2</td>
            <td>IW416(SDIO)<br/>BT 5.2</td>
        </tr>
        <tr>
            <td>Baseboard</td>
            <td>EDM-G-WB</td><td>EDM-G-WIZARD</td>
            <td>EDM-G-WB</td><td>EDM-G-WIZARD</td>
            <td>WIZARD</td><td>PI</td>
            <td>WIZARD</td>
            <td>AXON-WB</td>
            <td>EDM-WB</td>
        </tr>
        <tr>
            <td>Atheros/Realtek phy</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td>
            <td>Realtek</td>
            <td>Realtek</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>MIPI Panel support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>5 inch panel - ILI9881C</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>LVDS panel support (Native LVDS or MIPI to LVDS)</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>10.1 inch panel - vl101-12880YL-C01</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>10.1 inch panel - vl101-12880YL-C13</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>15.6 inch panel - vl15613676</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>15.6 inch panel - vl156192108</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>21.5 inch panel - vl215192108</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VIZIONPANEL with 10.1 inch panel - VL101-12880YL-C13</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VIZIONPANEL with 15.0 inch panel - VL150-10276YL-C04</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>VIZIONPANEL with 15.6 inch panel - VL156-13676YL-C03</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>HDMI</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>Native HDMI</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>MIPI to HDMI<br/>(via ADV7535)</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>Dual HDMI</td>
            <td>NA</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Parallel TTL panel support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>5/7 inch panel</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>8 inch panel</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Peripherals</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>Vizionlink-HDMI<br/>(HDMI input/capture)</td>
            <td>Yes<br/>(Dual/Single)</td><td>Yes<br/>(Dual/Single)</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes<br/>(Single)</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>TEVS-AR Camera</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>VLS3-AR Camera</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td style={{ backgroundColor: "#fae197" }}><b>Software support</b></td>
            <td colSpan={12} style={{ backgroundColor: "#fae197" }} />
        </tr>
        <tr>
            <td>VizionSDK/VizionViewer</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td><td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
            <td>Yes</td>
        </tr>
        <tr>
            <td>FreeRTOS(MCUXpresso v2.x)</td>
            <td>Yes</td><td>Yes</td>
            <td>NA</td><td>NA</td>
            <td>NA</td><td>NA</td>
            <td>NA</td>
            <td>NA</td>
            <td>NA</td>
        </tr>
        <tr>
            <td>U-boot splash screen</td>
            <td>NA</td><td>NA</td>
            <td>Yes<br/>(10" LVDS)</td><td>Yes<br/>(10" LVDS)</td>
            <td>Yes<br/>(5" MIPI)</td><td>Yes<br/>(5" MIPI)</td>
            <td>NA</td>
            <td>Parallel TTL panel</td>
            <td>Parallel TTL panel</td>
        </tr>
    </tbody>
</table>

---
### Camera Module
- i.mx8 supports the following camera modules
   -  [TechNexion TEVS-AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)
   -  [TechNexion VLS3 -AR series](https://developer.technexion.com/docs/edm-g-imx8m-tevs-camera-usage-guide)

---
### Prebuilt Images Available for Download

Prebuilt images can be available for download via TechNexion's server.

| ARM64 |  |
| --- | --- |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_debian-12-v2412_qca9377_hdmi-1920x1080_20250513.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_debian-12-v2412_qca9377_hdmi-1920x1080_20250513.zip) |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_debian-12-v2412_qca9377_lvds-1280x800_20250513.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_debian-12-v2412_qca9377_lvds-1280x800_20250513.zip) |
|AXON-IMX8MP| [axon-imx8mp_axon-wizard_debian-12-v2412_qca9377_hdmi-1920x1080_20250513.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_debian-12-v2412_qca9377_hdmi-1920x1080_20250513.zip) |
|PICO-IMX8MM| [pico-imx8mm_pico-pi-8m_debian-12-v2412_qca9377_mipi5-1280x720_20250513.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_debian-12-v2412_qca9377_mipi5-1280x720_20250513.zip) |
|AXON-IMX93| [axon-imx93_axon-wb_debian-12-v2412_iw416_lvds-1280x800_20250513.zip](https://download.technexion.com/demo_software/AXON/IMX9/axon-imx93/archived/axon-imx93_axon-wb_debian-12-v2412_iw416_lvds-1280x800_20250513.zip) |
|EDM-IMX93| [edm-imx93_edm-wb_debian-12-v2412_iw416_lvds-1280x800_20250513.zip](https://download.technexion.com/demo_software/EDM/IMX9/edm-imx93/archived/edm-imx93_edm-wb_debian-12-v2412_iw416_lvds-1280x800_20250513.zip) |
