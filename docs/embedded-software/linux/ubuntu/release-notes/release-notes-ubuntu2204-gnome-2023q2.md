---
title: Ubuntu 22.04 (Jammy Jellyfish) Gnome 2023Q2 Release Notes
description: Ubuntu 22.04 LTS Gnome Desktop Release Notes 2023Q2
sidebar_position: 3
---
## Ubuntu 22.04 LTS Gnome Desktop 2023Q2 Release Notes

### Software Specifications

| master branch |
| --- |
| U-boot version (2022.04) |
| Kernel version (5.15.71) |
| Ubuntu (Jammy Jellyfish 22.04.1 LTS ) |

| 64-bit Platform|Features |
|---|---|
| Graphic Framework | 1. Gnome-Wayland Desktop with GPU accelelation v9.0 <br/> 2. Wayland compositor v1.20.0 <br/> 3. gstreamer1.0 v1.20.1 <br/> 4. VPU libraries v6.4.3 <br/> 5. OpenGL with GPU accelelation v6.4.3|
| Security | 1. Common Vulnerabilities and Exposures (CVE) fixes using Ubuntu package management <br/> 2. sudo security alerts <br/> |
| System | 1. apt-get package manager <br/> 2. Swap parition implementation using zram <br/> 3. Device Tree Overlay |
| Wireless | 1. WiFi STA/AP/Concurrent mode <br/> 2. WiFi Direct <br/> 3. Bluetooth classic <br/> 4. Bluetooth LE  <br/> 5. ModemManager 1.20.0 for LTE/5GNR card |
|Video support format | MP4, AVI, MKV, WEBM |
|Audio support format | WAV, MP3, AAC |
- [Get the prebuilt images](#prebuilt-images-available-for-download)

---
### Supported Platforms in this Release
- Please refer to the [Supported Platforms in this Release](docs/embedded-software/linux/yocto/release-notes/release-notes-yp40-2023q2.md#supported-platforms-in-this-release) of Yocto

---
### Latest Update log
- 2023/04/21
    - Update u-boot
    - Update Linux kernel to 5.15.71
    - Add VizionSDK and VizionVIewer
    - Existing bug fixes
    - Adjust the source structure of the wifi module in Yocto

---
### Known Issue

---
### Prebuilt Images Available for Download
Prebuilt images are avialable for download via TechNexion's server.

|Product|Set|
|---|---|
|AXON-IMX8MP| [axon-imx8mp_axon-wizard_ubuntu-22.04_qca9377_hdmi-1920x1080_20230421.zip](https://download.technexion.com/demo_software/AXON/IMX8/axon-imx8mp/archived/axon-imx8mp_axon-wizard_ubuntu-22.04_qca9377_hdmi-1920x1080_20230421.zip) |
|EDM-G-IMX8MM| [edm-g-imx8mm_edm-g-wb_ubuntu-22.04_qca9377_lvds-1280x800_20230421.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_edm-g-wb_ubuntu-22.04_qca9377_lvds-1280x800_20230421.zip) |
|EDM-G-IMX8MP| [edm-g-imx8mp_edm-g-wb_ubuntu-22.04_qca9377_hdmi-1920x1080_20230421.zip](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_edm-g-wb_ubuntu-22.04_qca9377_hdmi-1920x1080_20230421.zip) |
|PICO-IMX8MM| [pico-imx8mm_pico-pi-8m_ubuntu-22.04_qca9377_mipi5-1280x720_20230421.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-8m_ubuntu-22.04_qca9377_mipi5-1280x720_20230421.zip) |
|PICO-IMX8MQ| [pico-imx8mq_pico-pi-8m_ubuntu-22.04_qca9377_mipi5-1280x720_20230421.zip](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8mq_pico-pi-8m_ubuntu-22.04_qca9377_mipi5-1280x720_20230421.zip) |
|TEK-IMX8MP| [tek3-imx8mp_ubuntu-22.04_hdmi_20230421.zip](https://download.technexion.com/demo_software/TEK/IMX8/tek-imx8mp/archived/tek3-imx8mp_ubuntu-22.04_hdmi_20230421.zip) |

