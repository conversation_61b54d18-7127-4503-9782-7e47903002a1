---
title: Ubuntu 20.04 (Focal Fossa) Xfce 2021Q2 Release Notes
description: Ubuntu 22.04 LTS Xfce Desktop Release Notes 2021Q2
sidebar_position: 6
---
## Ubuntu 20.04 LTS Release Notes

### Supported Platforms in this Release
|System-On-Module|Baseboard|DISPLAY|Accessories|
|---|---|---|---|
|EDM-G-IMX8M-PLUS|WANDBOARD|1. HDMI<br/> 2. Vl10112880 1280x800 10" LVDS panel <br/> 3. Vl215192108 1920x1080 21" LVDS 21" LVDS panel | 1. NFC <br/> 2. TEVI-OV5640 Camera <br/> 3. TC358743 HDMI Capture card |
|EDM-G-IMX8M-MINI|WANDBOARD| 1. Vl10112880 1280x800 10" LVDS panel <br/> 2. Vl215192108 1920x1080 21" LVDS panel  | 1. NFC <br/> 2. TEVI-OV5640 Camera <br/> 3. TC358743 HDMI Capture card|
|AXON-E-IMX8M-PLUS| AXON-WIZARD| HDMI | 1. TEVI-OV5640 Camera |
|PICO-IMX8M|PI-8M<br/> PICO-WIZARD|1. HDMI<br/> 2. ILI9881C 1280x720 5"  MIPI LCD <br/> 3. G080UAN01 1920x1080 8" MIPI LCD <br/> 4. G101UAN02 1920x1200 10" MIPI LCD <br/> 5. MIPI-TO-LVDS 1280x800 Vl10112880 10" LVDS panel <br/> 6. MIPI-TO-LVDS 1366x768 Vl15613676 15.6" LVDS panel <br/> 7. MIPI-TO-LVDS 1920x1080 Vl215192108 21.5" LVDS panel <br/> 8. MIPI-To-HDMI | 1. TEVI-OV5640 Camera <br/> 2. OV5645 Camera |
|PICO-IMX8M-MINI|PI-8M<br/> PICO-WIZARD| 1. ILI9881C 1280x720 5" MIPI LCD <br/> 2.  G080UAN01 1920x1080 8" MIPI LCD <br/> 3. G101UAN02 1920x1200 10" MIPI LCD <br/> 4. MIPI-TO-LVDS 1280x800 Vl10112880 10" LVDS panel  <br/> 5. MIPI-TO-LVDS 1366x768 Vl15613676 15.6" LVDS panel <br/> 6. MIPI-TO-LVDS 1920x1080 Vl215192108 21.5" LVDS panel <br/> 7. MIPI-To-HDMI | 1. TEVI-OV5640 Camera <br/> 2. OV5645 Camera |
|EDM-IMX8M| EDM-WIZARD | 1. HDMI<br/> 2. ILI9881C 1280x720 5" MIPI LCD <br/> 3. G080UAN01 1920x1080 8" MIPI LCD <br/> 4. G101UAN02 1920x1200 10" MIPI LCD <br/> 5. MIPI-TO-LVDS 1280x800 Vl10112880 10" LVDS panel <br/> 6. MIPI-TO-LVDS 1366x768 Vl15613676 15.6" LVDS panel <br/> 7. MIPI-TO-LVDS 1920x1080 Vl215192108 21.5" LVDS panel <br/> 8. MIPI-To-HDMI  | 1. TEVI-OV5640 Camera <br/> 2. OV5645 Camera |
|PICO-IMX6|PI<br/> NYMPH| 1. HDMI<br/> 2. VL050-8048NT 800x480 5" LCD| |
|PICO-IMX7|PI |1. VL050-8048NT 800x480 5" LCD | |
|PICO-IMX6ULL |PI |1. VL050-8048NT 800x480 5" LCD | |
|EDM-IMX6|FAIRY|1. HDMI<br/>2. VL050-8048NT 800x480 5" LCD|  |

### Software Specifications

| master branch |
| --- |
| U-boot version (2020.04) |
| Kernel version (5.4.70) |
| Ubuntu (Focal 20.04.1 LTS ) |

| 64-bit Platform Features | |
|---|---|
| Graphic Framework | 1. Weston Desktop with GPU accelelation v9.0 <br/> 2. Wayland compositor v1.18.0 <br/> 3. gstreamer1.0 v1.16.2 <br/> 4. VPU libraries v6.4.3 <br/> 5. OpenGL with GPU accelelation v6.4.3 <br/> 6. QT5 with GPU acceleration v5.12.8 |
| Security | 1. Common Vulnerabilities and Exposures (CVE) fixes using Ubuntu package management <br/> 2. sudo security alerts <br/> 3. Docker-CE 20.10.6|
| System | 1. apt-get package manager <br/> 2. Swap parition implementation using zram <br/> 3. Device Tree Overlay |
| Wireless | 1. WiFi STA/AP/Concurrent mode <br/> 2. WiFi Direct <br/> 3. Bluetooth classic <br/> 4. Bluetooth LE  <br/> 5. ModemManager 1.14.2 for LTE/5GNR card |
| Nerual Network | 1. OpenCV 4.4 SDK with dnn module (CPU) <br/> 2. TensorFlow-Lite 2.2.0 (NPU) |
|Video support format | MP4, AVI, MKV, WEBM |
|Audio support format | WAV, MP3, AAC |

| 32-bit Platform Features | |
|---|---|
| Graphic Framework | 1. XFCE4 Desktop with GPU accelelation v4.14 <br/> 2. X-Server v1.20.8 <br/> 3. gstreamer1.0 v1.16.2 <br/> 4. VPU libraries v6.4.3 <br/> 5. openGL with GPU accelelation v6.4.3 |
| Security | 1. Common Vulnerabilities and Exposures (CVE) fixes using Ubuntu package management <br/> 2. sudo security alerts |
| System | 1. apt-get package manager <br/> 2. Swap parition implementation using zram  |
| Wireless | 1.NetworkManager GUI for WiFi STA/AP/Concurrent mode <br/> 2. Blueman GUI for Bluetooth classic <br/> 3. NetworkManager GUI for LTE/5GNR card |
|Video support format | MP4, AVI, MKV, WEBM |
|Audio support format | WAV, MP3, AAC |

### Latest Update log
2021/05/28: Release **v20.04-lts-02**
* ARM64
    1. Improve entropy seed for boot service
    2. Fix bluetooth need initialization manually
    3. Fix system reboot when shutdown system
    4. Add TEVI-OV5640/OV5645 cameras supporting
* ARM32 will be updated soon

2021/02/22: First release **v20.04-lts-01**
First initialization for ARM64 and ARM32 platforms

### Prebuilt Images Available for Download
Prebuilt images are avialable for download via TechNexion's server.

|Product|Set|
|---|---|
|EDM-G-IMX8M-PLUS| WANDBOARD [runtime image](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mp/archived/edm-g-imx8mp_wandboard_ubuntu-20.04-weston_QCA9377_hdmi-1920x1080_20210827.zip) |
|EDM-G-IMX8M-MINI| WANDBOARD [runtime image](https://download.technexion.com/demo_software/EDM-G/IMX8/edm-g-imx8mm/archived/edm-g-imx8mm_wandboard_ubuntu-20.04-weston_QCA9377_hdmi-1920x1080_20210908.zip) |
|PICO-IMX8M| PI-8M/PICO-WIZARD [runtime image](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mq/archived/pico-imx8m_pico-pi-imx8m_ubuntu-20.04-weston_QCA9377_hdmi-1920x1080_20210528.zip) |
|PICO-IMX8M-MINI| PI-8M/PICO-WIZARD [runtime image](https://download.technexion.com/demo_software/PICO/IMX8/pico-imx8mm/archived/pico-imx8mm_pico-pi-imx8m_ubuntu-20.04-weston_QCA9377_mipi-dsi-ili9881c-1280x720_20210528.zip) |
|EDM-IMX8M| EDM-WIZARD [runtime image](https://download.technexion.com/demo_software/EDM/IMX8/edm-imx8mq/archived/edm-imx8m_edm-wizard_ubuntu-20.04-weston_QCA9377_hdmi-1920x1080_20210528.zip) |
|PICO-IMX6| 1. PI [VL050-8048NT 800x480 5" LCD runtime image](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/archived/pico-imx6_pi_ubuntu-20.04-xfce_QCA9377_5-lcd-800x480_20210122.zip) <br/> 2. NYMPH [HDMI runtime image](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6-emmc/DiskImage/pico-imx6_nymph_ubuntu-20.04-xfce_QCA9377_hdmi-1280x720_20210122.zip)|
|PICO-IMX6ULL| PI [VL050-8048NT 800x480 5" LCD runtime image](https://download.technexion.com/demo_software/PICO/IMX6/pico-imx6ul-emmc/archived/pico-imx6ull_pi_ubuntu-20.04-xfce_QCA9377_5-lcd-800x480_20210203.zip) |
|PICO-IMX7| PI [VL050-8048NT 800x480 5" LCD runtime image](https://download.technexion.com/demo_software/PICO/IMX7/pico-imx7-emmc/archived/pico-imx7d_pi_ubuntu-20.04-xfce_QCA9377_5-lcd-800x480_20211012.zip) |
|EDM-IMX6| FAIRY [HDMI runtime image](https://download.technexion.com/demo_software/EDM/IMX6/edm1-cf-imx6/archived/edm1-cf-imx6_edm1-fairy_ubuntu-20.04-xfce_QCA9377_hdmi-1280x720_20210203.zip) |