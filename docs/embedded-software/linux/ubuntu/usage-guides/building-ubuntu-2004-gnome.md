---
title: Building Ubuntu 20.04 Gnome
description:  Building Ubuntu 20.04 Gnome Desktop
sidebar_position: 4
---

## Introduction
This article provides step-by-step instructions for building a Ubntu Gnome image with TechNexion's Yocto BSP, which supports the i.MX series of processors.

### System Requirements
Building an image from source with Yocto requires a host with the following:
* 8GB RAM (more is better)
* 4 Core processor
* 200 GB storage

If less memory is used, then some additional swap space may be needed. Inadequate memory may result slow builds and random build errors.

In addition, the host must be connected to a network and have access to the Internet so that all source code and tools may be downloaded.

#### Set up build environment on host PC:

The build environment is tested under Ubuntu 18.04 and Ubuntu 20.04.

#### Install required packages:
```bash
# First update the host package list
sudo apt-get update

# Install necessary packages
sudo apt-get install gawk wget git git-core diffstat unzip texinfo gcc-multilib build-essential \
chrpath socat cpio python python3 python3-pip python3-pexpect \
python3-git python3-jinja2 libegl1-mesa pylint3 rsync bc bison \
xz-utils debianutils iputils-ping libsdl1.2-dev xterm \
language-pack-en coreutils texi2html file docbook-utils \
python-pysqlite2 help2man desktop-file-utils \
libgl1-mesa-dev libglu1-mesa-dev mercurial autoconf automake \
groff curl lzop asciidoc u-boot-tools libreoffice-writer \
sshpass ssh-askpass zip xz-utils kpartx vim screen
```
### Install the Repo Tool
There are many respositories required. Google's `repo` tool is used to manage this.
```bash
mkdir ~/bin
curl http://commondatastorage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
chmod a+x ~/bin/repo
```
### Download the BSP source
```bash
PATH=${PATH}:~/bin
mkdir tn_ubuntu
cd tn_ubuntu
```
Next, initialize the repositories based on the release you wish to build:

| Release | Command |
|---|---
|Ubuntu 20.04 Gnome base on Hardknott 3.3 | `$: repo init -u https://github.com/TechNexion/tn-imx-yocto-manifest.git -b hardknott_5.10.y-next -m imx-5.10.72-2.2.0.xml`

### Fetch the source code:
```bash
repo sync -j8
```
### Create build environment
There are several options regarding the build environment that you can use.

#### Prepare WIFI/BT firmware

You may optionally choose to add WIFI/BT firmware to the build. To do  this, add the argument "WIFI_FIRMWARE=y" in during BSP setup. Please refer to following for an explanation of other BSP initialization options.

#### Configurations for setup script

`MACHINE` is the target of build. It usually corresponds to the name of SOM or SBC.
For more information, please check the file under `“sources/meta-tn-imx-bsp/conf/machine”`.

`DISTRO` identifies the Yocto distribution and is the new way to configure for any backends.

`“-b”` specify the build directory.

`BASEBOARD` is the baseboard type.

`WIFI_MODULE` is to choose what kind of WLAN is on board. (qca/brcm/ath-pci)
<!-- :::(Internal) (Private notes)
Can we remove `brcm` from this list?
:::
 -->
`WIFI_FIRMWARE` is to choose to add WLAN firmware files in target rootfs or not. (y/all)
<!-- :::(Internal) (Private notes)
Is there a reason to NOT have WIFI_FIRMWARE included in the build? If not, then we should make WIFI_FIRMWARE=y the default option.
:::
 -->
 #### Build configurations for supported hardware

The following is a list of valid options for `MACHINE` based on the hardware.
| MACHINE | Description
|---|---
|edm-g-imx8mm |Compatible with TechNexion EDM-G-IMX8MM (i.MX8M Mini)
|edm-g-imx8mn | Compatible with TechNexion EDM-G-IMX8MN (i.MX8M Nano)
|edm-g-imx8mp |Compatible with TechNexion EDM-G-IMX8MP (i.MX8M Plus)
|pico-imx8mm |Compatible with TechNexion PICO-IMX8MM (i.MX8M Mini)
|pico-imx8mq |Compatible with TechNexion PICO-IMX8M (i.MX8M Quad)


The following is a list of valid options for `DISTRO`.
:::info
The X11 and Framebuffer distros are only supported for i.MX 6 and i.MX 7. i.MX 8 should use xwayland only.

XWayland is the default distro for all i.MX families
:::
| `DISTRO` | Description
|---|---
|imx-desktop-xwayland | Gnome desktop with Wayland graphics engine


#### `WIFI_MODULE`
The following is a list of valid options for `WIFI_MODULE`. This specifies the 'wifi_module' variable in uEnv.txt).

|`WIFI_MODULE` | Description
|---|---
|'qca', 'brcm', 'ath-pci' | Choose what kind of WLAN is on board.
#### `WIFI_FIRMWARE`
The following is a list of valid options for `WIFI_FIRMWARE`.
|`WIFI_FIRMWARE` | Description
|---|---
|'y' |'y' option depends on 'WIFI_MODULE'. If you specify 'WIFI_MODULE' as 'qca'. Then, it only add 'qca' firmware package in yocto build.
|'all' | 'all' option will add both 'qca', 'brcm' and 'ath-pci' firmware package in yocto build.
Please refer to section "Prepare WIFI/BT firmware" to ensure you already put firmware files in the right place.

#### `DISPLAY`
The following is a list of valid options for `DISPLAY`. This parameter only works on i.MX6 and i.MX8M. This specifies the `displayinfo` variable in uEnv.txt.

|`DISPLAY` | Description
|---|---
|mipi5 | On i.MX8M, MIPI-DSI 5 inch panel(with ILI9881 controller)
|hdmi |On i.MX8M, HDMI monitor (the resolution is decided by EDID)

#### `-b` : Specify the build directory name

| Usage:| |
|---|---
|   -b `<build dir>`  |  Assign the name of build directory

#### Choosing Yocto target image

The following is a list of target image options:

|Image name |Target
|---|---
|imx-image-desktop | Ubuntu Gnome desktop full image

### Example BSP Setup and Build Commands for Supported Targets
Build Yocto for TechNexion target platform.

#### For EDM-G-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=edm-g-imx8mm source tn-setup-release.sh -b build-desktop-edm-g-imx8mm

bitbake imx-image-full
```
#### For EDM-G-IMX8MN
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=edm-g-imx8mn source tn-setup-release.sh -b build-desktop-edm-g-imx8mn

bitbake fimx-image-full
```

#### For EDM-G-IMX8MP
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=edm-g-imx8mp source tn-setup-release.sh -b build-desktop-edm-g-imx8mp

bitbake imx-image-full
```

#### For PICO-IMX8MM
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=pico-imx8mm source tn-setup-release.sh -b build-desktop-pico-imx8mm

bitbake imx-image-full
```

#### For PICO-IMX8MQ
*Xwayland image:*
```bash
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=pico-imx8mq source tn-setup-release.sh -b build-desktop-pico-imx8mq

bitbake imx-image-full
```

### Image Deployment
When build completes, the generated release image is under `${BUILD-TYPE}/tmp/deploy/images/${MACHINE}`:

To decompress the .bz2:
```basg
bzip2 -fdk imx-image-desktop-XXX.rootfs.wic.bz2 "fsl-image-XXX.rootfs.wic"
```
or use `bmaptool` to flash fsl-image-XXX.rootfs.wic.bz2 directly.

To deploy the image to your board, please follow the instructions for loading software into the flash or microSD card of your development kit.

## Tutorial Video

We have put together a video showing this process, including download the image, into a PICO-IMX8M-MINI.

[![Building an Embedded Linux Distribution with Yocto Project on TechNexion Hardware](https://img.youtube.com/vi/AVkyVh9jhoA/0.jpg)](https://www.youtube.com/watch?v=AVkyVh9jhoA)
