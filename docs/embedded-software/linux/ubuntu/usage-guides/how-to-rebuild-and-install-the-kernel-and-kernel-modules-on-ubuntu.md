---
title: How to Rebuild and Install the Kernel and Kernel Modules on Ubuntu
description: How to Rebuild and Install the Kernel and Kernel Modules on Ubuntu
sidebar_position: 1
---
:::warning
This article requires an update as it pertains to older revisions of hardware and software.
:::

## Introduction
TechNexion provides Ubuntu demo images for some SOM/baseboard configurations. While these are good for initial hardware evaluation, and make installing new applications and packages easy, sometimes developers need to extend the platform by customizing the kernel and device drivers. This is a step-by-step procedure showing you how do to this.

### Applicable Platforms
- PICO-IMX6 (revision A)
- EDM1-IMX6 (revision A)
- PICO-IMX7 (revision A)

Download and install the toolchain by following the steps in this article:

[Preparing a Toolchain](/docs/embedded-software/linux/usage-guides/kernel-bootloader/preparing-a-toolchain-for-building-arm-binaries-on-x86-hosts)

Get the kernel source code from the TechNexion Linux Git repository:
```bash
$ git clone https://github.com/TechNexion/linux.git
$ cd linux
$ git checkout
```
Branches used for Ubuntu demo images:

| Ubuntu Version | Supported SOM | Kernel Branch |
|---|---|---|
|16.04| EDM1-IMX6, PICO-IMX6, PICO-IMX6UL, PICO-IMX7 revision A | tn-imx_4.1.15_2.0.0_ga |

Set up the kernel using the default TechNexion kernel configuration:

```bash
make tn_imx_defconfig
```

Then, you can select new drivers to be included as either built-in (compiled into the kernel image) or as kernel modules using the menuconfig tool:
```bash
make menuconfig
```
After finishing the selection, save your configuration and build the kernel and modules:
```bash
make zImage modules
```
Make a temp directory for the modules. The modules_install target wants a target directory as it normally installs to a specific path in the host system.
```bash
mkdir modules_temp
```

Install the modules to the directory:
```bash
make modules_install INSTALL_MOD_PATH=./modules_temp`
```
After that is done, build the device tree for your SOM. The device tree depends on the both the SOM and SOC as well as the baseboard you are using:

```bash
make
```

List of device tree names:
|SOM| Baseboard | Device Tree |
|---|---|---|
|Pico-imx6 Dual Lite or Solo | Pico-Pi |  	imx6dl-pico_pi.dtb |
|Pico-imx6 Quad 	| Pico-Pi 	|imx6q-pico_pi.dtb |
|Pico-imx7 	| Pico-Pi 	| imx7d-pico_pi.dtb |
|Pico-imx6ul |	Pico-Pi |	imx6ul-pico_pi.dtb|
|Pico-imx6ull |	Pico-Pi |	imx6ull-pico_pi.dtb|
|Pico-imx6 Dual Lite or Solo |	Pico-Nymph| 	imx6dl-pico_nymph.dtb|
|Pico-imx6 Quad |	Pico-Nymph |	imx6q-pico_nymph.dtb|
|Pico-imx7 |	Pico-Nymph | 	imx7d-pico_nymph.dtb |

For this article, we will assume we are building for Pico-imx6 Quad running on a Pico Pi, so the command is:
```bash
make imx6q-pico_pi.dtb
```
Pack up the modules into a nice .tar.gz file:
```bash
cd modules_temp/lib/modules
tar czvf modules.tar.gz *
```
Make a temporary directory on your target system:

```bash
cd ~
mkdir tmp
```

If you have the target system on your local network, you can copy the files to the target system using scp. Note that "technexion.local" represents the target name located via MDNS/Bonjour. The default username/password is ubuntu/ubuntu.
```bash
scp arch/arm/boot/zImage <EMAIL>:~/tmp
scp arch/arm/boot/dts/imx6q-pico_pi.dtb <EMAIL>:~/tmp
scp modules_temp/lib/modules/modules.tar.gz <EMAIL>:~/tmp
```
Alternatively, you can transfer the files using a USB flash drive if you don't have the target system on your local network. On your **TARGET** system: Go home:
```bash
cd
```
Make a directory to mount the boot partition (where we will replace the kernel and device tree file):
```bash
$ mkdir boot
```
Mount the partition:
```bash
sudo mount /dev/mmcblk2p1 boot
```
Copy the kernel:
```bash
cp tmp/zImage boot
```
Copy the device tree:
```bash
cp tmp/imx6q-pico_pi.dtb boot
```

Unpack the modules to the right directory:
```bash
cd /lib/modules
sudo tar -xzvf ~/tmp/modules.tar.gz .
```

Once this is all done, you should reboot the system. If everything goes well, it should come right up. If you've missed a module or two, you can always reconfigure the kernel and rebuild the modules, and transfer the modules back over.
