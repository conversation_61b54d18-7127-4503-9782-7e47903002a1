---
title: Building Ubuntu 20.04 XFCE
description: Building Ubuntu 20.04 XFCE Desktop
sidebar_position: 5
---
### Build Ubuntu Image
-----------

#### Download the source code

Github way (Prepare git command first is recommended)

Install git first:

```shell
sudo apt-get install repo
```

Note that repo already support ptthon 3.7+ only from 2020 ends, if your host OS is Ubuntu, please adapt Ubuntu 20.04, it can get newer repo base on python 3.7, or you need fix it manually.

Download source code:

```shell
git clone https://github.com/TechNexion-customization/ubuntu-tn-imx.git
```

Change to revision revision 20.04-lts-03
```shell
git checkout v20.04-lts-03
```

**About IMX8 with Weston Desktop instruction, please refer as follows:**
* [Compiling environment setup](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx8.md#compiling-environment-setup)
* [Build an runtime image](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx8.md#build-a-runtime-image)
* [Flash the image to the target board](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx8.md#flash-the-image-to-the-target-board)
* [Quick started guide](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx8.md#-quick-start)
* [App development guide](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx8.md#-apps-developing)


**About IMX6/IMX7 XFCE with XFCE Desktop instruction, please refer as follows:**
* [Compiling environment setup](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx6.md#compiling-environment-setup)
* [Build an runtime image](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx6.md#build-a-runtime-image)
* [Flash the image to the target board](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx6.md#flash-the-image-to-the-target-board)
* [Quick started guide](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx6.md#-quick-start)
* [App development guide](https://github.com/TechNexion-customization/ubuntu-tn-imx/blob/master/README-imx6.md#-apps-developing)

