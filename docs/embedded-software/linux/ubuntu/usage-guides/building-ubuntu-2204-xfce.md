---
title: Building Ubuntu 22.04 XFCE
description: Building Ubuntu 22.04 XFCE Desktop
sidebar_position: 3
---
### Overview
-----------
Technexion Ubuntu rootfs(Root Filesystem) was be generated by bash scripts, that help the
users to produce a customized ubuntu image using Cananical unique tool named `debootstrap`,
then adapt QEMU to config custom packages and tools from host PC.

**Default username:password = ubuntu:ubuntu**

**Superuser username:password = root:root**

We recommended adapt ubuntu user as normal use, and adapt root user to do deveoping and debuging because <PERSON> is running on root permission, it will not worked if you call GUI relate commands using "ubuntu user".

****
### Build Ubuntu Image
-----------

#### Download the source code

Github way (Prepare git command first is recommended)

Install git first:

```shell
sudo apt-get install repo
```

Note that repo already support ptthon 3.7+ only from 2020 ends, if your host OS is Ubuntu, please adapt Ubuntu 20.04, it can get newer repo base on python 3.7, or you need fix it manually.

Download source code:

```shell
$ git clone https://github.com/TechNexion-customization/ubuntu-tn-imx.git

Change to revision 22.04-lts-01
$ git checkout v22.04-lts-01
```

#### Compiling environment setup

General Packages Installation (Ubuntu 20.04 is recommended)

Install necessary packages
```shell
$ sudo apt-get install gawk wget git git-core diffstat unzip texinfo gcc-multilib build-essential \
    chrpath socat cpio python python3 python3-pip python3-pexpect \
    xz-utils debianutils iputils-ping libsdl1.2-dev xterm \
    language-pack-en coreutils texi2html file docbook-utils \
    python-pysqlite2 help2man desktop-file-utils \
    libgl1-mesa-dev libglu1-mesa-dev mercurial autoconf automake \
    groff curl lzop asciidoc u-boot-tools libreoffice-writer \
    sshpass ssh-askpass zip xz-utils kpartx vim screen bison flex \
    debootstrap qemu-system-arm qemu-user-static libssl-dev
```

    Install cross-compiler
```shell
sudo apt-get install gcc-arm-linux-gnueabi
```

We strongly recommended install latest qemu package if you're not use Ubuntu 20.04:

Step 1. Download deb binary file (don't choose incorrect archtecture)
https://pkgs.org/search/?q=qemu-user-static

Step 2. remove your current qemu package

Step 3. use dpkg tool to install latest deb file

****
#### Build a runtime Image

Quick way, make a runtime image directly with parameter for target platform, default is PICO-IMX8MM without platform assigning:

```shell
$ make all
(it will download the latest u-boot, kernel and rootfs and compile them automatically)
```

PICO-IMX6:
```shell
make all PLATFORM="pico-imx6"
```

PICO-IMX7:
```shell
make all PLATFORM="pico-imx7d"
```

PICO-IMX6ULL:
```shell
make all PLATFORM="pico-imx6ull"
```

EDM-IMX6:
```shell
make all PLATFORM="edm-imx6"
```

You also can compile the source code separatically, default is PICO-IMX8MM without platform assigning:

```shell
$ make u-boot (download and compile the latest u-boot only)
$ make kernel (download and compile the latest kernel only)
$ make rootfs (download and compile the latest rootfs only)
$ make image (package runtime image only, depend on above three items done)
```

Example for compile u-boot with specific platform:
```shell
make u-boot PLATFORM="<target platform>" (target platform: pico-imx6, pico-imx7, etc.)
```

Remove all compiled objects and image:

```shell
make clean
```

#### Flash the image to the target board

Output relative image files of path:

```shell
$ ls output
kernel  rootfs.tgz  u-boot  ubuntu.img
```

We provide two ways for image flashing:

**1. `uuu` way**

It's a modular flash tool base on serial download mode, download the tools from technexion website as following link:

Download: [prebuilt binary](https://download.technexion.com/development_resources/development_tools/installer/imx-mfg-uuu-tool_20220722.zip)

Step 1. Connect a Type-C cable between host PC and the board

Step 2. Change the boot mode from eMMC mode to serial download mode

Step 3. Issue uuu command to flash the compiled Ubuntu image to eMMC:

```shell
sudo uuu/linux64/uuu -b emmc_img imx6/pico-imx6-flash.bin ubuntu.img
```

Step 4. Change back boot mode from serial download mode to eMMC mode, it should be works!

For Windows OS or want to know the detail users , please click this [link](https://github.com/TechNexion/u-boot-edm/wiki/Use-mfgtool-%22uuu%22-to-flash-eMMC)

**2. `ums` way**

It's a easy way base on u-boot prompt of eMMC boot mode, but the disadvantage is the speed is a little bit lower than `uuu` way, another limitation is it must be existed a works u-boot inside the eMMC, if not, you still need adapt `uuu` way at first.

Step 1. Keep eMMC boot mode

Step 2. Connect a Type-C cable between host PC and the board for image flashing

Step 3. Connect a micro-usb cable between host PC and the board for debeg cosole

Step 4. press enter key on debug console when boot up u-boot, let u-boot into prompt

Step 5. Target board side: Issue ums command to mount eMMC as a mass storage:

```shell
> ums 0 mmc 2
```

Step 6. Host PC side: Adapt basic `dd` command is enough for image flashing:

```shell
$ sudo dd if=ubuntu.img of=/dev/sdx bs=1M (sdx means the device node which ums mounted storage)
$ sync
```

**QCA9377:** If you want to enable  QCA9377 WiFi/Bluetooth functions, please copy relate firmware files to specific path of rootfs partition as following:

WiFi:
```shell
$ sudo cp -a <firmware path>/qca9377/bdwlan30.bin mnt/lib/firmware/qca9377/
$ sudo cp -a <firmware path>/qca9377/otp30.bin mnt/lib/firmware/qca9377/
$ sudo cp -a <firmware path>/qca9377/qwlan30.bin mnt/lib/firmware/qca9377/
$ sudo cp -a <firmware path>/qca9377/utf30.bin mnt/lib/firmware/qca9377/
$ sudo cp -a <firmware path>/wlan/cfg.dat mnt/lib/firmware/wlan/cfg.dat
$ sudo cp -a <firmware path>/wlan/qca9377/qcom_cfg.ini mnt/lib/firmware/wlan/qca9377/qcom_cfg.ini
```

Bluetooth:
```shell
$ sudo cp -a <firmware path>/qca/nvm_tlv_3.2.bin mnt/lib/firmware/qca/nvm_tlv_3.2.bin
$ sudo cp -a <firmware path>/qca/rampatch_tlv_3.2.tlv mnt/lib/firmware/qca/rampatch_tlv_3.2.tlv
```

<NAME_EMAIL> to get firmware files.

****
### Quick Start
-----------

#### Run the apps using debug console/ssh

```shell
$ export DISPLAY=:0.0
$ chromium
```

#### Playback video

Adapt gstreamer-1.0 which supports avi, mp4, mkv and webm format files, please issue command to play video:

Specific audio card output
```shell
$ gst-launch-1.0 playbin uri=file:///home/<USER>/mnt/test_video/test.mp4 video-sink="imxg2dvideosink" audio-sink="alsasink device=hw:0"
```

#### `glmark` for GPU benchmark testing

```shell
$ export DISPLAY=:0.0
$ glmark2-es2
```

#### Control WiFi connection

Ubuntu adapt network-manager GUI to manage network status, so please folow steps to create wifi connection:

#### Station mode

Clicking the network manager icon of right top side.
Note that please select the hotspot under "Wi-Fi Networks (wlan0)", NOT "Wi-Fi Networks (p2p0)", or you will got a connection failed message.
![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-sta-1.png)

Then enter the password for your target hotspot, note that the "Wi-Fi adapter" need choose wlan0, it works after click "Connect" button.
You can use virtual keyboard "onboard" to keyin password if you have no keyboard as follows picture.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-sta-2.png)

Start enjoy your WiFi network.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-sta-3.png)

#### AP mode

Please choose the "Edit Connections" item first.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-ap-1.png)

Coose plus button for "add new connection".

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-ap-2.png)

Choose the "Wi-Fi" as your connection type.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-ap-3.png)

Setting the relative configuration as following picture, remember to set "Hostpot" as default mode.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-ap-4.png)

Setting the WPA password(recommended), then save the configuration and exit, and reboot the Ubuntu.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/wifi-ap-5.png)

Note that some resolution of screen may not show the save item(too small), please click the WPA password text once, and plug-in a USB keyboard, then press "TAB" key two times and press "Enter" key to save it directly.

After reboot, the hotspot already works! the user can connect it via smart phone, notebook or other devices.


### Control Bluetooth connection

Bluetooth will auto initial Bluetooth function include BLE (Bluetooth Low Energy) part which need root permission

Then you'll see a icon at the right-top side of desktop (blueman GUI), you can click it and choose relate functions what you want as follows picture, note that you have to close blueman if you want to use other tools to avoid conflictly.

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/imx6/bt-1.png)


#### 4G/5GNR mobile connection

Network-manager GUI at the right-top side of deskop can help users setting mobile connection easily, remember got right APN name first.

* Tested modules
    * mPCIE
        * FIBOCOM NL668-EU (4G)
    * M.2
        * SIMCOM SIM8202G-M2 (5GNR)
        * TELIT LN930 (4G)
        * SIERRA EM7430 (4G)

#### Expand rootfs partition

After flash the ubuntu image to eMMC, you'll see a pop-up window at first boot as following figure:

![image](https://github.com/TechNexion-customization/ubuntu-tn-imx/raw/master/figures/expand_rootfs.png)

The system will starting expand rootfs partition and wait for about 10 seconds, it will auto reboot and you can start enjoy your Ubuntu.

#### Chromium

|#|method|command|
|---|---|----
|1| click icon| icon is exist on desktop
|2| basic browser| # chromium
|3| kiosk mode (full screen)| # chromium --start-fullscreen --kiosk --fast --fast-start https://www.technexion.com

****
## Apps Developing
-----------

**Toolchain**

**1. Non-GUI/Web GUI applications**

Example: IoT, Industrial or Machine Learning

|#|Language|libraries|
|---|---|----
|1|JAVA|apt-get install openjdk11-jdk
|2|Python|apt-get install python3-pip
|2|JavaScript|apt-get install nodejs npm
|2|Golang|apt-get install golang
|2|C/C++|apt-get install gcc

The users also can compile the program on host PC side using cross-compiler if adapt non-OO programming language.

**2. GUI applications with HW acceleration**

Example: OpenGL

|#|Language|libraries|
|---|---|----
|2|C|OpenGL

We recommended developing GUI applications on host PC side, it's saving eMMC usage for develop libraries especially huge HMI system, so Technexion will provide a Graphic SDK for host PC side use soon.


****
 ### Known Limitations
-----------

1. Manual change PICO-IMX6 boot arguments in uEnv.txt using our Ubuntu BSP

   We adpat NYMPH baseboard with HDMI as dafault PICO-IMX6 configuration in Makefile, please change the relate argument if you use other baseboard.

    Modify baseboard in line 101 of gen_image.sh, for example, if your baseboard is PI, please modify it as follows:
```shell
sudo sh -c 'echo baseboard=pi > mnt/uEnv.txt'
```

    Modify resolution according to your target panel (IMX6 only), modify line 103 of gen_image.sh as follows:
```shell
sudo sh -c 'echo displayinfo=video=mxcfb0:dev=lcd,800x480@60,if=RGB24,bpp=32 >> mnt/uEnv.txt'
```

    That is a 5" LCD example.

2. Chromium cannot open successful at first time, close and reopen will works anytime.

3. In PICO-IMX6 platforms, GPU with HW acceleration on X11 has a little bit unstable when quick window on XFCE desktop frequently, don't do that if possible.

4. Docker engine was be removed temporary because of current docker-ce packages was broken from docker official site (only armhf has this issue).
