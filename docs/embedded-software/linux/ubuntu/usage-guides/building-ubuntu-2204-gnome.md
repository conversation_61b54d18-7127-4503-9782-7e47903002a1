---
title: Building Ubuntu 22.04 Gnome
description: Building Ubuntu 22.04 Gnome Desktop
sidebar_position: 2
---
## Introduction
This article provides step-by-step instructions for building a Ubntu Gnome image with TechNexion's Yocto BSP, which supports the i.MX series of processors.

### System Requirements
Building an image from source with Yocto requires a host with the following:
* 8GB RAM (more is better)
* 4 Core processor
* 200 GB storage

If less memory is used, then some additional swap space may be needed. Inadequate memory may result slow builds and random build errors.

In addition, the host must be connected to a network and have access to the Internet so that all source code and tools may be downloaded.

---
### Set up build environment on host PC
Please refer to **[TechNexion Yocto 4.0 Kirkstone 5.15.y GA BSP - Set up build environment on host PC](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/kirkstone_5.15.y-next#set-up-build-environment-on-host-pc)**

* Select initial manifest file in section [Download the BSP source](https://github.com/TechNexion/tn-imx-yocto-manifest#download-the-bsp-source)
Ex.
```shell
repo init -u https://github.com/TechNexion/tn-imx-yocto-manifest.git -b kirkstone_5.15.y-stable -m imx-5.15.71-2.2.2.xml
```

---
### Create build environment
There are several options regarding the build environment that you can use.

#### Configurations for setup script
Please refer to **[TechNexion Yocto 4.0 Kirkstone 5.15.y GA BSP - Configurations for setup script](https://github.com/TechNexion/tn-imx-yocto-manifest/tree/kirkstone_5.15.y-next#configurations-for-setup-script)**

---
### Example BSP Setup and Build Commands for Supported Targets
Build Ubuntu for TechNexion target platform.

:::info
- The environment argument DISTRO of Ubuntu is **imx-desktop-xwayland**
- The target image of Ubuntu is **imx-image-desktop**
:::

#### For EDM-G-IMX8MM
*Xwayland image:*
- Setup environment
```shell
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=edm-g-imx8mm BASEBOARD=wizard source tn-setup-release.sh -b build-desktop-edm-g-imx8mm
```
- Build image
```shell
bitbake imx-image-desktop
```
#### For EDM-G-IMX8MN
*Xwayland image:*
- Setup environment
```shell
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=edm-g-imx8mn BASEBOARD=wb source tn-setup-release.sh -b build-desktop-edm-g-imx8mn
```
- Build image
```shell
bitbake imx-image-desktop
```

#### For EDM-G-IMX8MP
*Xwayland image:*
- Setup environment
```shell
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=edm-g-imx8mp BASEBOARD=wizard source tn-setup-release.sh -b build-desktop-edm-g-imx8mp
```
- Build image
```shell
bitbake imx-image-desktop
```

#### For PICO-IMX8MM
*Xwayland image:*
- Setup environment
```shell
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=pico-imx8mm BASEBOARD=pi source tn-setup-release.sh -b build-desktop-pico-imx8mm
```
- Build image
```shell
bitbake imx-image-desktop
```

#### For PICO-IMX8MQ
*Xwayland image:*
- Setup environment
```shell
WIFI_FIRMWARE=y DISTRO=imx-desktop-xwayland MACHINE=pico-imx8mq BASEBOARD=pi source tn-setup-release.sh -b build-desktop-pico-imx8mq
```
- Build image
```shell
bitbake imx-image-desktop
```

---
### Image Deployment
- When build completes, the generated release image is under directory `<build_dir>/tmp/deploy/images/<MACHINE>`
Ex. PICO-IMX8MQ
```shell
build-desktop-pico-imx8mq/tmp/deploy/images/pico-imx8mq
```
<br/>

- To decompress the .bz2:
```shell
bzip2 -fdk imx-image-desktop-XXX.rootfs.wic.bz2 imx-image-desktop-XXX.rootfs.wic
```
- To deploy the image to your board, please follow the instructions for loading software into the flash or microSD card of your development kit.
  - Use `bmaptool` to flash imx-image-desktop-XXX.rootfs.wic.bz2 directly.
Ex. flash image of PICO-IMX8MQ to /dev/sdj
```shell
bmaptool copy --bmap imx-image-desktop-pico-imx8mq.rootfs.wic.map imx-image-desktop-pico-imx8mq.rootfs.wic.bz2
```
or
```shell
bmaptool copy --nobmap imx-image-desktop-pico-imx8mq.rootfs.wic.bz2
```
:::warning NOTE
**If bmaptool with --nobmap, it will take a long time to flash image.**
:::

---
### About Login
:::info
- account: ubuntu
- password: ubuntu
:::
- If you want to change the account or password, modify APTGET_ADD_USERS in `<build_dir>/conf/local.conf`.
:::info NOTE
The password must be generated by openssl v1.x
:::

---
## Tutorial Video

We have put together a video showing this process, including download the image, into a PICO-IMX8M-MINI.

[![Building an Embedded Linux Distribution with Yocto Project on TechNexion Hardware](https://img.youtube.com/vi/AVkyVh9jhoA/0.jpg)](https://www.youtube.com/watch?v=AVkyVh9jhoA)
